{"system_info": {"name": "نظام الذكاء الاصطناعي المتقدم والحديث", "version": "2.0.0", "description": "نظام ذكاء اصطناعي متقدم لمعالجة وتحليل الرسائل العربية والإنجليزية", "created_date": "2025-01-27", "last_updated": "2025-01-27"}, "ai_settings": {"enabled": true, "default_threshold": 0.3, "adaptive_threshold": true, "self_learning": true, "multi_language_support": true, "advanced_arabic_processing": true}, "text_processing": {"normalize_arabic_numbers": true, "remove_diacritics": true, "normalize_arabic_chars": true, "extract_entities": true, "expand_synonyms": true, "advanced_keyword_extraction": true}, "matching_algorithms": {"exact_match": {"enabled": true, "weight": 0.25, "priority": 1}, "jaccard_similarity": {"enabled": true, "weight": 0.15, "priority": 2}, "cosine_similarity": {"enabled": true, "weight": 0.15, "priority": 3}, "levenshtein_distance": {"enabled": true, "weight": 0.1, "priority": 4}, "keyword_matching": {"enabled": true, "weight": 0.2, "priority": 5}, "synonym_matching": {"enabled": true, "weight": 0.1, "priority": 6}, "entity_matching": {"enabled": true, "weight": 0.05, "priority": 7}}, "threshold_adjustments": {"by_message_type": {"greeting": -0.3, "question": -0.2, "request": -0.1, "complaint": -0.3, "thanks": -0.25, "price_inquiry": -0.2, "general": -0.2}, "by_language": {"arabic": -0.15, "english": 0.0, "mixed": -0.1}, "by_confidence": {"high_confidence": -0.1, "medium_confidence": 0.0, "low_confidence": 0.1}}, "advanced_features": {"semantic_analysis": true, "intent_detection": true, "sentiment_analysis": true, "entity_recognition": true, "context_awareness": true, "pattern_learning": true}, "arabic_language_support": {"dialectal_support": true, "advanced_normalization": true, "comprehensive_synonyms": true, "cultural_context": true, "regional_variations": true}, "performance_settings": {"cache_results": true, "parallel_processing": false, "memory_optimization": true, "response_time_target": 500, "max_concurrent_requests": 10}, "logging_and_monitoring": {"detailed_logging": true, "performance_metrics": true, "accuracy_tracking": true, "error_reporting": true, "usage_statistics": true}, "fallback_settings": {"use_regular_auto_reply": true, "fallback_threshold": 0.0, "fallback_message": "عذراً، لم أتمكن من فهم رسالتك. يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.", "enable_human_handoff": false}, "testing_and_validation": {"test_mode": false, "validation_enabled": true, "benchmark_testing": false, "a_b_testing": false, "quality_assurance": true}, "integration_settings": {"whatsapp_integration": true, "api_endpoints": true, "webhook_support": false, "external_ai_services": false, "database_integration": false}, "security_and_privacy": {"data_encryption": false, "message_anonymization": false, "audit_trail": true, "access_control": false, "data_retention_days": 30}}