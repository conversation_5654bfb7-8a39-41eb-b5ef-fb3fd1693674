/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-core/dist/image_test_util" />
import * as tf from './index';
/**
 * Returns an image used in various image related tests as a 4d tensor.
 *
 * The image is 8x8 and looks like this:
 * https://drive.google.com/file/d/1Y0AsFZ2w9HsWgJfm8f2uDOGY7A4IHjcK/view?usp=sharing
 *
 */
export declare function getTestImageAsTensor4d(): tf.Tensor4D;
