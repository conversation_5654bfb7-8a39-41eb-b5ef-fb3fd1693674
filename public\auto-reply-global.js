document.addEventListener('DOMContentLoaded', function() {
    // المتغيرات العامة
    const backBtn = document.getElementById('backBtn');
    const globalAutoReplyToggle = document.getElementById('globalAutoReplyToggle');
    const statusMessage = document.getElementById('statusMessage');
    const messageText = document.getElementById('messageText');
    const saveBtn = document.getElementById('saveBtn');
    const testBtn = document.getElementById('testBtn');
    const messagePreview = document.getElementById('messagePreview');
    
    // متغيرات الملفات
    const imageInput = document.getElementById('imageInput');
    const fileInput = document.getElementById('fileInput');
    const folderInput = document.getElementById('folderInput');
    const imagePreview = document.getElementById('imagePreview');
    const filePreview = document.getElementById('filePreview');
    const folderPreview = document.getElementById('folderPreview');
    
    // متغيرات شريط الأدوات
    const toolbar = document.getElementById('toolbar');
    const toolbarBtns = toolbar.querySelectorAll('.toolbar-btn');
    const insertNameBtn = document.getElementById('insertNameBtn');
    const emojiBtn = document.getElementById('emojiBtn');
    const emojiPicker = document.getElementById('emojiPicker');
    
    // متغيرات حاويات الملفات
    const imageContainer = document.getElementById('imageContainer');
    const fileContainer = document.getElementById('fileContainer');
    const folderContainer = document.getElementById('folderContainer');
    
    let currentMessageType = 'text';
    let selectedFiles = {
        image: null,
        file: null,
        folder: []
    };

    // تحميل الإعدادات الحالية
    loadCurrentSettings();

    // مستمعي الأحداث
    backBtn.addEventListener('click', () => {
        window.location.href = 'dashboard.html';
    });

    globalAutoReplyToggle.addEventListener('change', function() {
        updateToggleStatus();
        if (this.checked) {
            saveCurrentSettings();
        }
    });

    // مستمعي أحداث شريط الأدوات
    toolbarBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const type = this.getAttribute('data-type');
            if (type) {
                switchMessageType(type);
            }
        });
    });

    // مستمع إدراج الاسم
    insertNameBtn.addEventListener('click', function() {
        const cursorPos = messageText.selectionStart;
        const textBefore = messageText.value.substring(0, cursorPos);
        const textAfter = messageText.value.substring(cursorPos);
        messageText.value = textBefore + '{name}' + textAfter;
        messageText.focus();
        messageText.setSelectionRange(cursorPos + 6, cursorPos + 6);
        updatePreview();
    });

    // مستمع الرموز التعبيرية
    emojiBtn.addEventListener('click', function() {
        if (!emojiPicker.firstChild) {
            const picker = document.createElement('emoji-picker');
            picker.addEventListener('emoji-click', event => {
                const cursorPos = messageText.selectionStart;
                const textBefore = messageText.value.substring(0, cursorPos);
                const textAfter = messageText.value.substring(cursorPos);
                messageText.value = textBefore + event.detail.unicode + textAfter;
                messageText.focus();
                messageText.setSelectionRange(cursorPos + event.detail.unicode.length, cursorPos + event.detail.unicode.length);
                emojiPicker.classList.remove('show');
                updatePreview();
            });
            emojiPicker.appendChild(picker);
        }
        emojiPicker.classList.toggle('show');
    });

    // مستمعي أحداث الملفات
    imageInput.addEventListener('change', function() {
        handleFileSelection('image', this.files[0]);
    });

    fileInput.addEventListener('change', function() {
        handleFileSelection('file', this.files[0]);
    });

    folderInput.addEventListener('change', function() {
        handleFileSelection('folder', Array.from(this.files));
    });

    // مستمع تحديث النص
    messageText.addEventListener('input', updatePreview);

    // مستمع حفظ الإعدادات
    saveBtn.addEventListener('click', saveCurrentSettings);

    // مستمع اختبار الرسالة
    testBtn.addEventListener('click', testMessage);

    // الوظائف المساعدة
    function loadCurrentSettings() {
        fetch('/api/auto-reply/global/settings')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const settings = data.settings;
                    globalAutoReplyToggle.checked = settings.enabled || false;
                    
                    if (settings.message) {
                        if (settings.message.text) {
                            messageText.value = settings.message.text;
                            switchMessageType('text');
                        } else if (settings.message.image) {
                            switchMessageType('image');
                            // عرض معاينة الصورة المحفوظة
                        } else if (settings.message.file) {
                            switchMessageType('file');
                            // عرض معاينة الملف المحفوظ
                        } else if (settings.message.folder) {
                            switchMessageType('folder');
                            // عرض معاينة المجلد المحفوظ
                        }
                    }
                    
                    updateToggleStatus();
                    updatePreview();
                }
            })
            .catch(error => {
                console.error('Error loading settings:', error);
                Swal.fire({
                    title: 'خطأ',
                    text: 'حدث خطأ في تحميل الإعدادات',
                    icon: 'error'
                });
            });
    }

    function updateToggleStatus() {
        if (globalAutoReplyToggle.checked) {
            statusMessage.className = 'alert alert-success';
            statusMessage.innerHTML = '<i class="fas fa-check-circle me-2"></i>الرد التلقائي العام مفعل حالياً';
        } else {
            statusMessage.className = 'alert alert-info';
            statusMessage.innerHTML = '<i class="fas fa-info-circle me-2"></i>الرد التلقائي العام غير مفعل حالياً';
        }
    }

    function switchMessageType(type) {
        currentMessageType = type;
        
        // تحديث أزرار شريط الأدوات
        toolbarBtns.forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-type') === type) {
                btn.classList.add('active');
            }
        });

        // إخفاء جميع حاويات الملفات
        imageContainer.classList.remove('active');
        fileContainer.classList.remove('active');
        folderContainer.classList.remove('active');

        // إظهار الحاوية المناسبة
        switch(type) {
            case 'image':
                imageContainer.classList.add('active');
                break;
            case 'file':
                fileContainer.classList.add('active');
                break;
            case 'folder':
                folderContainer.classList.add('active');
                break;
        }

        updatePreview();
    }

    function handleFileSelection(type, files) {
        selectedFiles[type] = files;
        
        switch(type) {
            case 'image':
                if (files) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        imagePreview.innerHTML = `
                            <div class="mt-3">
                                <img src="${e.target.result}" class="preview-image" alt="معاينة الصورة">
                                <p class="text-muted mt-2">${files.name}</p>
                            </div>
                        `;
                    };
                    reader.readAsDataURL(files);
                }
                break;
            case 'file':
                if (files) {
                    filePreview.innerHTML = `
                        <div class="file-preview mt-3">
                            <i class="fas fa-file"></i>
                            <span>${files.name}</span>
                        </div>
                    `;
                }
                break;
            case 'folder':
                if (files && files.length > 0) {
                    folderPreview.innerHTML = `
                        <div class="mt-3">
                            <p><strong>تم اختيار ${files.length} ملف:</strong></p>
                            ${files.slice(0, 5).map(file => `
                                <div class="file-preview">
                                    <i class="fas fa-file"></i>
                                    <span>${file.name}</span>
                                </div>
                            `).join('')}
                            ${files.length > 5 ? `<p class="text-muted">... و ${files.length - 5} ملفات أخرى</p>` : ''}
                        </div>
                    `;
                }
                break;
        }
        
        updatePreview();
    }

    function updatePreview() {
        let previewContent = '';
        
        switch(currentMessageType) {
            case 'text':
                if (messageText.value.trim()) {
                    const previewText = messageText.value.replace(/{name}/g, 'أحمد');
                    previewContent = `<div class="preview-message">${previewText}</div>`;
                }
                break;
            case 'image':
                if (selectedFiles.image) {
                    previewContent = `
                        <img src="${URL.createObjectURL(selectedFiles.image)}" class="preview-image" alt="معاينة">
                        ${messageText.value.trim() ? `<div class="preview-message">${messageText.value.replace(/{name}/g, 'أحمد')}</div>` : ''}
                    `;
                }
                break;
            case 'file':
                if (selectedFiles.file) {
                    previewContent = `
                        <div class="file-preview">
                            <i class="fas fa-file"></i>
                            <span>${selectedFiles.file.name}</span>
                        </div>
                        ${messageText.value.trim() ? `<div class="preview-message">${messageText.value.replace(/{name}/g, 'أحمد')}</div>` : ''}
                    `;
                }
                break;
            case 'folder':
                if (selectedFiles.folder && selectedFiles.folder.length > 0) {
                    previewContent = `
                        <div class="file-preview">
                            <i class="fas fa-folder"></i>
                            <span>${selectedFiles.folder.length} ملفات</span>
                        </div>
                        ${messageText.value.trim() ? `<div class="preview-message">${messageText.value.replace(/{name}/g, 'أحمد')}</div>` : ''}
                    `;
                }
                break;
        }

        if (previewContent) {
            messagePreview.innerHTML = previewContent;
        } else {
            messagePreview.innerHTML = `
                <div class="text-muted text-center py-3">
                    <i class="fas fa-comment-dots mb-2" style="font-size: 2rem;"></i>
                    <p>ستظهر معاينة الرسالة هنا</p>
                </div>
            `;
        }
    }

    function saveCurrentSettings() {
        const formData = new FormData();
        
        const settings = {
            enabled: globalAutoReplyToggle.checked,
            messageType: currentMessageType,
            message: {}
        };

        // إضافة النص إذا كان موجوداً
        if (messageText.value.trim()) {
            settings.message.text = messageText.value.trim();
        }

        // إضافة الملفات حسب النوع
        switch(currentMessageType) {
            case 'image':
                if (selectedFiles.image) {
                    formData.append('image', selectedFiles.image);
                }
                break;
            case 'file':
                if (selectedFiles.file) {
                    formData.append('file', selectedFiles.file);
                }
                break;
            case 'folder':
                if (selectedFiles.folder && selectedFiles.folder.length > 0) {
                    selectedFiles.folder.forEach((file, index) => {
                        formData.append(`folder_${index}`, file);
                    });
                    settings.message.folderCount = selectedFiles.folder.length;
                }
                break;
        }

        formData.append('settings', JSON.stringify(settings));

        // إظهار رسالة التحميل
        Swal.fire({
            title: 'جاري الحفظ...',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        fetch('/api/auto-reply/global/save', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    title: 'تم الحفظ!',
                    text: 'تم حفظ إعدادات الرد التلقائي العام بنجاح',
                    icon: 'success'
                });
                updateToggleStatus();
            } else {
                throw new Error(data.error || 'حدث خطأ في الحفظ');
            }
        })
        .catch(error => {
            console.error('Error saving settings:', error);
            Swal.fire({
                title: 'خطأ',
                text: 'حدث خطأ في حفظ الإعدادات: ' + error.message,
                icon: 'error'
            });
        });
    }

    function testMessage() {
        if (!messageText.value.trim() && !selectedFiles[currentMessageType]) {
            Swal.fire({
                title: 'تنبيه',
                text: 'يرجى إدخال رسالة أو اختيار ملف للاختبار',
                icon: 'warning'
            });
            return;
        }

        Swal.fire({
            title: 'اختبار الرسالة',
            html: `
                <div class="text-start">
                    <p><strong>نوع الرسالة:</strong> ${getMessageTypeText(currentMessageType)}</p>
                    ${messageText.value.trim() ? `<p><strong>النص:</strong> ${messageText.value.replace(/{name}/g, 'أحمد')}</p>` : ''}
                    <p class="text-muted">هذه معاينة لكيفية ظهور الرسالة للمستلمين</p>
                </div>
            `,
            icon: 'info',
            confirmButtonText: 'موافق'
        });
    }

    function getMessageTypeText(type) {
        switch(type) {
            case 'text': return 'رسالة نصية';
            case 'image': return 'صورة';
            case 'file': return 'ملف';
            case 'folder': return 'مجلد';
            default: return 'غير محدد';
        }
    }

    // إخفاء منتقي الرموز التعبيرية عند النقر خارجه
    document.addEventListener('click', function(event) {
        if (!emojiBtn.contains(event.target) && !emojiPicker.contains(event.target)) {
            emojiPicker.classList.remove('show');
        }
    });
});
