#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات المتصفح المدمج لـ WhatsApp Node
يحتوي على جميع الإعدادات والتكوينات المطلوبة للمتصفح
"""

import os
from PyQt5.QtCore import QUrl

class BrowserConfig:
    """فئة إعدادات المتصفح"""
    
    # إعدادات الخادم
    DEFAULT_PORT = 3045
    ALLOWED_HOSTS = ['localhost', '127.0.0.1']
    
    # إعدادات الحماية
    SECURITY_ENABLED = True
    BLOCK_EXTERNAL_REQUESTS = True
    
    # المواقع المسموحة للموارد الخارجية
    ALLOWED_EXTERNAL_HOSTS = [
        'fonts.googleapis.com',
        'fonts.gstatic.com',
        'cdn.jsdelivr.net',
        'cdnjs.cloudflare.com',
        'www.gstatic.com',
        'apis.google.com',
        'identitytoolkit.googleapis.com',
        'securetoken.googleapis.com',
        'firestore.googleapis.com',
        'www.googleapis.com'
    ]
    
    # إعدادات المتصفح
    USER_AGENT = (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
        "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 WhatsAppNode/1.0"
    )
    
    # إعدادات التخزين المؤقت
    CACHE_SIZE_MB = 50
    ENABLE_PERSISTENT_COOKIES = True
    
    # إعدادات الأداء
    ENABLE_JAVASCRIPT = True
    ENABLE_PLUGINS = False
    ENABLE_IMAGES = True
    ENABLE_WEBGL = True
    
    # رسائل الحالة
    STATUS_MESSAGES = {
        'server_starting': '🔄 جاري بدء السيرفر...',
        'server_running': '✅ السيرفر يعمل - التطبيق جاهز',
        'server_stopped': '❌ السيرفر متوقف - جاري إعادة التشغيل...',
        'loading_page': '🔄 جاري تحميل الصفحة...',
        'page_loaded': '✅ تم تحميل الصفحة بنجاح',
        'page_failed': '❌ فشل في تحميل الصفحة',
        'connecting': '🔄 جاري الاتصال...',
        'connected': '✅ تم الاتصال بنجاح'
    }
    
    # أنماط CSS للحالات
    STATUS_STYLES = {
        'success': """
            QLabel {
                background-color: #25D366;
                color: white;
                padding: 8px;
                font-weight: bold;
                font-size: 14px;
            }
        """,
        'warning': """
            QLabel {
                background-color: #FFA500;
                color: white;
                padding: 8px;
                font-weight: bold;
                font-size: 14px;
            }
        """,
        'error': """
            QLabel {
                background-color: #DC3545;
                color: white;
                padding: 8px;
                font-weight: bold;
                font-size: 14px;
            }
        """,
        'info': """
            QLabel {
                background-color: #17A2B8;
                color: white;
                padding: 8px;
                font-weight: bold;
                font-size: 14px;
            }
        """
    }
    
    # كود JavaScript للحماية الأمنية
    SECURITY_JAVASCRIPT = """
    console.log('🔒 تم تفعيل الحماية الأمنية للمتصفح المدمج');
    
    // تعطيل F12 وأدوات المطور
    document.addEventListener('keydown', function(e) {
        // F12
        if (e.keyCode === 123) {
            e.preventDefault();
            console.log('🚫 F12 محظور');
            return false;
        }
        // Ctrl+Shift+I
        if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
            e.preventDefault();
            console.log('🚫 أدوات المطور محظورة');
            return false;
        }
        // Ctrl+Shift+C
        if (e.ctrlKey && e.shiftKey && e.keyCode === 67) {
            e.preventDefault();
            console.log('🚫 فحص العنصر محظور');
            return false;
        }
        // Ctrl+Shift+J
        if (e.ctrlKey && e.shiftKey && e.keyCode === 74) {
            e.preventDefault();
            console.log('🚫 وحدة التحكم محظورة');
            return false;
        }
        // Ctrl+U
        if (e.ctrlKey && e.keyCode === 85) {
            e.preventDefault();
            console.log('🚫 عرض المصدر محظور');
            return false;
        }
    });
    
    // تعطيل كليك يمين
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        console.log('🚫 كليك يمين محظور');
        return false;
    });
    
    // تحسين الأداء - تمكين التحميل السريع
    if (typeof window !== 'undefined') {
        // تحسين تحميل الصور
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                if (img.loading !== 'lazy') {
                    img.loading = 'eager';
                }
            });
        });
        
        // تحسين تحميل الخطوط
        if (document.fonts) {
            document.fonts.ready.then(function() {
                console.log('✅ تم تحميل جميع الخطوط');
            });
        }
    }
    
    console.log('✅ تم تطبيق جميع إعدادات الحماية والتحسين');
    """
    
    @classmethod
    def get_server_url(cls, port=None):
        """الحصول على رابط السيرفر"""
        port = port or cls.DEFAULT_PORT
        return f"http://localhost:{port}"
    
    @classmethod
    def is_allowed_host(cls, host):
        """فحص ما إذا كان المضيف مسموح"""
        return host in cls.ALLOWED_HOSTS
    
    @classmethod
    def is_allowed_external_host(cls, host):
        """فحص ما إذا كان المضيف الخارجي مسموح"""
        return host in cls.ALLOWED_EXTERNAL_HOSTS
    
    @classmethod
    def get_cache_size_bytes(cls):
        """الحصول على حجم التخزين المؤقت بالبايت"""
        return cls.CACHE_SIZE_MB * 1024 * 1024
    
    @classmethod
    def should_block_url(cls, url):
        """فحص ما إذا كان يجب حظر الرابط"""
        if not cls.BLOCK_EXTERNAL_REQUESTS:
            return False
        
        # السماح بالبروتوكولات الآمنة
        if url.startswith(('data:', 'blob:', 'chrome-extension:', 'qrc:', 'file:')):
            return False
        
        try:
            qurl = QUrl(url)
            host = qurl.host()
            
            # السماح بالمضيفين المحليين
            if cls.is_allowed_host(host):
                return False
            
            # السماح بالمضيفين الخارجيين المصرح بهم
            if cls.is_allowed_external_host(host):
                return False
            
            # حظر باقي الطلبات
            return True
            
        except Exception:
            # في حالة خطأ في تحليل الرابط، احظره للأمان
            return True

# إعدادات إضافية للتطوير والاختبار
class DevelopmentConfig(BrowserConfig):
    """إعدادات التطوير"""
    SECURITY_ENABLED = False
    BLOCK_EXTERNAL_REQUESTS = False

class ProductionConfig(BrowserConfig):
    """إعدادات الإنتاج"""
    SECURITY_ENABLED = True
    BLOCK_EXTERNAL_REQUESTS = True
    ENABLE_PLUGINS = False

# تحديد الإعدادات المستخدمة حسب متغير البيئة
def get_config():
    """الحصول على الإعدادات المناسبة"""
    env = os.environ.get('WHATSAPP_NODE_ENV', 'production').lower()
    
    if env == 'development':
        return DevelopmentConfig
    else:
        return ProductionConfig

# الإعدادات الافتراضية
Config = get_config()
