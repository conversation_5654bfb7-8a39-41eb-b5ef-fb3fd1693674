<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - تطبيق واتساب</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, var(--whatsapp-dark), var(--whatsapp-green));
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .logo-container {
            width: 80px;
            height: 80px;
            background-color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .logo-icon {
            font-size: 40px;
            color: var(--whatsapp-green);
        }
        
        .user-welcome {
            display: flex;
            align-items: center;
        }
        
        .main-card {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            border: none;
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--whatsapp-dark), var(--whatsapp-green));
            color: white;
            border: none;
            padding: 1rem;
        }
        
        .btn-success {
            background-color: var(--whatsapp-green);
            border-color: var(--whatsapp-green);
        }
        
        .btn-success:hover {
            background-color: var(--whatsapp-dark);
            border-color: var(--whatsapp-dark);
        }
        
        .btn-logout {
            background-color: transparent;
            border: 2px solid white;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-logout:hover {
            background-color: white;
            color: var(--whatsapp-dark);
        }
        
        .account-card {
            transition: all 0.3s ease;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 1.5rem;
            border: 1px solid #eee;
        }
        
        .account-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .account-card .card-header {
            padding: 0.75rem 1rem;
        }
        
        .account-status {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 0.5rem;
        }
        
        .status-connected {
            background-color: #4CAF50;
            box-shadow: 0 0 10px #4CAF50;
            animation: pulse 2s infinite;
        }
        
        .status-disconnected {
            background-color: #F44336;
        }
        
        .status-connecting {
            background-color: #FFC107;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
            100% {
                opacity: 1;
            }
        }
        
        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--whatsapp-green);
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <div class="dashboard-header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div class="user-welcome">
                    <div class="logo-container">
                        <i class="fab fa-whatsapp logo-icon"></i>
                    </div>
                    <div>
                        <h2 class="mb-0">مرحباً بك، المدير</h2>
                        <p class="mb-0">لوحة تحكم تطبيق واتساب</p>
                        <p class="mb-0" id="remainingDaysDisplay"></p>
                    </div>
                </div>
                <button id="logoutBtn" class="btn btn-logout">
                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="card main-card animate__animated animate__fadeIn">
                    <div class="card-header">
                        <h3 class="mb-0"><i class="fab fa-whatsapp me-2"></i>إدارة حسابات واتساب</h3>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <button id="createAccountBtn" class="btn btn-success btn-lg me-3 animate__animated animate__pulse animate__infinite animate__slower">
                                <i class="fas fa-plus-circle me-2"></i>إنشاء حساب
                            </button>
                            <button id="excelSendBtn" class="btn btn-secondary btn-lg me-3" disabled title="يجب أن يكون هناك حساب واحد متصل على الأقل لاستخدام هذه الميزة">
                                <i class="fas fa-file-excel me-2"></i>إرسال من ملف Excel
                            </button>
                            <button id="globalAutoReplyBtn" class="btn btn-info btn-lg">
                                <i class="fas fa-robot me-2"></i>الرد التلقائي العام
                            </button>
                        </div>

                        <!-- قسم خيارات التحكم في الرد التلقائي -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-cogs me-2"></i>إعدادات الرد التلقائي
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="radio" name="autoReplyMode" id="globalAutoReplyMode" value="global" checked>
                                            <label class="form-check-label" for="globalAutoReplyMode">
                                                <strong>الرد التلقائي العام</strong>
                                                <br>
                                                <small class="text-muted">تطبيق نفس الرد التلقائي على جميع الحسابات</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="radio" name="autoReplyMode" id="accountAutoReplyMode" value="account">
                                            <label class="form-check-label" for="accountAutoReplyMode">
                                                <strong>الرد التلقائي الخاص</strong>
                                                <br>
                                                <small class="text-muted">إعداد رد تلقائي مختلف لكل حساب</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <div id="autoReplyModeDescription" class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>الوضع الحالي: الرد التلقائي العام</strong>
                                        <br>
                                        في هذا الوضع، سيتم تطبيق نفس إعدادات الرد التلقائي على جميع الحسابات المتصلة. يمكنك تعديل الإعدادات من خلال زر "الرد التلقائي العام" أعلاه.
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <button id="saveAutoReplyModeBtn" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>حفظ إعدادات الوضع
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div id="accountsContainer" class="row">
                            <!-- هنا سيتم عرض بطاقات الحسابات -->
                            <div class="col-12 text-center" id="noAccountsMessage">
                                <p class="text-muted">لا توجد حسابات متاحة. قم بإنشاء حساب جديد للبدء.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- نافذة إنشاء حساب -->
    <div class="modal fade" id="createAccountModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">إنشاء حساب جديد</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="accountForm">
                        <div class="mb-3">
                            <label for="accountName" class="form-label">اسم الحساب</label>
                            <input type="text" class="form-control" id="accountName" required>
                            <div class="form-text">يجب أن يكون اسم الحساب فريدًا</div>
                        </div>
                        <div class="mb-3">
                            <label for="phoneNumber" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="phoneNumber" placeholder="مثال: ************" required>
                            <div class="form-text">أدخل رقم الهاتف مع رمز الدولة بدون + أو 00</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">طريقة الاتصال</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="connectionType" id="qrType" value="qr" checked>
                                <label class="form-check-label" for="qrType">
                                    الاتصال عن طريق مسح رمز QR
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="connectionType" id="pairingType" value="pairing">
                                <label class="form-check-label" for="pairingType">
                                    الاتصال عن طريق طلب رمز الاقتران
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" id="connectBtn">اتصال</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة عرض QR -->
    <div class="modal fade" id="qrModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">مسح رمز QR</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <p>افتح تطبيق واتساب على هاتفك وامسح رمز QR التالي:</p>
                    <div id="qrContainer" class="my-4">
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة عرض رمز الاقتران -->
    <div class="modal fade" id="pairingModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">رمز الاقتران</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <p>تم إرسال طلب رمز الاقتران إلى رقم الهاتف المدخل.</p>
                    <p>افتح تطبيق واتساب على هاتفك واتبع الخطوات التالية:</p>
                    <ol class="text-start">
                        <li>انتقل إلى الإعدادات في واتساب</li>
                        <li>اضغط على الأجهزة المرتبطة</li>
                        <li>اضغط على ربط جهاز</li>
                        <li>عندما يطلب منك مسح رمز QR، اضغط على خيار "ربط باستخدام رقم الهاتف"</li>
                        <li>أدخل رمز الاقتران المعروض أدناه</li>
                    </ol>
                    <!-- مكان عرض رمز الاقتران -->
                    <div class="mt-3 mb-3">
                        <h3 class="text-center">رمز الاقتران:</h3>
                        <div id="pairingCode" class="display-4 text-center font-weight-bold text-success my-3 pulse"></div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم إغلاق هذه النافذة تلقائيًا عند نجاح الاتصال
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>
    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/9.6.1/firebase-app.js";
        import { getFirestore, collection, query, where, getDocs } from "https://www.gstatic.com/firebasejs/9.6.1/firebase-firestore.js";

        // تمت إزالة التحقق من تسجيل الدخول هنا، حيث يتم الآن التعامل معه في الخادم
        // function checkLogin() {
        //     const loggedIn = localStorage.getItem('loggedIn');
        //     if (!loggedIn) {
        //         window.location.href = 'index.html'; // Redirect to login page
        //     }
        // }

        // تمت إزالة استدعاء checkLogin on page load
        // checkLogin();

        document.addEventListener('DOMContentLoaded', function() {
            const remainingDays = localStorage.getItem('remainingDays');
            if (remainingDays) {
                document.getElementById('remainingDaysDisplay').innerText = `الاشتراك متبقي: ${remainingDays} يوم`;
            }

            // Logout functionality
            document.getElementById('logoutBtn').addEventListener('click', function() {
                localStorage.removeItem('loggedIn'); // Clear login status
                localStorage.removeItem('remainingDays'); // Clear remaining days
                // استدعاء API لتسجيل الخروج من الجلسة على الخادم
                fetch('/api/logout', { method: 'POST' })
                    .then(() => {
                        window.location.href = 'index.html'; // Redirect to login page
                    })
                    .catch(error => console.error('Error during logout:', error));
            });
        });

        const firebaseConfig = {
            apiKey: "AIzaSyDGlKdkR5m6Uf-giCWObXp0-kStZxGfj9g",
            authDomain: "loginapp-e497d.firebaseapp.com",
            databaseURL: "https://loginapp-e497d-default-rtdb.firebaseio.com",
            projectId: "loginapp-e497d",
            storageBucket: "loginapp-e497d.firebasestorage.app",
            messagingSenderId: "809270704724",
            appId: "1:809270704724:web:e5d7804acc510c79088bc1",
            measurementId: "G-PB9YVDV8MK"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make db accessible globally for now, will refactor later
        window.db = db;
    </script>
    <script>
        // تمت إزالة التحقق من تسجيل الدخول هنا، حيث يتم الآن التعامل معه في الخادم
        // if (!localStorage.getItem('loggedIn')) {
        //     window.location.href = 'index.html';
        // }

        // تم دمج وظيفة تسجيل الخروج في السكريبت أعلاه لضمان استدعاء API
        // document.getElementById('logoutBtn').addEventListener('click', function() {
        //     localStorage.removeItem('loggedIn');
        //     window.location.href = 'index.html';
        // });
    </script>
    <script src="app.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // زر تسجيل الخروج (تم التعامل معه في السكريبت أعلاه)
            const logoutBtn = document.getElementById('logoutBtn');
            
            logoutBtn.addEventListener('click', function() {
                Swal.fire({
                    title: 'تسجيل الخروج',
                    text: 'هل أنت متأكد من رغبتك في تسجيل الخروج؟',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#25D366',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'نعم، تسجيل الخروج',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // تسجيل الخروج والعودة إلى صفحة تسجيل الدخول
                        // تم نقل منطق تسجيل الخروج إلى السكريبت type="module" أعلاه
                        // window.location.href = 'index.html';
                        // استدعاء API لتسجيل الخروج من الجلسة على الخادم
                        fetch('/api/logout', { method: 'POST' })
                            .then(() => {
                                localStorage.removeItem('loggedIn'); // Clear local storage after server logout
                                localStorage.removeItem('remainingDays');
                                window.location.href = 'index.html';
                            })
                            .catch(error => console.error('Error during logout:', error));
                    }
                });
            });
        });
    </script>
    <script>
        // كود استدعاء استعادة الاتصالات تلقائياً بعد الدخول للوحة التحكم
        document.addEventListener('DOMContentLoaded', function() {
            // إظهار رسالة انتظار
            Swal.fire({
                title: 'جاري استعادة الاتصالات...',
                text: 'يرجى الانتظار قليلاً حتى يتم تفعيل حسابات واتساب.',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            fetch('/api/restore-connections', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'تم التفعيل!',
                        text: 'تم استعادة اتصالات واتساب بنجاح.',
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });
                } else {
                    Swal.fire({
                        title: 'خطأ!',
                        text: data.message || 'حدث خطأ أثناء استعادة الاتصالات.',
                        icon: 'error',
                        confirmButtonText: 'موافق'
                    });
                }
            })
            .catch(error => {
                Swal.fire({
                    title: 'خطأ في الاتصال!',
                    text: 'تعذر الاتصال بالخادم. حاول مرة أخرى لاحقاً.',
                    icon: 'error',
                    confirmButtonText: 'موافق'
                });
            });
        });
    </script>
    <script>
        // وظيفة لمراقبة حالة الاشتراك بشكل دوري
        function startSubscriptionMonitor() {
            setInterval(async () => {
                try {
                    const response = await fetch('/api/check-subscription-status', { method: 'GET' });
                    const data = await response.json();

                    if (!data.subscriptionValid) {
                        // إذا انتهى الاشتراك، قم بتسجيل الخروج وتحويل المستخدم
                        Swal.fire({
                            title: 'انتهى الاشتراك!',
                            text: 'صلاحية حسابك قد انتهت. سيتم تسجيل خروجك.',
                            icon: 'warning',
                            allowOutsideClick: false,
                            confirmButtonText: 'موافق'
                        }).then(() => {
                            // استدعاء API لتسجيل الخروج من الجلسة على الخادم
                            fetch('/api/logout', { method: 'POST' })
                                .then(() => {
                                    localStorage.removeItem('loggedIn'); // Clear local storage
                                    localStorage.removeItem('remainingDays');
                                    window.location.href = 'index.html'; // Redirect to login page
                                })
                                .catch(error => console.error('Error during logout:', error));
                        });
                    }
                } catch (error) {
                    console.error('Error checking subscription status:', error);
                    // في حالة وجود خطأ في الاتصال، يمكن التعامل معه هنا (مثلاً، رسالة للمستخدم أو محاولة لاحقاً)
                }
            }, 60000); // التحقق كل 60 ثانية (دقيقة واحدة)
        }

        // ابدأ مراقبة الاشتراك عند تحميل الصفحة
        startSubscriptionMonitor();
    </script>
</body>
</html>