document.addEventListener('DOMContentLoaded', function() {
    // المتغيرات العامة
    let excelData = null;
    let selectedAccount = null;
    let selectedAccounts = []; // مصفوفة للحسابات المتعددة المختارة
    let accountSelectionMode = 'single'; // وضع اختيار الحساب: 'single' أو 'multi'
    let columnMapping = {
        phone: null,
        name: null,
        message: null
    };

    // العناصر
    const backBtn = document.getElementById('backBtn');
    const accountSelect = document.getElementById('accountSelect');
    const accountCheckboxes = document.getElementById('accountCheckboxes');
    const singleAccountMode = document.getElementById('singleAccountMode');
    const multiAccountMode = document.getElementById('multiAccountMode');
    const singleAccountContainer = document.getElementById('singleAccountContainer');
    const multiAccountContainer = document.getElementById('multiAccountContainer');
    const uploadArea = document.getElementById('uploadArea');
    const excelFile = document.getElementById('excelFile');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const removeFile = document.getElementById('removeFile');
    const columnStep = document.getElementById('columnStep');
    const phoneColumnSelect = document.getElementById('phoneColumnSelect');
    const nameColumnSelect = document.getElementById('nameColumnSelect');
    const messageColumnSelect = document.getElementById('messageColumnSelect');
    const previewBtn = document.getElementById('previewBtn');
    const previewStep = document.getElementById('previewStep');
    const previewContent = document.getElementById('previewContent');
    const sendBtn = document.getElementById('sendBtn');

    // تحميل الحسابات المتصلة
    loadConnectedAccounts();

    // مستمعي الأحداث
    backBtn.addEventListener('click', () => {
        window.location.href = 'dashboard.html';
    });

    // مستمعي أحداث اختيار وضع الحساب
    singleAccountMode.addEventListener('change', function() {
        if (this.checked) {
            accountSelectionMode = 'single';
            singleAccountContainer.style.display = 'block';
            multiAccountContainer.style.display = 'none';
            checkStepsCompletion();
        }
    });

    multiAccountMode.addEventListener('change', function() {
        if (this.checked) {
            accountSelectionMode = 'multi';
            singleAccountContainer.style.display = 'none';
            multiAccountContainer.style.display = 'block';
            checkStepsCompletion();
        }
    });

    accountSelect.addEventListener('change', function() {
        selectedAccount = this.value;
        checkStepsCompletion();
    });

    uploadArea.addEventListener('click', () => {
        excelFile.click();
    });

    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });

    excelFile.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });

    removeFile.addEventListener('click', () => {
        resetFileUpload();
    });

    // مستمعي أحداث اختيار الأعمدة
    phoneColumnSelect.addEventListener('change', updateColumnMapping);
    nameColumnSelect.addEventListener('change', updateColumnMapping);
    messageColumnSelect.addEventListener('change', updateColumnMapping);

    previewBtn.addEventListener('click', generatePreview);
    sendBtn.addEventListener('click', sendMessages);

    // وظيفة تحميل الحسابات المتصلة
    async function loadConnectedAccounts() {
        try {
            const response = await fetch('/api/available-accounts');
            const data = await response.json();
            
            // تفريغ القوائم
            accountSelect.innerHTML = '<option value="">اختر الحساب...</option>';
            accountCheckboxes.innerHTML = '';
            
            if (data.accounts && data.accounts.length > 0) {
                // فلترة الحسابات المتصلة فقط
                const connectedAccounts = data.accounts.filter(account => 
                    account.connected && (account.status === 'connected' || account.status === 'authenticated')
                );
                
                if (connectedAccounts.length > 0) {
                    // إضافة الحسابات إلى القائمة المنسدلة
                    connectedAccounts.forEach(account => {
                        // إضافة إلى القائمة المنسدلة
                        const option = document.createElement('option');
                        option.value = account.accountName;
                        option.textContent = `${account.accountName} - ${account.info?.name || 'غير معروف'}`;
                        accountSelect.appendChild(option);
                        
                        // إضافة إلى قائمة الاختيارات المتعددة
                        const checkboxDiv = document.createElement('div');
                        checkboxDiv.className = 'form-check mb-2';
                        
                        const checkbox = document.createElement('input');
                        checkbox.type = 'checkbox';
                        checkbox.className = 'form-check-input account-checkbox';
                        checkbox.id = `account-${account.accountName}`;
                        checkbox.value = account.accountName;
                        checkbox.addEventListener('change', updateSelectedAccounts);
                        
                        const label = document.createElement('label');
                        label.className = 'form-check-label';
                        label.htmlFor = `account-${account.accountName}`;
                        label.textContent = `${account.accountName} - ${account.info?.name || 'غير معروف'}`;
                        
                        checkboxDiv.appendChild(checkbox);
                        checkboxDiv.appendChild(label);
                        accountCheckboxes.appendChild(checkboxDiv);
                    });
                } else {
                    showNoAccountsError();
                }
            } else {
                showNoAccountsError();
            }
        } catch (error) {
            console.error('خطأ في تحميل الحسابات:', error);
            showNoAccountsError('حدث خطأ أثناء تحميل الحسابات');
        }
    }
    
    // وظيفة تحديث الحسابات المختارة
    function updateSelectedAccounts() {
        selectedAccounts = [];
        document.querySelectorAll('.account-checkbox:checked').forEach(checkbox => {
            selectedAccounts.push(checkbox.value);
        });
        checkStepsCompletion();
    }
    
    // وظيفة عرض خطأ عدم وجود حسابات
    function showNoAccountsError(message = 'لا توجد حسابات متصلة. يرجى إنشاء حساب والاتصال به أولاً.') {
        accountSelect.innerHTML = '<option value="">لا توجد حسابات متصلة</option>';
        accountCheckboxes.innerHTML = '<div class="alert alert-warning">لا توجد حسابات متصلة</div>';
        
        Swal.fire({
            title: 'تنبيه',
            text: message,
            icon: 'warning',
            confirmButtonText: 'العودة للرئيسية',
            confirmButtonColor: '#25D366'
        }).then(() => {
            window.location.href = 'dashboard.html';
        });
    }

    // وظيفة معالجة اختيار الملف
    function handleFileSelect(file) {
        if (!file) return;

        // التحقق من نوع الملف
        const validTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];
        
        if (!validTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
            Swal.fire({
                title: 'نوع ملف غير صحيح',
                text: 'يرجى اختيار ملف Excel (.xlsx أو .xls)',
                icon: 'error',
                confirmButtonText: 'حسناً'
            });
            return;
        }

        // عرض معلومات الملف
        fileName.textContent = `${file.name} (${formatFileSize(file.size)})`;
        fileInfo.style.display = 'block';

        // قراءة الملف
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                
                // أخذ أول ورقة عمل
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];
                
                // تحويل إلى JSON
                excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                
                if (excelData.length === 0) {
                    throw new Error('الملف فارغ');
                }

                // إعداد خيارات الأعمدة
                setupColumnOptions();
                columnStep.style.display = 'block';
                
                Swal.fire({
                    title: 'تم تحميل الملف بنجاح',
                    text: `تم العثور على ${excelData.length} صف`,
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });

            } catch (error) {
                console.error('خطأ في قراءة الملف:', error);
                Swal.fire({
                    title: 'خطأ في قراءة الملف',
                    text: 'تأكد من أن الملف صحيح وغير تالف',
                    icon: 'error',
                    confirmButtonText: 'حسناً'
                });
                resetFileUpload();
            }
        };
        
        reader.readAsArrayBuffer(file);
    }

    // وظيفة إعداد خيارات الأعمدة
    function setupColumnOptions() {
        if (!excelData || excelData.length === 0) return;

        const headers = excelData[0]; // الصف الأول كعناوين
        const selects = [phoneColumnSelect, nameColumnSelect, messageColumnSelect];

        selects.forEach(select => {
            select.innerHTML = '<option value="">اختر العمود...</option>';
            headers.forEach((header, index) => {
                const option = document.createElement('option');
                option.value = index;
                option.textContent = `${header} (العمود ${index + 1})`;
                select.appendChild(option);
            });
        });
    }

    // وظيفة تحديث تعيين الأعمدة
    function updateColumnMapping() {
        columnMapping.phone = phoneColumnSelect.value ? parseInt(phoneColumnSelect.value) : null;
        columnMapping.name = nameColumnSelect.value ? parseInt(nameColumnSelect.value) : null;
        columnMapping.message = messageColumnSelect.value ? parseInt(messageColumnSelect.value) : null;

        // تفعيل زر المعاينة إذا تم اختيار عمود الهاتف على الأقل
        previewBtn.disabled = columnMapping.phone === null;
        
        checkStepsCompletion();
    }

    // وظيفة التحقق من اكتمال الخطوات
    function checkStepsCompletion() {
        let accountsSelected = false;
        
        if (accountSelectionMode === 'single') {
            accountsSelected = selectedAccount && selectedAccount !== '';
        } else { // multi mode
            accountsSelected = selectedAccounts.length > 0;
        }
        
        const fileUploaded = excelData !== null;
        const phoneColumnSelected = columnMapping.phone !== null;
        
        // تفعيل زر الإرسال إذا تم اكتمال جميع الخطوات المطلوبة
        sendBtn.disabled = !(accountsSelected && fileUploaded && phoneColumnSelected);
    }

    // وظيفة إنشاء المعاينة
    function generatePreview() {
        if (!excelData || columnMapping.phone === null) return;

        const headers = excelData[0];
        const dataRows = excelData.slice(1); // تجاهل الصف الأول (العناوين)
        
        // فلترة الصفوف التي تحتوي على رقم هاتف
        const validRows = dataRows.filter(row => {
            const phone = row[columnMapping.phone];
            return phone && phone.toString().trim() !== '';
        });

        if (validRows.length === 0) {
            Swal.fire({
                title: 'لا توجد بيانات صالحة',
                text: 'لم يتم العثور على أي أرقام هواتف صالحة في العمود المحدد',
                icon: 'warning',
                confirmButtonText: 'حسناً'
            });
            return;
        }

        // إنشاء جدول المعاينة
        let tableHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                تم العثور على ${validRows.length} مستلم صالح من أصل ${dataRows.length} صف
            </div>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>#</th>
                            <th>رقم الهاتف</th>
                            ${columnMapping.name !== null ? '<th>الاسم</th>' : ''}
                            ${columnMapping.message !== null ? '<th>الرسالة</th>' : ''}
                        </tr>
                    </thead>
                    <tbody>
        `;

        validRows.slice(0, 10).forEach((row, index) => {
            const phone = row[columnMapping.phone] || '';
            const name = columnMapping.name !== null ? (row[columnMapping.name] || 'غير محدد') : '';
            const message = columnMapping.message !== null ? (row[columnMapping.message] || 'غير محدد') : '';
            
            tableHTML += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${phone}</td>
                    ${columnMapping.name !== null ? `<td>${name}</td>` : ''}
                    ${columnMapping.message !== null ? `<td>${message.length > 50 ? message.substring(0, 50) + '...' : message}</td>` : ''}
                </tr>
            `;
        });

        if (validRows.length > 10) {
            tableHTML += `
                <tr>
                    <td colspan="${3 + (columnMapping.name !== null ? 1 : 0) + (columnMapping.message !== null ? 1 : 0)}" class="text-center text-muted">
                        ... و ${validRows.length - 10} مستلم آخر
                    </td>
                </tr>
            `;
        }

        tableHTML += `
                    </tbody>
                </table>
            </div>
        `;

        // إضافة معلومات عن وضع إرسال الحساب
        let accountInfoHTML = '';
        if (accountSelectionMode === 'single') {
            const selectedOption = accountSelect.options[accountSelect.selectedIndex];
            accountInfoHTML = `
                <div class="alert alert-success mt-3">
                    <i class="fas fa-user-circle me-2"></i>
                    سيتم الإرسال باستخدام الحساب: <strong>${selectedOption.textContent}</strong>
                </div>
            `;
        } else { // multi mode
            const accountNames = selectedAccounts.map(accountName => {
                const checkbox = document.getElementById(`account-${accountName}`);
                const label = document.querySelector(`label[for="account-${accountName}"]`);
                return label ? label.textContent : accountName;
            }).join('، ');
            
            accountInfoHTML = `
                <div class="alert alert-success mt-3">
                    <i class="fas fa-random me-2"></i>
                    سيتم الإرسال بالتبديل العشوائي بين الحسابات: <strong>${accountNames}</strong>
                </div>
            `;
        }
        
        tableHTML += accountInfoHTML;

        previewContent.innerHTML = tableHTML;
        previewStep.style.display = 'block';
        previewStep.scrollIntoView({ behavior: 'smooth' });
        
        checkStepsCompletion();
    }

    // وظيفة إرسال الرسائل
    async function sendMessages() {
        let accountsToUse = [];
        
        if (accountSelectionMode === 'single') {
            if (!selectedAccount || selectedAccount === '') {
                Swal.fire({
                    title: 'لم يتم اختيار حساب',
                    text: 'يرجى اختيار حساب للإرسال',
                    icon: 'warning',
                    confirmButtonText: 'حسناً'
                });
                return;
            }
            accountsToUse = [selectedAccount];
        } else { // multi mode
            if (selectedAccounts.length === 0) {
                Swal.fire({
                    title: 'لم يتم اختيار حسابات',
                    text: 'يرجى اختيار حساب واحد على الأقل للإرسال',
                    icon: 'warning',
                    confirmButtonText: 'حسناً'
                });
                return;
            }
            
            // خلط الحسابات المختارة عشوائيًا
            accountsToUse = [...selectedAccounts].sort(() => Math.random() - 0.5);
        }
        
        if (!excelData || columnMapping.phone === null) {
            Swal.fire({
                title: 'بيانات ناقصة',
                text: 'يرجى التأكد من رفع الملف واختيار عمود الهاتف',
                icon: 'warning',
                confirmButtonText: 'حسناً'
            });
            return;
        }

        // تحضير بيانات المستلمين
        const dataRows = excelData.slice(1);
        const recipients = [];

        dataRows.forEach((row, index) => {
            const phone = row[columnMapping.phone];
            if (phone && phone.toString().trim() !== '') {
                const recipient = {
                    id: phone.toString().trim() + '@c.us',
                    name: columnMapping.name !== null ? (row[columnMapping.name] || `مستلم ${index + 1}`) : `مستلم ${index + 1}`,
                    number: phone.toString().trim(),
                    type: 'contact'
                };

                // إضافة الرسالة المخصصة إذا كانت متوفرة
                if (columnMapping.message !== null && row[columnMapping.message]) {
                    recipient.customMessage = row[columnMapping.message].toString();
                }

                recipients.push(recipient);
            }
        });

        if (recipients.length === 0) {
            Swal.fire({
                title: 'لا توجد مستلمين',
                text: 'لم يتم العثور على أي أرقام هواتف صالحة',
                icon: 'warning',
                confirmButtonText: 'حسناً'
            });
            return;
        }

        // تأكيد الإرسال
        let confirmMessage = `هل تريد إرسال الرسائل إلى ${recipients.length} مستلم؟`;
        if (accountSelectionMode === 'multi') {
            confirmMessage += `\nسيتم التبديل العشوائي بين ${accountsToUse.length} حساب.`;
        }
        
        const result = await Swal.fire({
            title: 'تأكيد الإرسال',
            text: confirmMessage,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم، أرسل',
            cancelButtonText: 'إلغاء',
            confirmButtonColor: '#25D366',
            cancelButtonColor: '#d33'
        });

        if (!result.isConfirmed) return;

        // تخزين المستلمين والحسابات في localStorage
        localStorage.setItem('selectedRecipients', JSON.stringify(recipients));
        localStorage.setItem('accountsToUse', JSON.stringify(accountsToUse));
        localStorage.setItem('accountSelectionMode', accountSelectionMode);
        
        // الانتقال إلى صفحة إرسال الرسائل
        if (accountSelectionMode === 'single') {
            window.location.href = `/send-message-excel.html?account=${encodeURIComponent(selectedAccount)}&mode=single`;
        } else {
            window.location.href = `/send-message-excel.html?mode=random`;
        }
    }

    // وظائف مساعدة
    function resetFileUpload() {
        excelFile.value = '';
        fileInfo.style.display = 'none';
        columnStep.style.display = 'none';
        previewStep.style.display = 'none';
        excelData = null;
        columnMapping = { phone: null, name: null, message: null };
        checkStepsCompletion();
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
});