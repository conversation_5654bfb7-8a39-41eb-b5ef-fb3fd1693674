<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الرد التلقائي للحساب</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 0;
            margin-bottom: 30px;
        }

        .header h1 {
            color: white;
            margin: 0;
            font-weight: 600;
        }

        .header p {
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
        }

        .container {
            max-width: 1000px;
        }

        .back-btn {
            margin-bottom: 20px;
            border-radius: 25px;
            padding: 10px 20px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            margin-bottom: 20px;
        }

        .card-header {
            background: linear-gradient(135deg, #25D366, #128C7E);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
            padding: 20px;
        }

        .account-info {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .account-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #25D366;
        }

        .account-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .status-connected {
            background-color: #d4edda;
            color: #155724;
        }

        .status-disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-toggle {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: white;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #25D366;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .message-section {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .message-section.active {
            border-color: #25D366;
            background-color: #f8fff9;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }

        .section-header h6 {
            margin: 0;
            color: #495057;
            font-weight: 600;
        }

        .form-check-input:checked {
            background-color: #25D366;
            border-color: #25D366;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #25D366;
            box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.25);
        }

        .custom-file-input {
            display: block;
            width: 100%;
            padding: 20px;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .custom-file-input:hover {
            border-color: #25D366;
            background: #f0fff4;
        }

        .custom-file-input i {
            font-size: 2rem;
            color: #6c757d;
            margin-bottom: 10px;
        }

        .preview-container {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            display: none;
        }

        .preview-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            background: white;
            border-radius: 8px;
            margin-bottom: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .preview-item:last-child {
            margin-bottom: 0;
        }

        .preview-image {
            max-width: 100px;
            max-height: 100px;
            border-radius: 8px;
            object-fit: cover;
        }

        .file-info {
            display: flex;
            align-items: center;
            flex-grow: 1;
        }

        .file-info i {
            font-size: 1.5rem;
            color: #6c757d;
            margin-left: 10px;
        }

        .btn-whatsapp {
            background: linear-gradient(135deg, #25D366, #128C7E);
            border: none;
            color: white;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-whatsapp:hover {
            background: linear-gradient(135deg, #128C7E, #25D366);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(37, 211, 102, 0.4);
        }

        .btn-outline-danger:hover {
            transform: translateY(-1px);
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 15px 20px;
        }

        .toolbar-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .toolbar-btn {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 8px 12px;
            margin: 0 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toolbar-btn:hover {
            background: #25D366;
            color: white;
            border-color: #25D366;
        }

        .emoji-picker {
            position: absolute;
            top: 100%;
            right: 0;
            z-index: 1000;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            display: none;
        }

        .emoji-picker.show {
            display: block;
        }

        .folder-files-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 10px;
            background: white;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px;
            border-bottom: 1px solid #f8f9fa;
            margin-bottom: 5px;
        }

        .file-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .file-item i {
            color: #6c757d;
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><i class="fas fa-user-robot me-2"></i>الرد التلقائي للحساب</h1>
            <p class="mb-0">إعداد الرد التلقائي المتقدم - يمكنك الرد بنص وصورة وملفات معاً</p>
        </div>
    </div>

    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <button id="backBtn" class="btn btn-secondary back-btn">
                <i class="fas fa-arrow-right me-2"></i>العودة لتفاصيل الحساب
            </button>
            <button id="advancedModeBtn" class="btn btn-outline-primary">
                <i class="fas fa-robot me-2"></i>الرد التلقائي المتقدم
            </button>
        </div>

        <!-- معلومات الحساب -->
        <div class="account-info">
            <div class="d-flex align-items-center">
                <img id="accountAvatar" src="" alt="صورة الحساب" class="account-avatar me-3">
                <div class="flex-grow-1">
                    <h4 id="accountName" class="mb-1">اسم الحساب</h4>
                    <p id="accountPhone" class="text-muted mb-1">رقم الهاتف</p>
                    <span id="accountStatus" class="account-status">حالة الاتصال</span>
                </div>
            </div>
        </div>

        <!-- قسم حالة الرد التلقائي -->
        <div class="status-toggle">
            <div>
                <h4 class="mb-1">حالة الرد التلقائي للحساب</h4>
                <p class="text-muted mb-0">تفعيل أو إلغاء تفعيل الرد التلقائي لهذا الحساب فقط</p>
            </div>
            <label class="switch">
                <input type="checkbox" id="accountAutoReplyToggle">
                <span class="slider"></span>
            </label>
        </div>

        <div id="statusMessage" class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>الرد التلقائي للحساب غير مفعل حالياً
        </div>

        <!-- قسم إعداد الرسائل -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-comment-dots me-2"></i>إعداد رسالة الرد التلقائي
                </h5>
                <small>يمكنك تفعيل عدة أنواع من الرسائل معاً</small>
            </div>
            <div class="card-body">
                <!-- قسم النص -->
                <div class="message-section" id="textSection">
                    <div class="section-header">
                        <h6><i class="fas fa-font me-2"></i>الرسالة النصية</h6>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableText" checked>
                            <label class="form-check-label" for="enableText">تفعيل النص</label>
                        </div>
                    </div>
                    <div class="text-content">
                        <div class="toolbar-section">
                            <button type="button" class="toolbar-btn" id="insertNameBtn" title="إدراج اسم المرسل">
                                <i class="fas fa-user me-1"></i>اسم المرسل
                            </button>
                            <button type="button" class="toolbar-btn" id="emojiBtn" title="إضافة رموز تعبيرية">
                                <i class="fas fa-smile me-1"></i>رموز تعبيرية
                            </button>
                        </div>
                        <div class="position-relative">
                            <textarea id="messageText" class="form-control" rows="4"
                                placeholder="اكتب رسالة الرد التلقائي هنا...&#10;&#10;يمكنك استخدام {name} لإدراج اسم المرسل تلقائياً"></textarea>
                            <div class="emoji-picker" id="emojiPicker"></div>
                        </div>
                    </div>
                </div>

                <!-- قسم الصورة -->
                <div class="message-section" id="imageSection">
                    <div class="section-header">
                        <h6><i class="fas fa-image me-2"></i>الصورة</h6>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableImage">
                            <label class="form-check-label" for="enableImage">تفعيل الصورة</label>
                        </div>
                    </div>
                    <div class="image-content">
                        <label class="custom-file-input" for="imageInput">
                            <i class="fas fa-image d-block"></i>
                            <span>اختر صورة للرد التلقائي</span>
                            <input type="file" id="imageInput" accept="image/*" style="display: none;">
                        </label>
                        <div id="imagePreview" class="preview-container"></div>
                    </div>
                </div>

                <!-- قسم الملف -->
                <div class="message-section" id="fileSection">
                    <div class="section-header">
                        <h6><i class="fas fa-file me-2"></i>الملف</h6>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableFile">
                            <label class="form-check-label" for="enableFile">تفعيل الملف</label>
                        </div>
                    </div>
                    <div class="file-content">
                        <label class="custom-file-input" for="fileInput">
                            <i class="fas fa-file d-block"></i>
                            <span>اختر ملف للرد التلقائي</span>
                            <input type="file" id="fileInput" style="display: none;">
                        </label>
                        <div id="filePreview" class="preview-container"></div>
                    </div>
                </div>

                <!-- قسم المجلد -->
                <div class="message-section" id="folderSection">
                    <div class="section-header">
                        <h6><i class="fas fa-folder me-2"></i>ملفات متعددة</h6>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableFolder">
                            <label class="form-check-label" for="enableFolder">تفعيل الملفات المتعددة</label>
                        </div>
                    </div>
                    <div class="folder-content">
                        <label class="custom-file-input" for="folderInput">
                            <i class="fas fa-folder d-block"></i>
                            <span>اختر ملفات متعددة للرد التلقائي</span>
                            <input type="file" id="folderInput" multiple style="display: none;">
                        </label>
                        <div id="folderPreview" class="preview-container"></div>
                    </div>
                </div>

                <!-- معاينة الرسالة الكاملة -->
                <div class="card mt-4" id="fullPreview" style="display: none;">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-eye me-2"></i>معاينة الرسالة الكاملة</h6>
                    </div>
                    <div class="card-body" id="previewContent">
                        <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="text-center mt-4">
                    <button id="saveBtn" class="btn btn-whatsapp btn-lg me-3">
                        <i class="fas fa-save me-2"></i>حفظ إعدادات الرد التلقائي
                    </button>
                    <button id="testBtn" class="btn btn-outline-primary btn-lg me-3">
                        <i class="fas fa-vial me-2"></i>اختبار الرسالة
                    </button>
                    <button onclick="deleteAllSettings()" class="btn btn-outline-danger btn-lg">
                        <i class="fas fa-trash-alt me-2"></i>حذف جميع الإعدادات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script type="module" src="https://cdn.jsdelivr.net/npm/emoji-picker-element@^1/index.js"></script>
    <script src="auto-reply-account-new.js"></script>
</body>
</html>
