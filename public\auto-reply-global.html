<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الرد التلقائي العام - واتساب</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.12/dist/sweetalert2.min.css">
    <!-- Google Fonts - <PERSON><PERSON>wal -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap">
    <!-- Emoji Picker Element -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/emoji-picker-element@1.18.3/css/emoji-picker-element.css">
    <style>
        body {
            background-color: #f0f2f5;
            font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .header {
            background-color: #128C7E;
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .auto-reply-form {
            background-color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .btn-whatsapp {
            background-color: #25D366;
            border-color: #25D366;
            color: white;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-whatsapp:hover {
            background-color: #128C7E;
            border-color: #128C7E;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(37, 211, 102, 0.3);
        }

        .back-btn {
            margin-bottom: 20px;
            border-radius: 25px;
            padding: 8px 20px;
        }

        .toolbar {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
        }

        .toolbar-btn {
            background: none;
            border: none;
            padding: 8px 12px;
            margin: 0 5px;
            border-radius: 8px;
            color: #6c757d;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .toolbar-btn:hover {
            background-color: #e9ecef;
            color: #495057;
        }

        .toolbar-btn.active {
            background-color: #25D366;
            color: white;
        }

        .message-input-container {
            position: relative;
        }

        #messageText {
            min-height: 120px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 15px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        #messageText:focus {
            border-color: #25D366;
            box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.25);
        }

        .file-input-container {
            display: none;
            margin-top: 15px;
        }

        .file-input-container.active {
            display: block;
        }

        .custom-file-input {
            border: 2px dashed #25D366;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            background-color: #f8fff9;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .custom-file-input:hover {
            background-color: #e8f5e8;
            border-color: #128C7E;
        }

        .custom-file-input i {
            font-size: 2rem;
            color: #25D366;
            margin-bottom: 10px;
        }

        .emoji-picker {
            position: absolute;
            bottom: 100%;
            right: 0;
            z-index: 1000;
            display: none;
        }

        .emoji-picker.show {
            display: block;
        }

        .status-section {
            background-color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .status-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #25D366;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .preview-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border: 1px solid #e9ecef;
        }

        .preview-message {
            background-color: #25D366;
            color: white;
            padding: 10px 15px;
            border-radius: 18px 18px 5px 18px;
            max-width: 70%;
            margin-left: auto;
            margin-bottom: 10px;
            word-wrap: break-word;
        }

        .preview-image {
            max-width: 200px;
            border-radius: 10px;
            margin-bottom: 10px;
        }

        .file-preview {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .file-preview i {
            margin-left: 10px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><i class="fas fa-robot me-2"></i>الرد التلقائي العام</h1>
            <p class="mb-0">إعداد الرد التلقائي لجميع الحسابات</p>
        </div>
    </div>

    <div class="container">
        <button id="backBtn" class="btn btn-secondary back-btn">
            <i class="fas fa-arrow-right me-2"></i>العودة للوحة التحكم
        </button>

        <!-- قسم حالة الرد التلقائي -->
        <div class="status-section">
            <div class="status-toggle">
                <div>
                    <h4 class="mb-1">حالة الرد التلقائي العام</h4>
                    <p class="text-muted mb-0">تفعيل أو إلغاء تفعيل الرد التلقائي لجميع الحسابات</p>
                </div>
                <label class="switch">
                    <input type="checkbox" id="globalAutoReplyToggle">
                    <span class="slider"></span>
                </label>
            </div>
            <div id="statusMessage" class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>الرد التلقائي العام غير مفعل حالياً
            </div>
        </div>

        <!-- نموذج إعداد الرد التلقائي -->
        <div class="auto-reply-form">
            <h3 class="mb-4"><i class="fas fa-cog me-2"></i>إعداد رسالة الرد التلقائي</h3>
            
            <!-- شريط الأدوات -->
            <div class="toolbar" id="toolbar">
                <button type="button" class="toolbar-btn active" data-type="text" title="رسالة نصية">
                    <i class="fas fa-font"></i>
                </button>
                <button type="button" class="toolbar-btn" data-type="image" title="صورة">
                    <i class="fas fa-image"></i>
                </button>
                <button type="button" class="toolbar-btn" data-type="file" title="ملف">
                    <i class="fas fa-file"></i>
                </button>
                <button type="button" class="toolbar-btn" data-type="folder" title="مجلد">
                    <i class="fas fa-folder"></i>
                </button>
                <div class="ms-auto">
                    <button type="button" class="toolbar-btn" id="insertNameBtn" title="إدراج اسم المرسل">
                        <i class="fas fa-user"></i>
                    </button>
                    <button type="button" class="toolbar-btn" id="emojiBtn" title="إضافة رموز تعبيرية">
                        <i class="fas fa-smile"></i>
                    </button>
                </div>
            </div>

            <!-- مربع النص -->
            <div class="message-input-container">
                <textarea id="messageText" class="form-control" placeholder="اكتب رسالة الرد التلقائي هنا...&#10;&#10;يمكنك استخدام {name} لإدراج اسم المرسل تلقائياً"></textarea>
                <div class="emoji-picker" id="emojiPicker"></div>
            </div>

            <!-- حاويات الملفات -->
            <div class="file-input-container" id="imageContainer">
                <label class="custom-file-input" for="imageInput">
                    <i class="fas fa-image d-block"></i>
                    <span>اختر صورة للرد التلقائي</span>
                    <input type="file" id="imageInput" accept="image/*" style="display: none;">
                </label>
                <div id="imagePreview"></div>
            </div>

            <div class="file-input-container" id="fileContainer">
                <label class="custom-file-input" for="fileInput">
                    <i class="fas fa-file d-block"></i>
                    <span>اختر ملف للرد التلقائي</span>
                    <input type="file" id="fileInput" style="display: none;">
                </label>
                <div id="filePreview"></div>
            </div>

            <div class="file-input-container" id="folderContainer">
                <label class="custom-file-input" for="folderInput">
                    <i class="fas fa-folder d-block"></i>
                    <span>اختر مجلد للرد التلقائي</span>
                    <input type="file" id="folderInput" webkitdirectory multiple style="display: none;">
                </label>
                <div id="folderPreview"></div>
            </div>

            <!-- معاينة الرسالة -->
            <div class="preview-section">
                <h5><i class="fas fa-eye me-2"></i>معاينة الرسالة</h5>
                <div id="messagePreview">
                    <div class="text-muted text-center py-3">
                        <i class="fas fa-comment-dots mb-2" style="font-size: 2rem;"></i>
                        <p>ستظهر معاينة الرسالة هنا</p>
                    </div>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="text-center mt-4">
                <button id="saveBtn" class="btn btn-whatsapp btn-lg me-3">
                    <i class="fas fa-save me-2"></i>حفظ إعدادات الرد التلقائي
                </button>
                <button id="testBtn" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-vial me-2"></i>اختبار الرسالة
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.12/dist/sweetalert2.all.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/emoji-picker-element@1.18.3/index.js" type="module"></script>
    <script src="auto-reply-global.js"></script>
</body>
</html>
