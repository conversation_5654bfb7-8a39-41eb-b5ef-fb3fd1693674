^D:\WHATSAPPNODE\NODE_MODULES\@TENSORFLOW\TFJS-NODE\BUILD\RELEASE\TFJS_BINDING.NODE
call mkdir "D:\Whatsappnode\node_modules\@tensorflow\tfjs-node\lib\napi-v8" 2>nul & set ERRORLEVEL=0 & copy /Y "D:\Whatsappnode\node_modules\@tensorflow\tfjs-node\build\Release\tfjs_binding.node" "D:\Whatsappnode\node_modules\@tensorflow\tfjs-node\lib\napi-v8\tfjs_binding.node"
if %errorlevel% neq 0 exit /b %errorlevel%
