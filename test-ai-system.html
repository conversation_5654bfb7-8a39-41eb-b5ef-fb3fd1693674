<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الذكاء الاصطناعي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .test-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
        }
        .result-card {
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #dee2e6;
            margin: 10px 0;
        }
        .score-badge {
            font-size: 1.1em;
            padding: 8px 15px;
        }
        .json-display {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="test-container p-4">
            <div class="text-center mb-4">
                <h1 class="text-primary">
                    <i class="fas fa-robot me-3"></i>
                    اختبار نظام الذكاء الاصطناعي المحسن
                </h1>
                <p class="text-muted">اختبار شامل لجميع مكونات نظام الرد التلقائي الذكي</p>
            </div>

            <!-- قسم اختبار معالجة النصوص -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-text-width me-2"></i>اختبار معالجة النصوص العربية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">النص للاختبار:</label>
                            <textarea id="textInput" class="form-control" rows="3" placeholder="أدخل النص هنا... مثال: مرحباً، كم السعر؟ رقم الهاتف: ٠١٢٣٤٥٦٧٨٩">مرحباً، كم السعر؟ رقم الهاتف: ٠١٢٣٤٥٦٧٨٩</textarea>
                            <button id="testTextBtn" class="btn btn-info mt-2">
                                <i class="fas fa-play me-2"></i>اختبار معالجة النص
                            </button>
                        </div>
                        <div class="col-md-6">
                            <div id="textResults" class="result-card p-3" style="display: none;">
                                <h6>نتائج معالجة النص:</h6>
                                <div id="textResultsContent"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قسم اختبار المطابقة الذكية -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-brain me-2"></i>اختبار المطابقة الذكية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">الرسالة للاختبار:</label>
                            <textarea id="messageInput" class="form-control" rows="2" placeholder="أدخل الرسالة...">مرحبا كيف الحال</textarea>
                            
                            <label class="form-label mt-3">عتبة التشابه (%):</label>
                            <input type="range" id="thresholdSlider" class="form-range" min="10" max="90" value="40">
                            <div class="text-center">
                                <span id="thresholdValue" class="badge bg-primary">40%</span>
                            </div>
                            
                            <button id="testAIBtn" class="btn btn-success mt-2 w-100">
                                <i class="fas fa-robot me-2"></i>اختبار المطابقة الذكية
                            </button>
                        </div>
                        <div class="col-md-8">
                            <div id="aiResults" class="result-card p-3" style="display: none;">
                                <h6>نتائج المطابقة الذكية:</h6>
                                <div id="aiResultsContent"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قسم اختبار التكامل -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-cogs me-2"></i>اختبار تكامل الأنظمة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">وضع الاختبار:</label>
                            <select id="modeSelect" class="form-select">
                                <option value="global">الوضع العام</option>
                                <option value="account">الوضع الخاص</option>
                            </select>
                            
                            <label class="form-label mt-3">اسم الحساب (للوضع الخاص):</label>
                            <input type="text" id="accountInput" class="form-control" placeholder="اسم الحساب..." value="test-account">
                            
                            <button id="testIntegrationBtn" class="btn btn-warning mt-2 w-100">
                                <i class="fas fa-flask me-2"></i>اختبار التكامل
                            </button>
                        </div>
                        <div class="col-md-6">
                            <div id="integrationResults" class="result-card p-3" style="display: none;">
                                <h6>نتائج اختبار التكامل:</h6>
                                <div id="integrationResultsContent"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قسم النتائج التفصيلية -->
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5><i class="fas fa-code me-2"></i>النتائج التفصيلية (JSON)</h5>
                </div>
                <div class="card-body">
                    <pre id="jsonResults" class="json-display p-3" style="display: none;"></pre>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث قيمة العتبة
        document.getElementById('thresholdSlider').addEventListener('input', function() {
            document.getElementById('thresholdValue').textContent = this.value + '%';
        });

        // اختبار معالجة النصوص
        document.getElementById('testTextBtn').addEventListener('click', async function() {
            const text = document.getElementById('textInput').value;
            if (!text.trim()) {
                alert('يرجى إدخال نص للاختبار');
                return;
            }

            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الاختبار...';

            try {
                const response = await fetch('/api/test-text-processing', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ text })
                });

                const data = await response.json();
                displayTextResults(data);
                displayJsonResults(data);
            } catch (error) {
                alert('حدث خطأ: ' + error.message);
            } finally {
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-play me-2"></i>اختبار معالجة النص';
            }
        });

        // اختبار المطابقة الذكية
        document.getElementById('testAIBtn').addEventListener('click', async function() {
            const message = document.getElementById('messageInput').value;
            const threshold = document.getElementById('thresholdSlider').value / 100;
            
            if (!message.trim()) {
                alert('يرجى إدخال رسالة للاختبار');
                return;
            }

            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الاختبار...';

            try {
                const response = await fetch('/api/test-ai-match', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message, threshold })
                });

                const data = await response.json();
                displayAIResults(data);
                displayJsonResults(data);
            } catch (error) {
                alert('حدث خطأ: ' + error.message);
            } finally {
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-robot me-2"></i>اختبار المطابقة الذكية';
            }
        });

        // اختبار التكامل
        document.getElementById('testIntegrationBtn').addEventListener('click', async function() {
            const mode = document.getElementById('modeSelect').value;
            const accountName = document.getElementById('accountInput').value;

            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الاختبار...';

            try {
                const response = await fetch('/api/test-auto-reply-integration', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ mode, accountName })
                });

                const data = await response.json();
                displayIntegrationResults(data);
                displayJsonResults(data);
            } catch (error) {
                alert('حدث خطأ: ' + error.message);
            } finally {
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-flask me-2"></i>اختبار التكامل';
            }
        });

        function displayTextResults(data) {
            const container = document.getElementById('textResultsContent');
            const resultsDiv = document.getElementById('textResults');
            
            if (data.success) {
                container.innerHTML = `
                    <div class="mb-2"><strong>النص الأصلي:</strong> ${data.original_text}</div>
                    <div class="mb-2"><strong>تطبيع الأرقام:</strong> ${data.normalized_numbers}</div>
                    <div class="mb-2"><strong>تطبيع العربية:</strong> ${data.normalized_arabic}</div>
                    <div class="mb-2"><strong>النص المعالج:</strong> ${data.processed_text}</div>
                    <div class="mb-2"><strong>الكلمات المفتاحية:</strong> ${data.keywords.join(', ')}</div>
                    ${Object.keys(data.synonyms).length > 0 ? 
                        `<div><strong>المرادفات:</strong><br>${Object.entries(data.synonyms).map(([key, values]) => 
                            `<small>${key}: ${values.join(', ')}</small>`).join('<br>')}</div>` : 
                        '<div><small>لا توجد مرادفات</small></div>'}
                `;
                resultsDiv.style.display = 'block';
            } else {
                container.innerHTML = `<div class="text-danger">خطأ: ${data.error}</div>`;
                resultsDiv.style.display = 'block';
            }
        }

        function displayAIResults(data) {
            const container = document.getElementById('aiResultsContent');
            const resultsDiv = document.getElementById('aiResults');
            
            if (data.success) {
                if (data.match) {
                    container.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>تم العثور على مطابقة!</h6>
                            <div><strong>القاعدة:</strong> ${data.match.code || data.match.id}</div>
                            <div><strong>النص:</strong> ${data.match.messages?.text || 'غير متوفر'}</div>
                            <div><strong>النتيجة:</strong> <span class="score-badge badge bg-success">${(data.score * 100).toFixed(1)}%</span></div>
                            <div><strong>النوع:</strong> ${data.type === 'exact' ? 'مطابقة دقيقة' : 'مطابقة ذكية'}</div>
                        </div>
                    `;
                } else {
                    container.innerHTML = `
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>لم يتم العثور على مطابقة</h6>
                            <div>${data.message || 'لا توجد قواعد تتطابق مع الرسالة'}</div>
                        </div>
                    `;
                }
                resultsDiv.style.display = 'block';
            } else {
                container.innerHTML = `<div class="alert alert-danger">خطأ: ${data.error}</div>`;
                resultsDiv.style.display = 'block';
            }
        }

        function displayIntegrationResults(data) {
            const container = document.getElementById('integrationResultsContent');
            const resultsDiv = document.getElementById('integrationResults');
            
            if (data.success) {
                let html = `<div class="mb-3"><strong>الوضع:</strong> ${data.mode === 'global' ? 'عام' : 'خاص'}</div>`;
                
                // عرض حالة الأنظمة
                Object.entries(data.systems_status).forEach(([key, status]) => {
                    const systemName = {
                        'regular_global': 'الرد التلقائي العام',
                        'regular_account': 'الرد التلقائي الخاص',
                        'advanced_global': 'الرد التلقائي المتقدم العام',
                        'advanced_account': 'الرد التلقائي المتقدم الخاص'
                    }[key];
                    
                    html += `
                        <div class="mb-2">
                            <strong>${systemName}:</strong>
                            <span class="badge ${status.enabled ? 'bg-success' : 'bg-danger'} me-1">
                                ${status.enabled ? 'مفعل' : 'معطل'}
                            </span>
                            ${status.enabled_rules_count !== undefined ? 
                                `<span class="badge bg-info">${status.enabled_rules_count} قاعدة</span>` : ''}
                            ${status.has_content !== undefined ? 
                                `<span class="badge ${status.has_content ? 'bg-info' : 'bg-warning'}">${status.has_content ? 'يحتوي محتوى' : 'لا يحتوي محتوى'}</span>` : ''}
                        </div>
                    `;
                });
                
                html += `
                    <hr>
                    <div class="alert alert-info">
                        <strong>التوصية:</strong> ${data.integration_analysis.recommendation}
                    </div>
                `;
                
                container.innerHTML = html;
                resultsDiv.style.display = 'block';
            } else {
                container.innerHTML = `<div class="alert alert-danger">خطأ: ${data.error}</div>`;
                resultsDiv.style.display = 'block';
            }
        }

        function displayJsonResults(data) {
            const container = document.getElementById('jsonResults');
            container.textContent = JSON.stringify(data, null, 2);
            container.style.display = 'block';
        }
    </script>
</body>
</html>
