[0627/235840.216:INFO:CONSOL<PERSON>(163)] "Event handler of 'x-storagemutated-1' event must be added on the initial evaluation of worker script.", source: https://static.whatsapp.net/rsrc.php/v4/yQ/r/XjJbdBZVNWX.js (163)
[0627/235842.679:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[0627/235842.679:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[0627/235842.680:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[0627/235842.680:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[0627/235842.680:INFO:CONSOL<PERSON>(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[0627/235842.680:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[0627/235842.680:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[0627/235842.680:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[0627/235842.680:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'unload'.", source:  (0)
[0627/235842.680:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[0627/235842.681:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://web.whatsapp.com/ (0)
[0627/235845.197:INFO:CONSOLE(163)] "Event handler of 'x-storagemutated-1' event must be added on the initial evaluation of worker script.", source: https://static.whatsapp.net/rsrc.php/v4/yQ/r/XjJbdBZVNWX.js (163)
[0627/235923.791:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0627/235923.912:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0627/235924.014:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0627/235924.344:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0627/235931.370:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0627/235931.619:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0628/000010.311:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0628/000010.432:INFO:CONSOLE(163)] "Event handler of 'x-storagemutated-1' event must be added on the initial evaluation of worker script.", source: https://static.whatsapp.net/rsrc.php/v4/yQ/r/XjJbdBZVNWX.js (163)
[0628/000010.506:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0628/000446.844:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0628/000447.038:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
