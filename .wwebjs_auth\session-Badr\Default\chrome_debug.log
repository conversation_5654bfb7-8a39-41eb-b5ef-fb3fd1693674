[0625/234650.441:INFO:CONSOLE(163)] "Event handler of 'x-storagemutated-1' event must be added on the initial evaluation of worker script.", source: https://static.whatsapp.net/rsrc.php/v4/yW/r/7TF8U4hAZnC.js (163)
[0625/234705.348:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[0625/234705.349:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[0625/234705.352:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[0625/234705.352:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[0625/234705.352:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[0625/234705.352:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[0625/234705.352:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[0625/234705.352:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[0625/234705.352:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'unload'.", source:  (0)
[0625/234705.352:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[0625/234705.353:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://web.whatsapp.com/ (0)
[0625/234714.667:INFO:CONSOLE(163)] "Event handler of 'x-storagemutated-1' event must be added on the initial evaluation of worker script.", source: https://static.whatsapp.net/rsrc.php/v4/yI/r/wly-09CeVTz.js (163)
[0625/234722.299:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0625/235031.471:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0625/235032.297:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0625/235032.702:INFO:CONSOLE(163)] "Event handler of 'x-storagemutated-1' event must be added on the initial evaluation of worker script.", source: https://static.whatsapp.net/rsrc.php/v4/yI/r/wly-09CeVTz.js (163)
[0625/235036.496:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0625/235036.681:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0625/235215.346:INFO:CONSOLE(902)] "Failed to parse video contentType: video/mp4; codecs=avc1.42000a", source: https://static.whatsapp.net/rsrc.php/v4iJJ14/yJ/l/rt/YEqa75ZY8Wt.js (902)
[0625/235215.346:INFO:CONSOLE(902)] "Failed to parse video contentType: video/mp4; codecs=hev1.1.6.L93.B0", source: https://static.whatsapp.net/rsrc.php/v4iJJ14/yJ/l/rt/YEqa75ZY8Wt.js (902)
[0625/235215.780:INFO:CONSOLE(163)] "Event handler of 'x-storagemutated-1' event must be added on the initial evaluation of worker script.", source: https://static.whatsapp.net/rsrc.php/v4/yI/r/wly-09CeVTz.js (163)
[0625/235720.074:INFO:CONSOLE(163)] "Event handler of 'x-storagemutated-1' event must be added on the initial evaluation of worker script.", source: https://static.whatsapp.net/rsrc.php/v4/yI/r/wly-09CeVTz.js (163)
