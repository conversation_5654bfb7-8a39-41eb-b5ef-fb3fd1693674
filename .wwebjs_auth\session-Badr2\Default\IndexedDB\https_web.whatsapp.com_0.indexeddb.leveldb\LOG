2025/06/27-23:34:57.086 480c Reusing MANIFEST D:\Whatsappnode\.wwebjs_auth\session-Badr2\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/06/27-23:34:57.087 480c Recovering log #2538
2025/06/27-23:34:57.247 480c Reusing old log D:\Whatsappnode\.wwebjs_auth\session-Badr2\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/002538.log 
2025/06/27-23:34:58.205 4ffc Manual compaction at level-0 from '\x00\x86\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x87\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/27-23:37:32.872 5798 Level-0 table #2562: started
2025/06/27-23:37:32.970 5798 Level-0 table #2562: 1195058 bytes OK
2025/06/27-23:37:32.975 5798 Delete type=0 #2538
2025/06/27-23:37:47.872 5a64 Compacting 1@0 + 0@1 files
2025/06/27-23:37:47.945 5a64 Generated table #2563@0: 29031 keys, 530875 bytes
2025/06/27-23:37:47.953 5a64 Generated table #2564@0: 709 keys, 33944 bytes
2025/06/27-23:37:47.953 5a64 Compacted 1@0 + 0@1 files => 564819 bytes
2025/06/27-23:37:47.955 5a64 compacted to: files[ 0 2 18 0 0 0 0 ]
2025/06/27-23:37:48.511 4f90 Compacting 1@1 + 8@2 files
2025/06/27-23:37:48.708 4f90 Generated table #2565@1: 29637 keys, 2254963 bytes
2025/06/27-23:37:48.782 4f90 Generated table #2566@1: 3348 keys, 2119380 bytes
2025/06/27-23:37:48.811 4f90 Generated table #2567@1: 850 keys, 2114207 bytes
2025/06/27-23:37:48.878 4f90 Generated table #2568@1: 12173 keys, 2150021 bytes
2025/06/27-23:37:49.256 4f90 Generated table #2569@1: 90780 keys, 2238298 bytes
2025/06/27-23:37:49.482 4f90 Generated table #2570@1: 98240 keys, 2250612 bytes
2025/06/27-23:37:49.527 4f90 Generated table #2571@1: 17142 keys, 444383 bytes
2025/06/27-23:37:49.527 4f90 Compacted 1@1 + 8@2 files => 13571864 bytes
2025/06/27-23:37:49.532 4f90 compacted to: files[ 0 1 17 0 0 0 0 ]
2025/06/27-23:37:49.533 4f90 Delete type=2 #2552
2025/06/27-23:37:49.533 4f90 Delete type=2 #2553
2025/06/27-23:37:49.533 4f90 Delete type=2 #2554
2025/06/27-23:37:49.533 4f90 Delete type=2 #2555
2025/06/27-23:37:49.533 4f90 Delete type=2 #2556
2025/06/27-23:37:49.533 4f90 Delete type=2 #2557
2025/06/27-23:37:49.533 4f90 Delete type=2 #2558
2025/06/27-23:37:49.533 4f90 Delete type=2 #2559
2025/06/27-23:37:49.533 4f90 Delete type=2 #2562
2025/06/27-23:37:49.533 4f90 Delete type=2 #2564
2025/06/27-23:37:59.459 4f90 Compacting 1@1 + 10@2 files
2025/06/27-23:37:59.658 4f90 Generated table #2572@1: 88411 keys, 2239672 bytes
2025/06/27-23:37:59.792 4f90 Generated table #2573@1: 95064 keys, 2246663 bytes
2025/06/27-23:37:59.890 4f90 Generated table #2574@1: 97996 keys, 2249046 bytes
2025/06/27-23:38:00.022 4f90 Generated table #2575@1: 101588 keys, 2254054 bytes
2025/06/27-23:38:00.151 4f90 Generated table #2576@1: 77680 keys, 2293764 bytes
2025/06/27-23:38:00.242 4f90 Generated table #2577@1: 30585 keys, 2194549 bytes
2025/06/27-23:38:00.274 4f90 Generated table #2578@1: 1897 keys, 2157196 bytes
2025/06/27-23:38:00.312 4f90 Generated table #2579@1: 2087 keys, 2144562 bytes
2025/06/27-23:38:00.347 4f90 Generated table #2580@1: 1112 keys, 2131766 bytes
2025/06/27-23:38:00.429 4f90 Generated table #2581@1: 15976 keys, 2192165 bytes
2025/06/27-23:38:00.440 4f90 Generated table #2582@1: 1128 keys, 113134 bytes
2025/06/27-23:38:00.440 4f90 Compacted 1@1 + 10@2 files => 22216571 bytes
2025/06/27-23:38:00.443 4f90 compacted to: files[ 0 0 18 0 0 0 0 ]
2025/06/27-23:38:00.444 4f90 Delete type=2 #2542
2025/06/27-23:38:00.444 4f90 Delete type=2 #2543
2025/06/27-23:38:00.444 4f90 Delete type=2 #2544
2025/06/27-23:38:00.444 4f90 Delete type=2 #2545
2025/06/27-23:38:00.444 4f90 Delete type=2 #2546
2025/06/27-23:38:00.444 4f90 Delete type=2 #2547
2025/06/27-23:38:00.444 4f90 Delete type=2 #2548
2025/06/27-23:38:00.444 4f90 Delete type=2 #2549
2025/06/27-23:38:00.444 4f90 Delete type=2 #2550
2025/06/27-23:38:00.444 4f90 Delete type=2 #2551
2025/06/27-23:38:00.444 4f90 Delete type=2 #2563
