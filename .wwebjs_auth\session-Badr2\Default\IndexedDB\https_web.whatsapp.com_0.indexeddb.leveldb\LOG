2025/06/27-03:18:20.748 21c4 Reusing MANIFEST D:\Whatsappnode\.wwebjs_auth\session-Badr2\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/06/27-03:18:20.750 21c4 Recovering log #2262
2025/06/27-03:18:21.258 21c4 Reusing old log D:\Whatsappnode\.wwebjs_auth\session-Badr2\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/002262.log 
2025/06/27-03:18:21.259 21c4 Delete type=2 #2245
2025/06/27-03:18:21.259 21c4 Delete type=2 #2246
2025/06/27-03:18:21.259 21c4 Delete type=2 #2247
2025/06/27-03:18:21.259 21c4 Delete type=2 #2248
2025/06/27-03:18:21.259 21c4 Delete type=2 #2249
2025/06/27-03:18:21.259 21c4 Delete type=2 #2250
2025/06/27-03:18:21.259 21c4 Delete type=2 #2251
2025/06/27-03:18:21.259 21c4 Delete type=2 #2252
2025/06/27-03:18:21.259 21c4 Delete type=2 #2253
2025/06/27-03:18:21.259 21c4 Delete type=2 #2254
2025/06/27-03:18:21.259 21c4 Delete type=2 #2264
2025/06/27-03:18:22.518 52b4 Level-0 table #2284: started
2025/06/27-03:18:22.620 52b4 Level-0 table #2284: 1202721 bytes OK
2025/06/27-03:18:22.623 52b4 Delete type=0 #2262
2025/06/27-03:18:22.624 52b4 Manual compaction at level-0 from '\x00{\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00|\x00\x00\x00' @ 0 : 0; will stop at '\x00{\x00\x00\x05' @ 5578962 : 1
2025/06/27-03:18:22.624 52b4 Compacting 1@0 + 0@1 files
2025/06/27-03:18:22.880 52b4 Generated table #2285@0: 24434 keys, 515887 bytes
2025/06/27-03:18:22.888 52b4 Generated table #2286@0: 383 keys, 50635 bytes
2025/06/27-03:18:22.888 52b4 Compacted 1@0 + 0@1 files => 566522 bytes
2025/06/27-03:18:22.891 52b4 compacted to: files[ 0 2 17 0 0 0 0 ]
2025/06/27-03:18:22.892 52b4 Delete type=2 #2284
2025/06/27-03:18:22.905 52b4 Manual compaction at level-0 from '\x00{\x00\x00\x05' @ 5578962 : 1 .. '\x00|\x00\x00\x00' @ 0 : 0; will stop at (end)
