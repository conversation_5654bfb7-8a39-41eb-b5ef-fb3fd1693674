document.addEventListener('DOMContentLoaded', function() {
    // الحصول على معاملات URL
    const urlParams = new URLSearchParams(window.location.search);
    const accountName = urlParams.get('account');
    const isGlobal = !accountName;

    console.log('Advanced Auto-Reply Mode:', isGlobal ? 'Global' : `Account: ${accountName}`);

    // تحديث عنوان الصفحة
    const pageTitle = document.getElementById('pageTitle');
    const pageSubtitle = document.getElementById('pageSubtitle');
    if (pageTitle && pageSubtitle) {
        if (isGlobal) {
            pageTitle.textContent = 'الرد التلقائي المتقدم - عام';
            pageSubtitle.textContent = 'نظام ذكي للرد التلقائي لجميع الحسابات';
        } else {
            pageTitle.textContent = `الرد التلقائي المتقدم - ${accountName}`;
            pageSubtitle.textContent = `نظام ذكي للرد التلقائي للحساب: ${accountName}`;
        }
    }

    // عناصر الواجهة
    const advancedAutoReplyToggle = document.getElementById('advancedAutoReplyToggle');
    const addRuleBtn = document.getElementById('addRuleBtn');
    const importExcelBtn = document.getElementById('importExcelBtn');
    const exportJsonBtn = document.getElementById('exportJsonBtn');
    const backBtn = document.getElementById('backBtn');
    const testSystemBtn = document.getElementById('testSystemBtn');
    const rulesTableContainer = document.getElementById('rulesTableContainer');

    // بيانات القواعد
    let rulesData = {
        version: "1.0",
        enabled: false,
        rules: [],
        ai_fallback: {
            enabled: true,
            similarity_threshold: 0.7,
            default_response: {
                text: "شكراً لك، سنرد عليك قريباً",
                image: null,
                file: null,
                folder: []
            }
        }
    };

    // تحميل البيانات عند بدء الصفحة
    loadAdvancedSettings();

    // مستمعي الأحداث
    advancedAutoReplyToggle.addEventListener('change', updateSystemStatus);
    addRuleBtn.addEventListener('click', showAddRuleModal);
    importExcelBtn.addEventListener('click', importFromExcel);
    exportJsonBtn.addEventListener('click', exportToJson);
    backBtn.addEventListener('click', goBack);
    testSystemBtn.addEventListener('click', testSystem);

    // تحميل الإعدادات المتقدمة
    function loadAdvancedSettings() {
        let url = '/api/auto-reply/advanced/load';
        if (accountName) {
            url += `?account=${encodeURIComponent(accountName)}`;
        }

        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.settings) {
                    rulesData = data.settings;
                    advancedAutoReplyToggle.checked = rulesData.enabled || false;
                    renderRulesTable();
                    console.log('تم تحميل إعدادات الرد التلقائي المتقدم:', isGlobal ? 'عام' : `للحساب: ${accountName}`);
                } else {
                    renderEmptyState();
                }
            })
            .catch(error => {
                console.error('Error loading advanced settings:', error);
                renderEmptyState();
            });
    }

    // تحديث حالة النظام
    function updateSystemStatus() {
        rulesData.enabled = advancedAutoReplyToggle.checked;
        saveAdvancedSettings();
    }

    // حفظ الإعدادات المتقدمة
    function saveAdvancedSettings() {
        let url = '/api/auto-reply/advanced/save';
        if (accountName) {
            url += `?account=${encodeURIComponent(accountName)}`;
        }

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(rulesData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('تم حفظ الإعدادات بنجاح:', isGlobal ? 'عام' : `للحساب: ${accountName}`);
            } else {
                console.error('خطأ في حفظ الإعدادات:', data.error);
            }
        })
        .catch(error => {
            console.error('خطأ في الاتصال:', error);
        });
    }

    // عرض جدول القواعد
    function renderRulesTable() {
        if (!rulesData.rules || rulesData.rules.length === 0) {
            renderEmptyState();
            return;
        }

        const tableHTML = `
            <table class="advanced-table">
                <thead>
                    <tr>
                        <th style="width: 60px;">#</th>
                        <th style="width: 100px;">الكود</th>
                        <th style="width: 180px;">رسالة النص</th>
                        <th style="width: 150px;">كلمات مفتاحية</th>
                        <th style="width: 100px;">الصورة</th>
                        <th style="width: 100px;">الملف</th>
                        <th style="width: 100px;">المجلد</th>
                        <th style="width: 80px;">الحالة</th>
                        <th style="width: 130px;">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    ${rulesData.rules.map((rule, index) => renderRuleRow(rule, index)).join('')}
                </tbody>
            </table>
        `;

        rulesTableContainer.innerHTML = tableHTML;
    }

    // عرض صف قاعدة واحدة
    function renderRuleRow(rule, index) {
        const hasText = rule.messages.text && rule.messages.text.trim();
        const hasImage = rule.messages.image;
        const hasFile = rule.messages.file;
        const hasFolder = rule.messages.folder && rule.messages.folder.length > 0;
        const hasKeywords = rule.keywords && rule.keywords.trim();

        return `
            <tr>
                <td>${index + 1}</td>
                <td>
                    <span class="code-cell">${rule.code}</span>
                </td>
                <td>
                    ${hasText ?
                        `<div class="message-preview" title="${rule.messages.text}">${rule.messages.text}</div>` :
                        '<span class="text-muted">-</span>'
                    }
                </td>
                <td>
                    ${hasKeywords ?
                        `<div class="keywords-preview" title="${rule.keywords}">${rule.keywords.length > 25 ? rule.keywords.substring(0, 25) + '...' : rule.keywords}</div>` :
                        '<span class="text-muted">-</span>'
                    }
                </td>
                <td>
                    ${hasImage ?
                        '<span class="file-indicator"><i class="fas fa-image"></i> صورة</span>' :
                        '<span class="text-muted">-</span>'
                    }
                </td>
                <td>
                    ${hasFile ?
                        '<span class="file-indicator"><i class="fas fa-file"></i> ملف</span>' :
                        '<span class="text-muted">-</span>'
                    }
                </td>
                <td>
                    ${hasFolder ?
                        `<span class="file-indicator"><i class="fas fa-folder"></i> ${rule.messages.folder.length} ملف</span>` :
                        '<span class="text-muted">-</span>'
                    }
                </td>
                <td>
                    <span class="badge ${rule.enabled ? 'bg-success' : 'bg-secondary'}">
                        ${rule.enabled ? 'مفعل' : 'معطل'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline-primary" onclick="editRule(${index})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="previewRule(${index})" title="معاينة">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteRule(${index})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    // عرض حالة فارغة
    function renderEmptyState() {
        rulesTableContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-robot"></i>
                <h3>لا توجد قواعد رد تلقائي</h3>
                <p>ابدأ بإضافة قاعدة جديدة أو استيراد البيانات من ملف Excel</p>
                <button class="btn btn-whatsapp mt-3" onclick="showAddRuleModal()">
                    <i class="fas fa-plus me-2"></i>إضافة قاعدة جديدة
                </button>
            </div>
        `;
    }

    // عرض نافذة إضافة قاعدة جديدة
    function showAddRuleModal() {
        Swal.fire({
            title: 'إضافة قاعدة رد تلقائي جديدة',
            html: `
                <div class="text-start">
                    <div class="mb-3">
                        <label class="form-label">الكود (رقم، إيموجي، أو كلمة للمطابقة 100%)</label>
                        <input type="text" class="form-control" id="ruleCode" placeholder="مثال: 1 أو 👋 أو مرحبا">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الكلمات المفتاحية (مفصولة بعلامة -)</label>
                        <input type="text" class="form-control" id="ruleKeywords" placeholder="مثال: مرحبا-أهلا-سلام-تحية">
                        <small class="text-muted">استخدم علامة (-) للفصل بين الكلمات المفتاحية</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableRuleText">
                            <label class="form-check-label" for="enableRuleText">تفعيل رسالة النص</label>
                        </div>
                        <textarea class="form-control mt-2" id="ruleText" rows="3" placeholder="اكتب رسالة النص هنا..." style="display: none;"></textarea>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableRuleImage">
                            <label class="form-check-label" for="enableRuleImage">تفعيل رسالة الصورة</label>
                        </div>
                        <input type="file" class="form-control mt-2" id="ruleImage" accept="image/*" style="display: none;">
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableRuleFile">
                            <label class="form-check-label" for="enableRuleFile">تفعيل رسالة الملف</label>
                        </div>
                        <input type="file" class="form-control mt-2" id="ruleFile" style="display: none;">
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableRuleFolder">
                            <label class="form-check-label" for="enableRuleFolder">تفعيل ملفات المجلد</label>
                        </div>
                        <input type="file" class="form-control mt-2" id="ruleFolder" multiple style="display: none;">
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="ruleEnabled" checked>
                            <label class="form-check-label" for="ruleEnabled">تفعيل هذه القاعدة</label>
                        </div>
                    </div>
                </div>
            `,
            width: '600px',
            showCancelButton: true,
            confirmButtonText: 'إضافة القاعدة',
            cancelButtonText: 'إلغاء',
            confirmButtonColor: '#25D366',
            didOpen: () => {
                // إضافة مستمعي الأحداث للتحكم في إظهار/إخفاء الحقول
                const enableRuleText = document.getElementById('enableRuleText');
                const enableRuleImage = document.getElementById('enableRuleImage');
                const enableRuleFile = document.getElementById('enableRuleFile');
                const enableRuleFolder = document.getElementById('enableRuleFolder');

                const ruleText = document.getElementById('ruleText');
                const ruleImage = document.getElementById('ruleImage');
                const ruleFile = document.getElementById('ruleFile');
                const ruleFolder = document.getElementById('ruleFolder');

                enableRuleText.addEventListener('change', () => {
                    ruleText.style.display = enableRuleText.checked ? 'block' : 'none';
                });

                enableRuleImage.addEventListener('change', () => {
                    ruleImage.style.display = enableRuleImage.checked ? 'block' : 'none';
                });

                enableRuleFile.addEventListener('change', () => {
                    ruleFile.style.display = enableRuleFile.checked ? 'block' : 'none';
                });

                enableRuleFolder.addEventListener('change', () => {
                    ruleFolder.style.display = enableRuleFolder.checked ? 'block' : 'none';
                });
            },
            preConfirm: () => {
                const code = document.getElementById('ruleCode').value.trim();
                const keywords = document.getElementById('ruleKeywords').value.trim();
                const enableText = document.getElementById('enableRuleText').checked;
                const enableImage = document.getElementById('enableRuleImage').checked;
                const enableFile = document.getElementById('enableRuleFile').checked;
                const enableFolder = document.getElementById('enableRuleFolder').checked;
                const enabled = document.getElementById('ruleEnabled').checked;

                if (!code) {
                    Swal.showValidationMessage('يجب إدخال الكود');
                    return false;
                }

                if (!enableText && !enableImage && !enableFile && !enableFolder) {
                    Swal.showValidationMessage('يجب تفعيل نوع واحد على الأقل من الرسائل');
                    return false;
                }

                const text = enableText ? document.getElementById('ruleText').value.trim() : '';
                if (enableText && !text) {
                    Swal.showValidationMessage('يجب إدخال نص الرسالة عند تفعيل رسالة النص');
                    return false;
                }

                return {
                    code,
                    keywords,
                    enableText,
                    enableImage,
                    enableFile,
                    enableFolder,
                    text,
                    enabled
                };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                addNewRule(result.value);
            }
        });
    }

    // استيراد من Excel
    function importFromExcel() {
        // سيتم تطوير هذه الوظيفة لاحقاً
        Swal.fire({
            title: 'استيراد من Excel',
            text: 'هذه الميزة قيد التطوير',
            icon: 'info'
        });
    }

    // تصدير إلى JSON
    function exportToJson() {
        const dataStr = JSON.stringify(rulesData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'auto-reply-advanced-rules.json';
        link.click();
        URL.revokeObjectURL(url);
    }

    // العودة للصفحة السابقة
    function goBack() {
        if (accountName) {
            // العودة لصفحة الرد التلقائي للحساب
            window.location.href = `auto-reply-account-new.html?account=${encodeURIComponent(accountName)}`;
        } else {
            // العودة لصفحة الرد التلقائي العام
            window.location.href = 'auto-reply-global-new.html';
        }
    }

    // اختبار النظام
    function testSystem() {
        // سيتم تطوير هذه الوظيفة لاحقاً
        Swal.fire({
            title: 'اختبار النظام',
            text: 'هذه الميزة قيد التطوير',
            icon: 'info'
        });
    }

    // وظائف عامة للجدول
    window.editRule = function(index) {
        const rule = rulesData.rules[index];
        if (!rule) return;

        Swal.fire({
            title: 'تعديل قاعدة الرد التلقائي',
            html: `
                <div class="text-start">
                    <div class="mb-3">
                        <label class="form-label">الكود (رقم، إيموجي، أو كلمة للمطابقة 100%)</label>
                        <input type="text" class="form-control" id="editRuleCode" value="${rule.code}" placeholder="مثال: 1 أو 👋 أو مرحبا">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الكلمات المفتاحية (مفصولة بعلامة -)</label>
                        <input type="text" class="form-control" id="editRuleKeywords" value="${rule.keywords || ''}" placeholder="مثال: مرحبا-أهلا-سلام-تحية">
                        <small class="text-muted">استخدم علامة (-) للفصل بين الكلمات المفتاحية</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editEnableRuleText" ${rule.messages.text ? 'checked' : ''}>
                            <label class="form-check-label" for="editEnableRuleText">تفعيل رسالة النص</label>
                        </div>
                        <textarea class="form-control mt-2" id="editRuleText" rows="3" placeholder="اكتب رسالة النص هنا..." style="display: ${rule.messages.text ? 'block' : 'none'};">${rule.messages.text || ''}</textarea>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editEnableRuleImage" ${rule.messages.image ? 'checked' : ''}>
                            <label class="form-check-label" for="editEnableRuleImage">تفعيل رسالة الصورة</label>
                        </div>
                        ${rule.messages.image ? `<div class="mt-2 text-muted">الصورة الحالية: متوفرة</div>` : ''}
                        <input type="file" class="form-control mt-2" id="editRuleImage" accept="image/*" style="display: ${rule.messages.image ? 'block' : 'none'};">
                        <small class="text-muted">اتركه فارغاً للاحتفاظ بالصورة الحالية</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editEnableRuleFile" ${rule.messages.file ? 'checked' : ''}>
                            <label class="form-check-label" for="editEnableRuleFile">تفعيل رسالة الملف</label>
                        </div>
                        ${rule.messages.file ? `<div class="mt-2 text-muted">الملف الحالي: متوفر</div>` : ''}
                        <input type="file" class="form-control mt-2" id="editRuleFile" style="display: ${rule.messages.file ? 'block' : 'none'};">
                        <small class="text-muted">اتركه فارغاً للاحتفاظ بالملف الحالي</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editEnableRuleFolder" ${rule.messages.folder && rule.messages.folder.length > 0 ? 'checked' : ''}>
                            <label class="form-check-label" for="editEnableRuleFolder">تفعيل ملفات المجلد</label>
                        </div>
                        ${rule.messages.folder && rule.messages.folder.length > 0 ? `<div class="mt-2 text-muted">الملفات الحالية: ${rule.messages.folder.length} ملف</div>` : ''}
                        <input type="file" class="form-control mt-2" id="editRuleFolder" multiple style="display: ${rule.messages.folder && rule.messages.folder.length > 0 ? 'block' : 'none'};">
                        <small class="text-muted">اتركه فارغاً للاحتفاظ بالملفات الحالية</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editRuleEnabled" ${rule.enabled ? 'checked' : ''}>
                            <label class="form-check-label" for="editRuleEnabled">تفعيل هذه القاعدة</label>
                        </div>
                    </div>
                </div>
            `,
            width: '600px',
            showCancelButton: true,
            confirmButtonText: 'حفظ التعديلات',
            cancelButtonText: 'إلغاء',
            confirmButtonColor: '#25D366',
            didOpen: () => {
                // إضافة مستمعي الأحداث للتحكم في إظهار/إخفاء الحقول
                const editEnableRuleText = document.getElementById('editEnableRuleText');
                const editEnableRuleImage = document.getElementById('editEnableRuleImage');
                const editEnableRuleFile = document.getElementById('editEnableRuleFile');
                const editEnableRuleFolder = document.getElementById('editEnableRuleFolder');

                const editRuleText = document.getElementById('editRuleText');
                const editRuleImage = document.getElementById('editRuleImage');
                const editRuleFile = document.getElementById('editRuleFile');
                const editRuleFolder = document.getElementById('editRuleFolder');

                editEnableRuleText.addEventListener('change', () => {
                    editRuleText.style.display = editEnableRuleText.checked ? 'block' : 'none';
                });

                editEnableRuleImage.addEventListener('change', () => {
                    editRuleImage.style.display = editEnableRuleImage.checked ? 'block' : 'none';
                });

                editEnableRuleFile.addEventListener('change', () => {
                    editRuleFile.style.display = editEnableRuleFile.checked ? 'block' : 'none';
                });

                editEnableRuleFolder.addEventListener('change', () => {
                    editRuleFolder.style.display = editEnableRuleFolder.checked ? 'block' : 'none';
                });
            },
            preConfirm: () => {
                const code = document.getElementById('editRuleCode').value.trim();
                const keywords = document.getElementById('editRuleKeywords').value.trim();
                const enableText = document.getElementById('editEnableRuleText').checked;
                const enableImage = document.getElementById('editEnableRuleImage').checked;
                const enableFile = document.getElementById('editEnableRuleFile').checked;
                const enableFolder = document.getElementById('editEnableRuleFolder').checked;
                const enabled = document.getElementById('editRuleEnabled').checked;

                if (!code) {
                    Swal.showValidationMessage('يجب إدخال الكود');
                    return false;
                }

                if (!enableText && !enableImage && !enableFile && !enableFolder) {
                    Swal.showValidationMessage('يجب تفعيل نوع واحد على الأقل من الرسائل');
                    return false;
                }

                const text = enableText ? document.getElementById('editRuleText').value.trim() : '';
                if (enableText && !text) {
                    Swal.showValidationMessage('يجب إدخال نص الرسالة عند تفعيل رسالة النص');
                    return false;
                }

                return {
                    code,
                    keywords,
                    enableText,
                    enableImage,
                    enableFile,
                    enableFolder,
                    text,
                    enabled
                };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                updateRule(index, result.value);
            }
        });
    };

    window.previewRule = function(index) {
        const rule = rulesData.rules[index];
        if (!rule) return;

        let previewHTML = `<div class="text-start">`;
        previewHTML += `<h6><strong>الكود:</strong> ${rule.code}</h6>`;

        if (rule.keywords) {
            previewHTML += `<h6><strong>الكلمات المفتاحية:</strong> ${rule.keywords}</h6>`;
        }

        if (rule.messages.text) {
            previewHTML += `<h6><strong>النص:</strong></h6><p>${rule.messages.text}</p>`;
        }
        
        if (rule.messages.image) {
            previewHTML += `<h6><strong>الصورة:</strong> متوفرة</h6>`;
        }
        
        if (rule.messages.file) {
            previewHTML += `<h6><strong>الملف:</strong> متوفر</h6>`;
        }
        
        if (rule.messages.folder && rule.messages.folder.length > 0) {
            previewHTML += `<h6><strong>المجلد:</strong> ${rule.messages.folder.length} ملف</h6>`;
        }
        
        previewHTML += `</div>`;

        Swal.fire({
            title: 'معاينة القاعدة',
            html: previewHTML,
            icon: 'info',
            width: '600px'
        });
    };

    window.deleteRule = function(index) {
        Swal.fire({
            title: 'تأكيد الحذف',
            text: 'هل أنت متأكد من حذف هذه القاعدة؟',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'حذف',
            cancelButtonText: 'إلغاء',
            confirmButtonColor: '#dc3545'
        }).then((result) => {
            if (result.isConfirmed) {
                rulesData.rules.splice(index, 1);
                saveAdvancedSettings();
                renderRulesTable();
                
                Swal.fire({
                    title: 'تم الحذف',
                    text: 'تم حذف القاعدة بنجاح',
                    icon: 'success',
                    timer: 2000
                });
            }
        });
    };

    // إضافة قاعدة جديدة
    function addNewRule(ruleData) {
        const formData = new FormData();

        // إنشاء معرف فريد للقاعدة
        const ruleId = 'rule_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

        const newRule = {
            id: ruleId,
            code: ruleData.code,
            keywords: ruleData.keywords || '',
            priority: rulesData.rules.length + 1,
            messages: {},
            enabled: ruleData.enabled,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        // إضافة النص
        if (ruleData.enableText && ruleData.text) {
            newRule.messages.text = ruleData.text;
        }

        // إضافة الصورة
        if (ruleData.enableImage) {
            const imageFile = document.getElementById('ruleImage').files[0];
            if (imageFile) {
                formData.append('image', imageFile);
                newRule.messages.image = 'pending'; // سيتم تحديثه بعد الرفع
            }
        }

        // إضافة الملف
        if (ruleData.enableFile) {
            const file = document.getElementById('ruleFile').files[0];
            if (file) {
                formData.append('file', file);
                newRule.messages.file = 'pending'; // سيتم تحديثه بعد الرفع
            }
        }

        // إضافة ملفات المجلد
        if (ruleData.enableFolder) {
            const folderFiles = document.getElementById('ruleFolder').files;
            if (folderFiles.length > 0) {
                for (let i = 0; i < folderFiles.length; i++) {
                    formData.append(`folder_${i}`, folderFiles[i]);
                }
                newRule.messages.folder = Array.from(folderFiles).map(() => 'pending'); // سيتم تحديثه بعد الرفع
            }
        }

        // إضافة القاعدة إلى البيانات
        rulesData.rules.push(newRule);

        // إضافة بيانات القاعدة إلى FormData
        formData.append('ruleData', JSON.stringify(newRule));

        // إرسال البيانات إلى الخادم
        fetch('/api/auto-reply/advanced/add-rule', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث مسارات الملفات في القاعدة
                if (data.filePaths) {
                    const ruleIndex = rulesData.rules.findIndex(r => r.id === ruleId);
                    if (ruleIndex !== -1) {
                        if (data.filePaths.image) {
                            rulesData.rules[ruleIndex].messages.image = data.filePaths.image;
                        }
                        if (data.filePaths.file) {
                            rulesData.rules[ruleIndex].messages.file = data.filePaths.file;
                        }
                        if (data.filePaths.folder) {
                            rulesData.rules[ruleIndex].messages.folder = data.filePaths.folder;
                        }
                    }
                }

                // حفظ الإعدادات المحدثة
                saveAdvancedSettings();

                // إعادة عرض الجدول
                renderRulesTable();

                Swal.fire({
                    title: 'تم بنجاح!',
                    text: 'تم إضافة القاعدة الجديدة بنجاح',
                    icon: 'success',
                    timer: 2000
                });
            } else {
                console.error('Error adding rule:', data.error);
                // إزالة القاعدة من البيانات في حالة الفشل
                rulesData.rules = rulesData.rules.filter(r => r.id !== ruleId);

                Swal.fire({
                    title: 'خطأ',
                    text: data.error || 'حدث خطأ في إضافة القاعدة',
                    icon: 'error'
                });
            }
        })
        .catch(error => {
            console.error('Error adding rule:', error);
            // إزالة القاعدة من البيانات في حالة الفشل
            rulesData.rules = rulesData.rules.filter(r => r.id !== ruleId);

            Swal.fire({
                title: 'خطأ',
                text: 'حدث خطأ في الاتصال بالخادم',
                icon: 'error'
            });
        });
    }

    // تحديث قاعدة موجودة
    function updateRule(index, ruleData) {
        const rule = rulesData.rules[index];
        if (!rule) return;

        const formData = new FormData();

        // تحديث البيانات الأساسية
        rule.code = ruleData.code;
        rule.keywords = ruleData.keywords || '';
        rule.enabled = ruleData.enabled;
        rule.updated_at = new Date().toISOString();

        // تحديث النص
        if (ruleData.enableText && ruleData.text) {
            rule.messages.text = ruleData.text;
        } else {
            delete rule.messages.text;
        }

        // تحديث الصورة
        if (ruleData.enableImage) {
            const imageFile = document.getElementById('editRuleImage').files[0];
            if (imageFile) {
                formData.append('image', imageFile);
                rule.messages.image = 'pending'; // سيتم تحديثه بعد الرفع
            }
            // إذا لم يتم اختيار صورة جديدة، الاحتفاظ بالصورة الحالية
        } else {
            delete rule.messages.image;
        }

        // تحديث الملف
        if (ruleData.enableFile) {
            const file = document.getElementById('editRuleFile').files[0];
            if (file) {
                formData.append('file', file);
                rule.messages.file = 'pending'; // سيتم تحديثه بعد الرفع
            }
            // إذا لم يتم اختيار ملف جديد، الاحتفاظ بالملف الحالي
        } else {
            delete rule.messages.file;
        }

        // تحديث ملفات المجلد
        if (ruleData.enableFolder) {
            const folderFiles = document.getElementById('editRuleFolder').files;
            if (folderFiles.length > 0) {
                for (let i = 0; i < folderFiles.length; i++) {
                    formData.append(`folder_${i}`, folderFiles[i]);
                }
                rule.messages.folder = Array.from(folderFiles).map(() => 'pending'); // سيتم تحديثه بعد الرفع
            }
            // إذا لم يتم اختيار ملفات جديدة، الاحتفاظ بالملفات الحالية
        } else {
            delete rule.messages.folder;
        }

        // إضافة بيانات القاعدة إلى FormData
        formData.append('ruleData', JSON.stringify(rule));
        formData.append('ruleIndex', index);

        // إرسال البيانات إلى الخادم
        fetch('/api/auto-reply/advanced/update-rule', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث مسارات الملفات في القاعدة
                if (data.filePaths) {
                    if (data.filePaths.image) {
                        rulesData.rules[index].messages.image = data.filePaths.image;
                    }
                    if (data.filePaths.file) {
                        rulesData.rules[index].messages.file = data.filePaths.file;
                    }
                    if (data.filePaths.folder) {
                        rulesData.rules[index].messages.folder = data.filePaths.folder;
                    }
                }

                // حفظ الإعدادات المحدثة
                saveAdvancedSettings();

                // إعادة عرض الجدول
                renderRulesTable();

                Swal.fire({
                    title: 'تم بنجاح!',
                    text: 'تم تحديث القاعدة بنجاح',
                    icon: 'success',
                    timer: 2000
                });
            } else {
                console.error('Error updating rule:', data.error);

                Swal.fire({
                    title: 'خطأ',
                    text: data.error || 'حدث خطأ في تحديث القاعدة',
                    icon: 'error'
                });
            }
        })
        .catch(error => {
            console.error('Error updating rule:', error);

            Swal.fire({
                title: 'خطأ',
                text: 'حدث خطأ في الاتصال بالخادم',
                icon: 'error'
            });
        });
    }

    // استيراد من Excel
    function importFromExcel() {
        Swal.fire({
            title: 'استيراد قواعد من Excel',
            html: `
                <div class="text-start">
                    <p>اختر ملف Excel يحتوي على القواعد:</p>
                    <input type="file" id="excelFile" accept=".xlsx,.xls" class="form-control mb-3">
                    <div class="alert alert-info">
                        <strong>تنسيق الملف المطلوب:</strong><br>
                        العمود الأول: الكود<br>
                        العمود الثاني: الكلمات المفتاحية (مفصولة بعلامة -)<br>
                        العمود الثالث: رسالة النص<br>
                        العمود الرابع: مسار الصورة (اختياري)<br>
                        العمود الخامس: مسار الملف (اختياري)<br>
                        العمود السادس: مسارات ملفات المجلد (مفصولة بفاصلة)
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'استيراد',
            cancelButtonText: 'إلغاء',
            confirmButtonColor: '#25D366',
            preConfirm: () => {
                const fileInput = document.getElementById('excelFile');
                if (!fileInput.files[0]) {
                    Swal.showValidationMessage('يرجى اختيار ملف Excel');
                    return false;
                }
                return fileInput.files[0];
            }
        }).then((result) => {
            if (result.isConfirmed) {
                processExcelFile(result.value);
            }
        });
    }

    // معالجة ملف Excel
    function processExcelFile(file) {
        Swal.fire({
            title: 'جاري المعالجة...',
            text: 'يتم استيراد البيانات من ملف Excel',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });

                // قراءة الورقة الأولى
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];

                // تحويل البيانات إلى JSON
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                if (jsonData.length < 2) {
                    Swal.fire({
                        title: 'خطأ',
                        text: 'الملف فارغ أو لا يحتوي على بيانات كافية',
                        icon: 'error'
                    });
                    return;
                }

                // تخطي الصف الأول (العناوين) ومعالجة البيانات
                const importedRules = [];
                let successCount = 0;
                let errorCount = 0;

                for (let i = 1; i < jsonData.length; i++) {
                    const row = jsonData[i];

                    // التحقق من وجود الكود على الأقل
                    if (!row[0] || row[0].toString().trim() === '') {
                        errorCount++;
                        continue;
                    }

                    const ruleId = 'rule_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9) + '_' + i;

                    const newRule = {
                        id: ruleId,
                        code: row[0].toString().trim(),
                        keywords: row[1] && row[1].toString().trim() !== '' ? row[1].toString().trim() : '',
                        priority: rulesData.rules.length + importedRules.length + 1,
                        messages: {},
                        enabled: true,
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    };

                    // إضافة النص إذا كان موجوداً
                    if (row[2] && row[2].toString().trim() !== '') {
                        newRule.messages.text = row[2].toString().trim();
                    }

                    // إضافة مسار الصورة إذا كان موجوداً
                    if (row[3] && row[3].toString().trim() !== '') {
                        newRule.messages.image = row[3].toString().trim();
                    }

                    // إضافة مسار الملف إذا كان موجوداً
                    if (row[4] && row[4].toString().trim() !== '') {
                        newRule.messages.file = row[4].toString().trim();
                    }

                    // إضافة مسارات ملفات المجلد إذا كانت موجودة
                    if (row[5] && row[5].toString().trim() !== '') {
                        const folderPaths = row[5].toString().split(',').map(path => path.trim()).filter(path => path !== '');
                        if (folderPaths.length > 0) {
                            newRule.messages.folder = folderPaths;
                        }
                    }

                    // التحقق من وجود نوع واحد على الأقل من الرسائل
                    if (!newRule.messages.text && !newRule.messages.image && !newRule.messages.file && !newRule.messages.folder) {
                        errorCount++;
                        continue;
                    }

                    importedRules.push(newRule);
                    successCount++;
                }

                if (importedRules.length === 0) {
                    Swal.fire({
                        title: 'لا توجد قواعد صالحة',
                        text: 'لم يتم العثور على قواعد صالحة في الملف',
                        icon: 'warning'
                    });
                    return;
                }

                // إضافة القواعد المستوردة
                rulesData.rules.push(...importedRules);

                // حفظ الإعدادات
                saveAdvancedSettings();

                // إعادة عرض الجدول
                renderRulesTable();

                Swal.fire({
                    title: 'تم الاستيراد بنجاح!',
                    html: `
                        <div class="text-start">
                            <p><strong>تم استيراد:</strong> ${successCount} قاعدة</p>
                            ${errorCount > 0 ? `<p><strong>فشل في استيراد:</strong> ${errorCount} صف</p>` : ''}
                            <p>يمكنك الآن مراجعة القواعد المستوردة وتعديلها حسب الحاجة.</p>
                        </div>
                    `,
                    icon: 'success'
                });

            } catch (error) {
                console.error('Error processing Excel file:', error);
                Swal.fire({
                    title: 'خطأ في معالجة الملف',
                    text: 'حدث خطأ أثناء قراءة ملف Excel. تأكد من صحة تنسيق الملف.',
                    icon: 'error'
                });
            }
        };

        reader.onerror = function() {
            Swal.fire({
                title: 'خطأ في قراءة الملف',
                text: 'حدث خطأ أثناء قراءة الملف',
                icon: 'error'
            });
        };

        reader.readAsArrayBuffer(file);
    }

    // تصدير إلى JSON
    function exportToJson() {
        Swal.fire({
            title: 'اختر نوع التصدير',
            html: `
                <div class="text-start">
                    <div class="mb-3">
                        <button class="btn btn-success w-100 mb-2" onclick="exportAsExcel()">
                            <i class="fas fa-file-excel me-2"></i>تصدير كملف Excel
                        </button>
                        <button class="btn btn-primary w-100" onclick="exportAsJson()">
                            <i class="fas fa-file-code me-2"></i>تصدير كملف JSON
                        </button>
                    </div>
                </div>
            `,
            showConfirmButton: false,
            showCancelButton: true,
            cancelButtonText: 'إلغاء'
        });
    }

    // تصدير كملف Excel
    window.exportAsExcel = function() {
        try {
            // إنشاء البيانات للتصدير
            const exportData = [
                ['الكود', 'الكلمات المفتاحية', 'رسالة النص', 'مسار الصورة', 'مسار الملف', 'مسارات ملفات المجلد', 'مفعل', 'تاريخ الإنشاء']
            ];

            rulesData.rules.forEach(rule => {
                exportData.push([
                    rule.code || '',
                    rule.keywords || '',
                    rule.messages.text || '',
                    rule.messages.image || '',
                    rule.messages.file || '',
                    rule.messages.folder ? rule.messages.folder.join(', ') : '',
                    rule.enabled ? 'نعم' : 'لا',
                    rule.created_at ? new Date(rule.created_at).toLocaleDateString('ar-SA') : ''
                ]);
            });

            // إنشاء workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(exportData);

            // تحسين عرض الأعمدة
            const colWidths = [
                { wch: 15 }, // الكود
                { wch: 25 }, // الكلمات المفتاحية
                { wch: 30 }, // رسالة النص
                { wch: 25 }, // مسار الصورة
                { wch: 25 }, // مسار الملف
                { wch: 35 }, // مسارات ملفات المجلد
                { wch: 10 }, // مفعل
                { wch: 15 }  // تاريخ الإنشاء
            ];
            ws['!cols'] = colWidths;

            // إضافة الورقة إلى الكتاب
            XLSX.utils.book_append_sheet(wb, ws, 'قواعد الرد التلقائي');

            // تصدير الملف
            const fileName = `advanced-auto-reply-rules-${new Date().toISOString().split('T')[0]}.xlsx`;
            XLSX.writeFile(wb, fileName);

            Swal.fire({
                title: 'تم التصدير!',
                text: 'تم تصدير القواعد إلى ملف Excel بنجاح',
                icon: 'success',
                timer: 2000
            });
        } catch (error) {
            console.error('Error exporting to Excel:', error);
            Swal.fire({
                title: 'خطأ في التصدير',
                text: 'حدث خطأ أثناء تصدير الملف',
                icon: 'error'
            });
        }
    };

    // تصدير كملف JSON
    window.exportAsJson = function() {
        const dataStr = JSON.stringify(rulesData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `advanced-auto-reply-rules-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        Swal.fire({
            title: 'تم التصدير!',
            text: 'تم تصدير القواعد إلى ملف JSON بنجاح',
            icon: 'success',
            timer: 2000
        });
    };



    // اختبار النظام
    function testSystem() {
        Swal.fire({
            title: 'اختبار النظام',
            html: `
                <div class="text-start">
                    <label class="form-label">اكتب رسالة تجريبية:</label>
                    <input type="text" id="testMessage" class="form-control" placeholder="مثال: مرحبا أو 1 أو 👋">
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'اختبار',
            cancelButtonText: 'إلغاء',
            confirmButtonColor: '#25D366',
            preConfirm: () => {
                const message = document.getElementById('testMessage').value.trim();
                if (!message) {
                    Swal.showValidationMessage('يرجى إدخال رسالة للاختبار');
                    return false;
                }
                return message;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                testMessageMatching(result.value);
            }
        });
    }

    // اختبار مطابقة الرسالة
    function testMessageMatching(message) {
        // البحث عن مطابقة دقيقة
        const exactMatch = rulesData.rules.find(rule =>
            rule.enabled && rule.code && rule.code.toLowerCase() === message.toLowerCase()
        );

        if (exactMatch) {
            Swal.fire({
                title: 'مطابقة دقيقة!',
                html: `
                    <div class="text-start">
                        <p><strong>تم العثور على مطابقة دقيقة للكود:</strong> "${exactMatch.code}"</p>
                        <p><strong>الرد سيكون:</strong></p>
                        ${exactMatch.messages.text ? `<p>• نص: ${exactMatch.messages.text}</p>` : ''}
                        ${exactMatch.messages.image ? `<p>• صورة: متوفرة</p>` : ''}
                        ${exactMatch.messages.file ? `<p>• ملف: متوفر</p>` : ''}
                        ${exactMatch.messages.folder && exactMatch.messages.folder.length > 0 ? `<p>• ملفات مجلد: ${exactMatch.messages.folder.length} ملف</p>` : ''}
                    </div>
                `,
                icon: 'success'
            });
        } else {
            // محاولة المطابقة بالذكاء الاصطناعي
            const aiMatch = findBestTextMatch(message);
            if (aiMatch) {
                Swal.fire({
                    title: 'مطابقة بالذكاء الاصطناعي',
                    html: `
                        <div class="text-start">
                            <p><strong>تم العثور على مطابقة نصية للقاعدة:</strong> "${aiMatch.code}"</p>
                            <p><strong>النص المطابق:</strong> "${aiMatch.messages.text}"</p>
                            <p><strong>الرد سيكون:</strong></p>
                            ${aiMatch.messages.text ? `<p>• نص: ${aiMatch.messages.text}</p>` : ''}
                            ${aiMatch.messages.image ? `<p>• صورة: متوفرة</p>` : ''}
                            ${aiMatch.messages.file ? `<p>• ملف: متوفر</p>` : ''}
                            ${aiMatch.messages.folder && aiMatch.messages.folder.length > 0 ? `<p>• ملفات مجلد: ${aiMatch.messages.folder.length} ملف</p>` : ''}
                        </div>
                    `,
                    icon: 'info'
                });
            } else {
                Swal.fire({
                    title: 'لا توجد مطابقة',
                    text: 'لم يتم العثور على قاعدة مطابقة لهذه الرسالة. سيتم إرسال الرد الافتراضي إذا كان مفعلاً.',
                    icon: 'warning'
                });
            }
        }
    }

    // البحث عن أفضل مطابقة نصية
    function findBestTextMatch(message) {
        const enabledRules = rulesData.rules.filter(rule => rule.enabled && rule.messages.text);
        let bestMatch = null;
        let bestSimilarity = 0;

        for (const rule of enabledRules) {
            const similarity = calculateSimilarity(message, rule.messages.text);
            if (similarity > bestSimilarity && similarity >= 0.7) {
                bestSimilarity = similarity;
                bestMatch = rule;
            }
        }

        return bestMatch;
    }

    // حساب التشابه بين النصوص
    function calculateSimilarity(text1, text2) {
        const words1 = text1.toLowerCase().split(/\s+/);
        const words2 = text2.toLowerCase().split(/\s+/);

        const commonWords = words1.filter(word => words2.includes(word));
        const totalWords = Math.max(words1.length, words2.length);

        return commonWords.length / totalWords;
    }

    window.showAddRuleModal = showAddRuleModal;
});

// إضافة تنسيقات CSS للكلمات المفتاحية
const keywordsStyle = document.createElement('style');
keywordsStyle.textContent = `
    .keywords-preview {
        font-size: 0.9em;
        color: #6c757d;
        background-color: #f8f9fa;
        padding: 2px 6px;
        border-radius: 4px;
        border: 1px solid #e9ecef;
        display: inline-block;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .keywords-preview:hover {
        background-color: #e9ecef;
        cursor: help;
    }
`;
document.head.appendChild(keywordsStyle);
