#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8

echo ""
echo "========================================"
echo "  🚀 WhatsApp Node - اختبار سريع"
echo "  📅 الإصدار 2.0 المحسن"
echo "========================================"
echo ""

echo "🔍 فحص المتطلبات..."

# فحص Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python غير مثبت أو غير موجود في PATH"
        echo "💡 يرجى تثبيت Python من: https://python.org"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi
echo "✅ Python متوفر ($($PYTHON_CMD --version))"

# فحص Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت أو غير موجود في PATH"
    echo "💡 يرجى تثبيت Node.js من: https://nodejs.org"
    exit 1
fi
echo "✅ Node.js متوفر ($(node --version))"

# فحص PyQt5
if ! $PYTHON_CMD -c "import PyQt5" &> /dev/null; then
    echo "❌ PyQt5 غير مثبت"
    echo "🔄 جاري تثبيت PyQt5..."
    
    # محاولة تثبيت PyQt5
    if command -v pip3 &> /dev/null; then
        pip3 install PyQt5 PyQtWebEngine
    elif command -v pip &> /dev/null; then
        pip install PyQt5 PyQtWebEngine
    else
        echo "❌ pip غير متوفر لتثبيت PyQt5"
        echo "💡 يرجى تثبيت pip أو PyQt5 يدوياً"
        exit 1
    fi
    
    # فحص التثبيت مرة أخرى
    if ! $PYTHON_CMD -c "import PyQt5" &> /dev/null; then
        echo "❌ فشل في تثبيت PyQt5"
        exit 1
    fi
fi
echo "✅ PyQt5 متوفر"

echo ""
echo "🧪 تشغيل اختبار التحسينات..."
echo ""

# تشغيل ملف الاختبار
$PYTHON_CMD test_browser.py

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ فشل في تشغيل الاختبار"
    echo "💡 تحقق من رسائل الخطأ أعلاه"
    exit 1
fi

echo ""
echo "✅ تم الانتهاء من الاختبار"
