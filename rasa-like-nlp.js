// ===== نظام NLP متقدم يحاكي وظائف Rasa =====
// Advanced NLP System that mimics Rasa functionality

const natural = require('natural');
const compromise = require('compromise');
const fs = require('fs');
const path = require('path');

// ===== نظام تحليل النوايا المتقدم =====
class AdvancedIntentClassifier {
    constructor() {
        this.intents = new Map();
        this.entities = new Map();
        this.patterns = new Map();
        this.confidence_threshold = 0.7;
        
        // تحميل النماذج المدربة
        this.loadTrainingData();
        
        // إعداد المصنفات
        this.setupClassifiers();
    }

    // تحميل بيانات التدريب
    loadTrainingData() {
        try {
            const trainingPath = path.join(__dirname, 'nlp-training-data.json');
            if (fs.existsSync(trainingPath)) {
                const data = JSON.parse(fs.readFileSync(trainingPath, 'utf8'));
                this.intents = new Map(Object.entries(data.intents || {}));
                this.entities = new Map(Object.entries(data.entities || {}));
                this.patterns = new Map(Object.entries(data.patterns || {}));
            } else {
                this.initializeDefaultTrainingData();
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات التدريب:', error);
            this.initializeDefaultTrainingData();
        }
    }

    // إنشاء بيانات التدريب الافتراضية
    initializeDefaultTrainingData() {
        // النوايا الأساسية
        this.intents.set('greeting', {
            examples: [
                'مرحبا', 'أهلا', 'هلا', 'السلام عليكم', 'صباح الخير', 'مساء الخير',
                'hello', 'hi', 'hey', 'good morning', 'good evening', 'سلام', 'أهلين'
            ],
            responses: ['مرحباً بك! كيف يمكنني مساعدتك؟', 'أهلاً وسهلاً! ما الذي تحتاجه؟']
        });

        this.intents.set('price_inquiry', {
            examples: [
                'كم السعر', 'بكم', 'ما السعر', 'كم التكلفة', 'كم الثمن', 'أسعاركم',
                'what is the price', 'how much', 'cost', 'price', 'pricing'
            ],
            responses: ['يمكنك الاطلاع على أسعارنا من خلال...', 'أسعارنا تبدأ من...']
        });

        this.intents.set('purchase_request', {
            examples: [
                'أريد شراء', 'أبغى أشتري', 'أطلب', 'أحتاج', 'بدي أشتري',
                'i want to buy', 'i need', 'purchase', 'order', 'buy'
            ],
            responses: ['ممتاز! ما المنتج الذي تريد شراءه؟', 'سأساعدك في عملية الشراء']
        });

        this.intents.set('complaint', {
            examples: [
                'عندي مشكلة', 'شكوى', 'مشكلة في', 'عطل', 'خلل', 'لا يعمل',
                'problem', 'issue', 'complaint', 'not working', 'error'
            ],
            responses: ['أعتذر عن المشكلة. دعني أساعدك في حلها', 'سأقوم بتوجيهك للدعم الفني']
        });

        this.intents.set('thanks', {
            examples: [
                'شكرا', 'شكراً', 'تسلم', 'يعطيك العافية', 'جزاك الله خير',
                'thanks', 'thank you', 'thx', 'appreciate'
            ],
            responses: ['العفو! سعداء بخدمتك', 'لا شكر على واجب']
        });

        // الكيانات
        this.entities.set('product', {
            values: ['جوال', 'هاتف', 'لابتوب', 'كمبيوتر', 'تابلت', 'phone', 'laptop', 'computer', 'tablet']
        });

        this.entities.set('number', {
            pattern: /\d+|[٠-٩]+/g
        });

        this.entities.set('currency', {
            values: ['ريال', 'دولار', 'دينار', 'درهم', 'SAR', 'USD', 'AED']
        });

        // حفظ البيانات الافتراضية
        this.saveTrainingData();
    }

    // إعداد المصنفات
    setupClassifiers() {
        // مصنف النوايا باستخدام Naive Bayes
        this.intentClassifier = new natural.BayesClassifier();
        
        // تدريب المصنف على النوايا
        for (const [intent, data] of this.intents) {
            for (const example of data.examples) {
                this.intentClassifier.addDocument(example, intent);
            }
        }
        
        this.intentClassifier.train();
    }

    // تحليل الرسالة وتحديد النية
    async analyzeMessage(message) {
        try {
            // تطبيع النص
            const normalizedMessage = this.normalizeText(message);
            
            // تحديد النية
            const intent = this.classifyIntent(normalizedMessage);
            
            // استخراج الكيانات
            const entities = this.extractEntities(normalizedMessage);
            
            // تحليل المشاعر
            const sentiment = this.analyzeSentiment(normalizedMessage);
            
            // حساب الثقة
            const confidence = this.calculateConfidence(normalizedMessage, intent);

            return {
                message: message,
                normalized_message: normalizedMessage,
                intent: intent,
                entities: entities,
                sentiment: sentiment,
                confidence: confidence,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('خطأ في تحليل الرسالة:', error);
            return {
                message: message,
                intent: 'unknown',
                entities: [],
                confidence: 0,
                error: error.message
            };
        }
    }

    // تطبيع النص
    normalizeText(text) {
        // تحويل الأرقام العربية
        let normalized = text.replace(/[٠-٩]/g, (match) => {
            return '٠١٢٣٤٥٦٧٨٩'.indexOf(match).toString();
        });
        
        // إزالة التشكيل
        normalized = normalized.replace(/[\u064B-\u0652]/g, '');
        
        // تطبيع الأحرف العربية
        normalized = normalized.replace(/[أإآ]/g, 'ا');
        normalized = normalized.replace(/[ة]/g, 'ه');
        normalized = normalized.replace(/[ى]/g, 'ي');
        
        // تحويل إلى أحرف صغيرة
        normalized = normalized.toLowerCase();
        
        return normalized.trim();
    }

    // تصنيف النية
    classifyIntent(message) {
        const classification = this.intentClassifier.classify(message);
        const classifications = this.intentClassifier.getClassifications(message);
        
        // التحقق من الثقة
        const topClassification = classifications[0];
        if (topClassification && topClassification.value >= this.confidence_threshold) {
            return {
                intent: classification,
                confidence: topClassification.value,
                all_classifications: classifications
            };
        }
        
        return {
            intent: 'unknown',
            confidence: topClassification ? topClassification.value : 0,
            all_classifications: classifications
        };
    }

    // استخراج الكيانات
    extractEntities(message) {
        const entities = [];
        
        // استخراج الأرقام
        const numbers = message.match(/\d+/g);
        if (numbers) {
            numbers.forEach(num => {
                entities.push({
                    entity: 'number',
                    value: num,
                    start: message.indexOf(num),
                    end: message.indexOf(num) + num.length
                });
            });
        }
        
        // استخراج المنتجات
        for (const [entityType, entityData] of this.entities) {
            if (entityData.values) {
                for (const value of entityData.values) {
                    if (message.includes(value)) {
                        entities.push({
                            entity: entityType,
                            value: value,
                            start: message.indexOf(value),
                            end: message.indexOf(value) + value.length
                        });
                    }
                }
            }
        }
        
        return entities;
    }

    // تحليل المشاعر
    analyzeSentiment(message) {
        try {
            // استخدام طريقة مبسطة لتحليل المشاعر
            const tokenizer = new natural.WordTokenizer();
            const tokens = tokenizer.tokenize(message.toLowerCase());

            let positiveScore = 0;
            let negativeScore = 0;

            // كلمات إيجابية عربية وإنجليزية
            const positiveWords = ['جيد', 'ممتاز', 'رائع', 'شكرا', 'أحب', 'سعيد', 'مرحبا', 'أهلا', 'good', 'great', 'excellent', 'love', 'happy', 'thanks', 'hello'];
            const negativeWords = ['سيء', 'مشكلة', 'خطأ', 'غاضب', 'حزين', 'bad', 'problem', 'error', 'angry', 'sad'];

            for (const token of tokens) {
                if (positiveWords.includes(token)) {
                    positiveScore++;
                } else if (negativeWords.includes(token)) {
                    negativeScore++;
                }
            }

            const totalScore = positiveScore + negativeScore;
            if (totalScore === 0) {
                return { sentiment: 'neutral', score: 0, confidence: 0.5 };
            }

            const sentiment = positiveScore > negativeScore ? 'positive' : 'negative';
            const score = positiveScore > negativeScore ? positiveScore / tokens.length : -negativeScore / tokens.length;
            const confidence = Math.max(positiveScore, negativeScore) / totalScore;

            return {
                sentiment: sentiment,
                score: score,
                confidence: confidence
            };
        } catch (error) {
            console.error('خطأ في تحليل المشاعر:', error);
            return {
                sentiment: 'neutral',
                score: 0,
                confidence: 0
            };
        }
    }

    // حساب الثقة
    calculateConfidence(message, intentResult) {
        return intentResult.confidence || 0;
    }

    // العثور على أفضل رد
    findBestResponse(analysisResult, availableRules) {
        try {
            const intent = analysisResult.intent.intent || analysisResult.intent;
            const confidence = analysisResult.confidence;
            
            // البحث في القواعد المتاحة
            let bestMatch = null;
            let bestScore = 0;
            
            for (const rule of availableRules) {
                if (!rule.enabled || !rule.messages.text) continue;
                
                // حساب التطابق مع النية المحددة
                const intentMatch = this.calculateIntentMatch(intent, rule.messages.text);
                
                // حساب التطابق النصي
                const textMatch = this.calculateTextSimilarity(analysisResult.message, rule.messages.text);
                
                // حساب النتيجة المركبة
                const compositeScore = (intentMatch * 0.6) + (textMatch * 0.4);
                
                if (compositeScore > bestScore) {
                    bestScore = compositeScore;
                    bestMatch = {
                        rule: rule,
                        score: compositeScore,
                        intent_match: intentMatch,
                        text_match: textMatch,
                        analysis: analysisResult
                    };
                }
            }
            
            return bestMatch;
        } catch (error) {
            console.error('خطأ في العثور على أفضل رد:', error);
            return null;
        }
    }

    // حساب تطابق النية
    calculateIntentMatch(detectedIntent, ruleText) {
        // البحث عن كلمات مفتاحية مرتبطة بالنية
        const intentKeywords = this.getIntentKeywords(detectedIntent);
        const ruleWords = ruleText.toLowerCase().split(' ');
        
        let matches = 0;
        for (const keyword of intentKeywords) {
            if (ruleWords.some(word => word.includes(keyword) || keyword.includes(word))) {
                matches++;
            }
        }
        
        return matches / Math.max(intentKeywords.length, 1);
    }

    // الحصول على كلمات مفتاحية للنية
    getIntentKeywords(intent) {
        const keywordMap = {
            'greeting': ['مرحبا', 'أهلا', 'hello', 'hi'],
            'price_inquiry': ['سعر', 'كم', 'price', 'cost'],
            'purchase_request': ['شراء', 'أريد', 'buy', 'purchase'],
            'complaint': ['مشكلة', 'شكوى', 'problem', 'issue'],
            'thanks': ['شكرا', 'thanks', 'thank']
        };
        
        return keywordMap[intent] || [];
    }

    // حساب التشابه النصي
    calculateTextSimilarity(text1, text2) {
        const natural = require('natural');
        return natural.JaroWinklerDistance(text1.toLowerCase(), text2.toLowerCase());
    }

    // حفظ بيانات التدريب
    saveTrainingData() {
        try {
            const data = {
                intents: Object.fromEntries(this.intents),
                entities: Object.fromEntries(this.entities),
                patterns: Object.fromEntries(this.patterns)
            };
            
            fs.writeFileSync(
                path.join(__dirname, 'nlp-training-data.json'),
                JSON.stringify(data, null, 2),
                'utf8'
            );
        } catch (error) {
            console.error('خطأ في حفظ بيانات التدريب:', error);
        }
    }

    // إضافة مثال تدريب جديد
    addTrainingExample(intent, example, response = null) {
        if (!this.intents.has(intent)) {
            this.intents.set(intent, { examples: [], responses: [] });
        }
        
        const intentData = this.intents.get(intent);
        if (!intentData.examples.includes(example)) {
            intentData.examples.push(example);
            
            if (response && !intentData.responses.includes(response)) {
                intentData.responses.push(response);
            }
            
            // إعادة تدريب المصنف
            this.setupClassifiers();
            this.saveTrainingData();
            
            return true;
        }
        
        return false;
    }

    // الحصول على إحصائيات النموذج
    getModelStats() {
        const stats = {
            totalIntents: this.intents.size,
            totalExamples: 0,
            totalEntities: Object.keys(this.trainingData.entities).length,
            totalPatterns: Object.keys(this.trainingData.patterns).length,
            intentDetails: {}
        };

        // حساب إجمالي الأمثلة وتفاصيل كل نية
        for (const [intent, data] of this.intents.entries()) {
            const exampleCount = data.examples ? data.examples.length : 0;
            const responseCount = data.responses ? data.responses.length : 0;

            stats.totalExamples += exampleCount;
            stats.intentDetails[intent] = {
                examples: exampleCount,
                responses: responseCount
            };
        }

        return stats;
    }

    // تحميل بيانات التدريب من ملف
    loadTrainingDataFromFile() {
        try {
            const fs = require('fs');
            const path = require('path');
            const filePath = path.join(__dirname, 'nlp-training-data.json');

            if (fs.existsSync(filePath)) {
                const data = fs.readFileSync(filePath, 'utf8');
                const loadedData = JSON.parse(data);

                // دمج البيانات المحملة مع البيانات الافتراضية
                if (loadedData.intents) {
                    for (const [intent, data] of Object.entries(loadedData.intents)) {
                        this.intents.set(intent, data);
                    }
                }

                if (loadedData.entities) {
                    this.trainingData.entities = { ...this.trainingData.entities, ...loadedData.entities };
                }

                if (loadedData.patterns) {
                    this.trainingData.patterns = { ...this.trainingData.patterns, ...loadedData.patterns };
                }

                // إعادة تدريب المصنف
                this.setupClassifiers();

                console.log('Training data loaded successfully from file');
                return true;
            }
        } catch (error) {
            console.error('Error loading training data from file:', error);
        }
        return false;
    }

    // تصدير بيانات التدريب
    exportTrainingData() {
        const exportData = {
            intents: {},
            entities: this.trainingData.entities,
            patterns: this.trainingData.patterns,
            metadata: {
                version: '1.0',
                created: new Date().toISOString(),
                total_intents: this.intents.size,
                total_examples: 0
            }
        };

        // تحويل Map إلى Object للتصدير
        for (const [intent, data] of this.intents.entries()) {
            exportData.intents[intent] = data;
            exportData.metadata.total_examples += data.examples.length;
        }

        return exportData;
    }
}

module.exports = {
    AdvancedIntentClassifier
};
