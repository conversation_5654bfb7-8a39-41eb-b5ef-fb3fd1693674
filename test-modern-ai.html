<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الذكاء الاصطناعي المتقدم والحديث</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            padding: 30px;
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .header-section {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
        }
        
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .btn-modern {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            color: white;
        }
        
        .result-card {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            border-left: 5px solid #4CAF50;
        }
        
        .analysis-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .score-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin: 2px;
        }
        
        .score-high { background: #4CAF50; color: white; }
        .score-medium { background: #FF9800; color: white; }
        .score-low { background: #f44336; color: white; }
        
        .keyword-tag {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            margin: 2px;
            border: 1px solid #bbdefb;
        }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .progress-modern {
            height: 8px;
            border-radius: 10px;
            background: #e0e0e0;
            overflow: hidden;
        }
        
        .progress-bar-modern {
            height: 100%;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            border-radius: 10px;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header-section">
                <h1><i class="fas fa-robot"></i> نظام الذكاء الاصطناعي المتقدم والحديث</h1>
                <p class="mb-0">اختبار متقدم لنظام المطابقة الذكية مع تقنيات الذكاء الاصطناعي الحديثة</p>
            </div>

            <!-- Test Message Input -->
            <div class="test-card">
                <h3><i class="fas fa-message"></i> اختبار تحليل الرسائل المتقدم</h3>
                <div class="row">
                    <div class="col-md-8">
                        <label class="form-label">الرسالة للاختبار:</label>
                        <textarea id="messageInput" class="form-control" rows="3" placeholder="أدخل الرسالة هنا...">مرحبا، أريد معرفة السعر للمنتج الجديد</textarea>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">عتبة التشابه:</label>
                        <input type="range" id="thresholdSlider" class="form-range" min="0" max="100" value="30">
                        <div class="text-center">
                            <span id="thresholdValue" class="badge bg-primary">30%</span>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <button onclick="testModernAI()" class="btn btn-modern">
                        <i class="fas fa-brain"></i> اختبار النظام المتقدم
                    </button>
                    <button onclick="testTextProcessing()" class="btn btn-modern ms-2">
                        <i class="fas fa-cogs"></i> اختبار معالجة النصوص
                    </button>
                </div>
            </div>

            <!-- Loading Spinner -->
            <div id="loadingSpinner" class="loading-spinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحليل...</span>
                </div>
                <p class="mt-2">جاري تحليل الرسالة باستخدام الذكاء الاصطناعي المتقدم...</p>
            </div>

            <!-- Results Section -->
            <div id="resultsSection" style="display: none;">
                <!-- Message Analysis Results -->
                <div class="test-card">
                    <h3><i class="fas fa-chart-line"></i> تحليل الرسالة المتقدم</h3>
                    <div id="messageAnalysisResults"></div>
                </div>

                <!-- AI Matching Results -->
                <div class="test-card">
                    <h3><i class="fas fa-search"></i> نتائج المطابقة الذكية</h3>
                    <div id="matchingResults"></div>
                </div>

                <!-- Detailed Breakdown -->
                <div class="test-card">
                    <h3><i class="fas fa-microscope"></i> التحليل التفصيلي</h3>
                    <div id="detailedBreakdown"></div>
                </div>
            </div>

            <!-- Text Processing Results -->
            <div id="textProcessingResults" style="display: none;">
                <div class="test-card">
                    <h3><i class="fas fa-text-width"></i> نتائج معالجة النصوص</h3>
                    <div id="textProcessingContent"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Update threshold display
        document.getElementById('thresholdSlider').addEventListener('input', function() {
            document.getElementById('thresholdValue').textContent = this.value + '%';
        });

        // Test Modern AI System
        async function testModernAI() {
            const message = document.getElementById('messageInput').value.trim();
            const threshold = document.getElementById('thresholdSlider').value / 100;

            if (!message) {
                alert('يرجى إدخال رسالة للاختبار');
                return;
            }

            // Show loading
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('textProcessingResults').style.display = 'none';

            try {
                const response = await fetch('/api/test-modern-ai', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        threshold: threshold
                    })
                });

                const result = await response.json();
                
                // Hide loading
                document.getElementById('loadingSpinner').style.display = 'none';
                
                if (result.success) {
                    displayModernAIResults(result);
                    document.getElementById('resultsSection').style.display = 'block';
                } else {
                    alert('خطأ في الاختبار: ' + result.error);
                }
            } catch (error) {
                document.getElementById('loadingSpinner').style.display = 'none';
                alert('خطأ في الاتصال: ' + error.message);
            }
        }

        // Display Modern AI Results
        function displayModernAIResults(result) {
            // Message Analysis
            const analysisHtml = `
                <div class="analysis-section">
                    <h5><i class="fas fa-brain"></i> تحليل الرسالة الذكي</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>النص الأصلي:</strong> ${result.analysis.original}</p>
                            <p><strong>النص المعالج:</strong> ${result.analysis.processed}</p>
                            <p><strong>نوع الرسالة:</strong> <span class="badge bg-primary">${result.analysis.type}</span></p>
                            <p><strong>اللغة:</strong> <span class="badge bg-info">${result.analysis.language}</span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>المشاعر:</strong> <span class="badge bg-success">${result.analysis.sentiment}</span></p>
                            <p><strong>النية:</strong> <span class="badge bg-warning">${result.analysis.intent}</span></p>
                            <p><strong>مستوى الثقة:</strong> <span class="badge bg-secondary">${(result.analysis.confidence * 100).toFixed(1)}%</span></p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <strong>الكلمات المفتاحية:</strong><br>
                        ${result.analysis.keywords.map(k => `<span class="keyword-tag">${k}</span>`).join('')}
                    </div>
                </div>
            `;
            document.getElementById('messageAnalysisResults').innerHTML = analysisHtml;

            // Matching Results
            let matchingHtml = '';
            if (result.match) {
                const scoreClass = result.match.score > 0.7 ? 'score-high' : result.match.score > 0.4 ? 'score-medium' : 'score-low';
                matchingHtml = `
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle"></i> تم العثور على مطابقة!</h5>
                        <p><strong>القاعدة:</strong> ${result.match.rule.code}</p>
                        <p><strong>النص المطابق:</strong> ${result.match.rule.messages.text}</p>
                        <p><strong>نتيجة التشابه:</strong> <span class="score-badge ${scoreClass}">${(result.match.score * 100).toFixed(1)}%</span></p>
                        <p><strong>مستوى الثقة:</strong> <span class="score-badge ${scoreClass}">${(result.match.confidence * 100).toFixed(1)}%</span></p>
                    </div>
                `;
            } else {
                matchingHtml = `
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> لم يتم العثور على مطابقة</h5>
                        <p>لم يتم العثور على قاعدة مطابقة تتجاوز العتبة المحددة (${(result.threshold * 100).toFixed(1)}%)</p>
                    </div>
                `;
            }
            document.getElementById('matchingResults').innerHTML = matchingHtml;

            // Detailed Breakdown
            if (result.match && result.match.breakdown) {
                let breakdownHtml = '<h5>تفصيل خوارزميات المطابقة:</h5><div class="row">';
                for (const [algorithm, score] of Object.entries(result.match.breakdown)) {
                    const scoreClass = score > 0.7 ? 'score-high' : score > 0.4 ? 'score-medium' : 'score-low';
                    breakdownHtml += `
                        <div class="col-md-4 mb-2">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h6>${algorithm}</h6>
                                    <span class="score-badge ${scoreClass}">${(score * 100).toFixed(1)}%</span>
                                </div>
                            </div>
                        </div>
                    `;
                }
                breakdownHtml += '</div>';
                document.getElementById('detailedBreakdown').innerHTML = breakdownHtml;
            }
        }

        // Test Text Processing
        async function testTextProcessing() {
            const text = document.getElementById('messageInput').value.trim();

            if (!text) {
                alert('يرجى إدخال نص للاختبار');
                return;
            }

            try {
                const response = await fetch('/api/test-modern-text-processing', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ text: text })
                });

                const result = await response.json();
                
                if (result.success) {
                    displayTextProcessingResults(result);
                    document.getElementById('textProcessingResults').style.display = 'block';
                    document.getElementById('resultsSection').style.display = 'none';
                } else {
                    alert('خطأ في الاختبار: ' + result.error);
                }
            } catch (error) {
                alert('خطأ في الاتصال: ' + error.message);
            }
        }

        // Display Text Processing Results
        function displayTextProcessingResults(result) {
            const html = `
                <div class="analysis-section">
                    <h5><i class="fas fa-text-width"></i> معالجة النصوص المتقدمة</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>النص الأصلي:</strong><br><code>${result.original_text}</code></p>
                            <p><strong>تطبيع الأرقام:</strong><br><code>${result.normalized_numbers}</code></p>
                            <p><strong>تطبيع العربية:</strong><br><code>${result.normalized_arabic}</code></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>النص المعالج:</strong><br><code>${result.processed_text}</code></p>
                            <p><strong>الكلمات المفتاحية:</strong><br>
                                ${result.keywords.map(k => `<span class="keyword-tag">${k}</span>`).join('')}
                            </p>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('textProcessingContent').innerHTML = html;
        }
    </script>
</body>
</html>
