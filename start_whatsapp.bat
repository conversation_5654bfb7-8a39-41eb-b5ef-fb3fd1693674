@echo off
chcp 65001 >nul
title WhatsApp Node - المتصفح المدمج

echo.
echo ================================================
echo 🚀 WhatsApp Node - المتصفح المدمج الحصري
echo ================================================
echo.

:: التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.7 أو أحدث
    pause
    exit /b 1
)

:: التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Node.js 14 أو أحدث
    pause
    exit /b 1
)

:: التحقق من وجود pip
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip غير متوفر
    echo يرجى التأكد من تثبيت Python بشكل صحيح
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات متوفرة
echo.

:: تثبيت مكتبات Python المطلوبة
echo 📦 فحص مكتبات Python...
pip show PyQt5 >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 تثبيت PyQt5...
    pip install PyQt5 PyQtWebEngine requests
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت مكتبات Python
        pause
        exit /b 1
    )
) else (
    echo ✅ مكتبات Python متوفرة
)

:: تثبيت تبعيات Node.js إذا لم تكن موجودة
if not exist "node_modules" (
    echo 📦 تثبيت تبعيات Node.js...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت تبعيات Node.js
        pause
        exit /b 1
    )
) else (
    echo ✅ تبعيات Node.js متوفرة
)

echo.
echo 🚀 بدء تشغيل المتصفح المدمج...
echo.

:: تشغيل المتصفح المدمج
python start_whatsapp_browser.py

echo.
echo 👋 تم إغلاق التطبيق
pause
