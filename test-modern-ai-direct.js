// ===== اختبار مباشر لنظام الذكاء الاصطناعي المتقدم =====

const { ModernAITextProcessor, ModernMessageAnalyzer, ModernAIMatcher } = require('./advanced-ai-system');

console.log('🚀 بدء اختبار نظام الذكاء الاصطناعي المتقدم...\n');

// رسائل اختبار متنوعة
const testMessages = [
    'مرحبا، أريد معرفة السعر',
    'كم سعر المنتج الجديد؟',
    'عندي مشكلة في الطلب',
    'شكراً لكم على الخدمة الممتازة',
    'Hello, what is the price?',
    'أريد شراء ٣ قطع بسعر ١٠٠ ريال',
    'متى يصل الطلب؟',
    'أريد إلغاء الطلب رقم ١٢٣٤',
    'هل يوجد خصم على المنتجات؟',
    'أحتاج مساعدة في الدفع'
];

async function testAISystem() {
    try {
        // إنشاء نسخ من النظام
        const processor = new ModernAITextProcessor();
        const analyzer = new ModernMessageAnalyzer();
        const matcher = new ModernAIMatcher();

        console.log('📊 نتائج اختبار معالجة النصوص والتحليل:\n');
        console.log('=' .repeat(80));

        for (let i = 0; i < testMessages.length; i++) {
            const message = testMessages[i];
            
            console.log(`\n🔍 اختبار رقم ${i + 1}:`);
            console.log(`📝 الرسالة: "${message}"`);
            
            // معالجة النص
            const processedText = processor.processText(message);
            console.log(`🔧 النص المعالج: "${processedText}"`);
            
            // استخراج الكلمات المفتاحية
            const keywords = processor.extractAdvancedKeywords(message);
            console.log(`🔑 الكلمات المفتاحية: [${keywords.slice(0, 3).join(', ')}]`);
            
            // استخراج الكيانات
            const entities = processor.extractEntities(message);
            const entityCount = Object.keys(entities).reduce((sum, key) => sum + entities[key].length, 0);
            console.log(`🏷️  الكيانات المستخرجة: ${entityCount} كيان`);
            
            // تحليل الرسالة
            const analysis = analyzer.analyzeMessage(message);
            console.log(`📈 التحليل: نوع=${analysis.type}, لغة=${analysis.language}, مشاعر=${analysis.sentiment}, نية=${analysis.intent}, ثقة=${(analysis.confidence * 100).toFixed(1)}%`);
            
            console.log('-'.repeat(80));
        }

        console.log('\n✅ تم إنجاز جميع الاختبارات بنجاح!');
        console.log('🎉 نظام الذكاء الاصطناعي المتقدم يعمل بشكل مثالي!');

    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        console.error('التفاصيل:', error.stack);
    }
}

// تشغيل الاختبار
testAISystem();
