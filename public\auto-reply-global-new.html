<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الرد التلقائي العام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .container {
            max-width: 1000px;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            border-radius: 50px;
            padding: 10px 20px;
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .main-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: 80px;
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
            color: white;
            padding: 30px;
            text-align: center;
            border: none;
        }

        .card-header h2 {
            margin: 0;
            font-size: 2rem;
            font-weight: 600;
        }

        .card-body {
            padding: 40px;
        }

        .status-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .status-toggle {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #25D366;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .message-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .message-section.active {
            border-color: #25D366;
            box-shadow: 0 5px 20px rgba(37, 211, 102, 0.2);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .section-title {
            display: flex;
            align-items: center;
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
        }

        .section-title i {
            margin-left: 10px;
            color: #25D366;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 25px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 25px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 19px;
            width: 19px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: #25D366;
        }

        input:checked + .toggle-slider:before {
            transform: translateX(25px);
        }

        .section-content {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .section-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #25D366;
            box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.25);
        }

        .btn {
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-whatsapp {
            background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
            border: none;
            color: white;
        }

        .btn-whatsapp:hover {
            background: linear-gradient(135deg, #128C7E 0%, #25D366 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(37, 211, 102, 0.4);
        }

        .preview-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .preview-content {
            background: white;
            border-radius: 10px;
            padding: 20px;
            min-height: 100px;
            border: 1px solid #dee2e6;
        }

        .file-preview {
            display: flex;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-top: 10px;
        }

        .file-preview i {
            font-size: 1.5rem;
            margin-left: 10px;
            color: #6c757d;
        }

        .image-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 10px;
            margin-top: 10px;
            border: 2px solid #dee2e6;
        }

        .emoji-toolbar {
            display: flex;
            gap: 5px;
            margin-bottom: 15px;
        }

        .toolbar-btn {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 8px 12px;
            margin: 0 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toolbar-btn:hover {
            background: #25D366;
            color: white;
            border-color: #25D366;
        }

        .emoji-picker {
            position: absolute;
            z-index: 1000;
            display: none;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            cursor: pointer;
            width: 100%;
        }

        .file-input-wrapper input[type=file] {
            position: absolute;
            left: -9999px;
        }

        .file-input-label {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-input-label:hover {
            border-color: #25D366;
            background: rgba(37, 211, 102, 0.1);
        }

        .file-input-label i {
            font-size: 2rem;
            color: #6c757d;
            margin-bottom: 10px;
        }

        .multiple-files-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }

        .file-item {
            display: flex;
            align-items: center;
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .file-item .remove-file {
            margin-right: 8px;
            color: #dc3545;
            cursor: pointer;
        }

        .file-item .remove-file:hover {
            color: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <button id="backBtn" class="btn btn-secondary back-btn">
                <i class="fas fa-arrow-right me-2"></i>العودة للوحة التحكم
            </button>
            <button id="advancedModeBtn" class="btn btn-outline-primary">
                <i class="fas fa-robot me-2"></i>الرد التلقائي المتقدم
            </button>
        </div>

        <div class="main-card">
            <div class="card-header">
                <h2><i class="fas fa-globe me-3"></i>الرد التلقائي العام</h2>
                <p class="mb-0 mt-2">إعداد رسالة رد تلقائي لجميع الحسابات</p>
            </div>

            <div class="card-body">
                <!-- قسم حالة الرد التلقائي -->
                <div class="status-section">
                    <div class="status-toggle">
                        <div>
                            <h4 class="mb-1">حالة الرد التلقائي العام</h4>
                            <p class="text-muted mb-0">تفعيل أو إلغاء تفعيل الرد التلقائي لجميع الحسابات</p>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="globalAutoReplyToggle">
                            <span class="slider"></span>
                        </label>
                    </div>
                    <div id="statusMessage" class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>الرد التلقائي العام غير مفعل حالياً
                    </div>
                </div>

                <!-- قسم النص -->
                <div class="message-section" id="textSection">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-comment-alt"></i>
                            رسالة نصية
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" id="enableText" onchange="toggleSection('text')">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="section-content" id="textContent">
                        <div class="emoji-toolbar">
                            <button type="button" class="toolbar-btn" id="insertNameBtn">
                                <i class="fas fa-user me-1"></i>إدراج الاسم
                            </button>
                            <button type="button" class="toolbar-btn" id="emojiBtn">
                                <i class="fas fa-smile me-1"></i>رموز تعبيرية
                            </button>
                        </div>
                        <textarea id="messageText" class="form-control" rows="4" 
                                  placeholder="اكتب رسالة الرد التلقائي هنا... يمكنك استخدام {name} لإدراج اسم المرسل"></textarea>
                        <div class="emoji-picker" id="emojiPicker"></div>
                    </div>
                </div>

                <!-- قسم الصورة -->
                <div class="message-section" id="imageSection">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-image"></i>
                            صورة
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" id="enableImage" onchange="toggleSection('image')">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="section-content" id="imageContent">
                        <div class="file-input-wrapper">
                            <input type="file" id="imageInput" accept="image/*">
                            <label for="imageInput" class="file-input-label">
                                <div class="text-center">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <div>انقر لاختيار صورة</div>
                                    <small class="text-muted">PNG, JPG, GIF حتى 10MB</small>
                                </div>
                            </label>
                        </div>
                        <div id="imagePreviewContainer"></div>
                    </div>
                </div>

                <!-- قسم الملف -->
                <div class="message-section" id="fileSection">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-file"></i>
                            ملف
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" id="enableFile" onchange="toggleSection('file')">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="section-content" id="fileContent">
                        <div class="file-input-wrapper">
                            <input type="file" id="fileInput">
                            <label for="fileInput" class="file-input-label">
                                <div class="text-center">
                                    <i class="fas fa-paperclip"></i>
                                    <div>انقر لاختيار ملف</div>
                                    <small class="text-muted">أي نوع ملف حتى 50MB</small>
                                </div>
                            </label>
                        </div>
                        <div id="filePreviewContainer"></div>
                    </div>
                </div>

                <!-- قسم ملفات متعددة -->
                <div class="message-section" id="folderSection">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-folder"></i>
                            ملفات متعددة
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" id="enableFolder" onchange="toggleSection('folder')">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="section-content" id="folderContent">
                        <div class="file-input-wrapper">
                            <input type="file" id="folderInput" multiple>
                            <label for="folderInput" class="file-input-label">
                                <div class="text-center">
                                    <i class="fas fa-files"></i>
                                    <div>انقر لاختيار ملفات متعددة</div>
                                    <small class="text-muted">يمكنك اختيار عدة ملفات معاً</small>
                                </div>
                            </label>
                        </div>
                        <div id="folderPreviewContainer" class="multiple-files-container"></div>
                    </div>
                </div>

                <!-- معاينة الرسالة -->
                <div class="preview-section">
                    <h5><i class="fas fa-eye me-2"></i>معاينة الرسالة الكاملة</h5>
                    <div id="fullPreview" class="preview-content">
                        <div class="text-muted text-center py-4">
                            <i class="fas fa-comment-dots mb-2" style="font-size: 2rem;"></i>
                            <p>ستظهر معاينة الرسالة الكاملة هنا</p>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="text-center mt-4">
                    <button id="saveBtn" class="btn btn-whatsapp btn-lg me-3">
                        <i class="fas fa-save me-2"></i>حفظ إعدادات الرد التلقائي
                    </button>
                    <button id="testBtn" class="btn btn-outline-primary btn-lg me-3">
                        <i class="fas fa-vial me-2"></i>اختبار الرسالة
                    </button>
                    <button onclick="deleteAllSettings()" class="btn btn-outline-danger btn-lg">
                        <i class="fas fa-trash-alt me-2"></i>حذف جميع الإعدادات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script type="module" src="https://cdn.jsdelivr.net/npm/emoji-picker-element@^1/index.js"></script>
    <script src="auto-reply-global-new.js"></script>
</body>
</html>
