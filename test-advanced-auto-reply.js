// ملف اختبار لنظام الرد التلقائي المتقدم
// يمكن استخدامه لاختبار دوال معالجة النصوص العربية

// محاكاة الدوال من server.js
function convertArabicNumbers(text) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    let convertedText = text;
    for (let i = 0; i < arabicNumbers.length; i++) {
        convertedText = convertedText.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
    }
    return convertedText;
}

function normalizeArabicText(text) {
    if (!text || typeof text !== 'string') return text;
    
    let normalizedText = text;
    
    // إزالة التشكيل (الحركات)
    normalizedText = normalizedText.replace(/[\u064B-\u0652\u0670\u0640]/g, '');
    
    // توحيد الهمزات
    normalizedText = normalizedText.replace(/[أإآ]/g, 'ا');
    normalizedText = normalizedText.replace(/[ؤ]/g, 'و');
    normalizedText = normalizedText.replace(/[ئ]/g, 'ي');
    normalizedText = normalizedText.replace(/[ة]/g, 'ه');
    
    // توحيد الياء
    normalizedText = normalizedText.replace(/[ى]/g, 'ي');
    
    // إزالة علامات الترقيم والمسافات الزائدة
    normalizedText = normalizedText.replace(/[.,;:!?()[\]{}"'`~@#$%^&*+=|\\/<>؟،]/g, ' ');
    normalizedText = normalizedText.replace(/\s+/g, ' ').trim();
    
    return normalizedText;
}

function processArabicTextForMatching(text) {
    if (!text || typeof text !== 'string') return '';
    
    // تحويل الأرقام العربية
    let processedText = convertArabicNumbers(text);
    
    // تطبيع النص العربي
    processedText = normalizeArabicText(processedText);
    
    // تحويل إلى أحرف صغيرة
    processedText = processedText.toLowerCase();
    
    return processedText;
}

function calculateKeywordMatch(messageText, keywords) {
    if (!keywords || !messageText) return 0;

    // معالجة النص للمطابقة (تطبيع عربي + تحويل أرقام)
    const processedMessage = processArabicTextForMatching(messageText);
    const processedKeywords = processArabicTextForMatching(keywords);

    // تقسيم الرسالة إلى كلمات
    const messageWords = processedMessage.split(/\s+/).filter(w => w.length > 0);

    // تقسيم الكلمات المفتاحية (مفصولة بـ -)
    const keywordList = processedKeywords.split('-').map(k => k.trim()).filter(k => k.length > 0);

    if (keywordList.length === 0 || messageWords.length === 0) return 0;

    // حساب عدد الكلمات المطابقة
    let matchCount = 0;
    for (const word of messageWords) {
        if (keywordList.includes(word)) {
            matchCount++;
        }
    }

    // حساب النسبة المئوية للتطابق
    const matchPercentage = (matchCount / Math.max(messageWords.length, keywordList.length)) * 100;
    
    console.log(`🔍 مطابقة الكلمات المفتاحية:`);
    console.log(`   الرسالة الأصلية: "${messageText}"`);
    console.log(`   الرسالة المعالجة: "${processedMessage}"`);
    console.log(`   الكلمات المفتاحية الأصلية: "${keywords}"`);
    console.log(`   الكلمات المفتاحية المعالجة: "${processedKeywords}"`);
    console.log(`   كلمات الرسالة: [${messageWords.join(', ')}]`);
    console.log(`   قائمة الكلمات المفتاحية: [${keywordList.join(', ')}]`);
    console.log(`   عدد المطابقات: ${matchCount}/${Math.max(messageWords.length, keywordList.length)}`);
    console.log(`   نسبة التطابق: ${matchPercentage.toFixed(2)}%`);
    
    return matchPercentage;
}

// اختبارات
console.log('=== اختبار تحويل الأرقام العربية ===');
console.log(convertArabicNumbers('رقم ١٢٣٤٥')); // رقم 12345

console.log('\n=== اختبار تطبيع النصوص العربية ===');
console.log(normalizeArabicText('أهلاً وسهلاً بك في المتجر!')); // اهلا وسهلا بك في المتجر

console.log('\n=== اختبار معالجة النصوص للمطابقة ===');
console.log(processArabicTextForMatching('أريد معرفة الأسعار!')); // اريد معرفه الاسعار

console.log('\n=== اختبار مطابقة الكلمات المفتاحية ===');
calculateKeywordMatch('أريد معرفة الأسعار', 'اسعار-معرفه-سعر');
calculateKeywordMatch('كم سعر المنتج؟', 'سعر-منتج-كم');
calculateKeywordMatch('مرحباً بك', 'مرحبا-اهلا-سلام');
