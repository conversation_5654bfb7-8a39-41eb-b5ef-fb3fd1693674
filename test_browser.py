#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف اختبار المتصفح المدمج المحسن
يستخدم لاختبار التحسينات الجديدة
"""

import sys
import os

# إضافة مسار المشروع إلى sys.path
project_path = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_path)

try:
    from PyQt5.QtWidgets import QApplication
    from LocalBrowser import WhatsAppBrowser
    from browser_config import Config
    
    def main():
        """الدالة الرئيسية لاختبار المتصفح"""
        print("🚀 بدء اختبار المتصفح المدمج المحسن...")
        print(f"📊 إعدادات الحماية: {'مفعلة' if Config.SECURITY_ENABLED else 'معطلة'}")
        print(f"🌐 المنفذ: {Config.DEFAULT_PORT}")
        print(f"💾 حجم التخزين المؤقت: {Config.CACHE_SIZE_MB} MB")
        print(f"🍪 الكوكيز الدائمة: {'مفعلة' if Config.ENABLE_PERSISTENT_COOKIES else 'معطلة'}")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("WhatsApp Node - المتصفح المدمج المحسن")
        app.setApplicationVersion("2.0")
        
        # إنشاء المتصفح
        browser = WhatsAppBrowser()
        browser.show()
        
        print("✅ تم تشغيل المتصفح بنجاح")
        print("🔍 يرجى فحص:")
        print("  - تحميل الصفحات بشكل صحيح")
        print("  - استجابة الأزرار والعناصر التفاعلية")
        print("  - عمل CSS و JavaScript")
        print("  - تحميل الصور والخطوط")
        print("  - حماية F12 وكليك يمين")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())

    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("💡 تأكد من تثبيت PyQt5:")
    print("   pip install PyQt5 PyQtWebEngine")
    sys.exit(1)
except Exception as e:
    print(f"❌ خطأ غير متوقع: {e}")
    sys.exit(1)
