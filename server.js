const express = require('express');

const path = require('path');
const fs = require('fs');
const qrcode = require('qrcode');
const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const bodyParser = require('body-parser');
const cors = require('cors');
const multer = require('multer');
const { saveAccountData, loadAccountData, saveContactsData, loadContactsData, getContactsLastUpdated } = require('./utils');

// مكتبات الذكاء الاصطناعي
const natural = require('natural');

const Fuse = require('fuse.js');


// استيراد نظام الذكاء الاصطناعي المتقدم والحديث
const { ModernAITextProcessor } = require('./advanced-ai-system');
const operationRouter = require('./operation-api'); // استيراد موجه API العمليات
const tempUtils = require('./temp-utils'); // استيراد وظائف إدارة الملفات المؤقتة
const session = require('express-session');
const { initializeApp } = require('firebase/app');
const { getFirestore, collection, query, where, getDocs } = require('firebase/firestore');

// تخزين اتصالات Server-Sent Events للعملاء
const sseClients = new Map(); // Map<accountName, Set<response objects>>

// دالة لإرسال تحديثات حالة الرسائل لجميع العملاء المتصلين
function broadcastMessageStatusUpdate(accountName, statusUpdate) {
    const clients = sseClients.get(accountName);
    if (clients && clients.size > 0) {
        const data = JSON.stringify(statusUpdate);
        console.log(`Broadcasting message status update for ${accountName}:`, data);

        // إرسال التحديث لجميع العملاء المتصلين
        clients.forEach(client => {
            try {
                client.write(`event: messageStatusUpdate\ndata: ${data}\n\n`);
            } catch (error) {
                console.error('Error sending SSE message:', error);
                // إزالة العميل المعطوب
                clients.delete(client);
            }
        });
    }
}

const app = express();
app.use(bodyParser.json());
app.use(cors());

// إعداد multer لرفع الملفات
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
}

const upload = multer({
    dest: 'uploads/', // مجلد مؤقت للملفات المرفوعة
    limits: {
        fileSize: 50 * 1024 * 1024, // حد أقصى 50MB لكل ملف
        files: 50 // حد أقصى 50 ملف
    }
});

// إعداد الجلسات
app.use(session({
    secret: 'whatsappnode_super_secret_key', // استخدم مفتاح قوي في الإنتاج
    resave: false,
    saveUninitialized: false,
    cookie: {
        httpOnly: true,
        secure: false, // true إذا كنت تستخدم https
        maxAge: 1000 * 60 * 60 * 2 // ساعتان
    }
}));

// إعدادات فايربيز (نفس الإعدادات من الواجهة الأمامية)
const firebaseConfig = {
    apiKey: "AIzaSyCiTTWM-3bDahO4hdPAoCPqQCiYZlCg9xc",
    authDomain: "login-system-7389c.firebaseapp.com",
    projectId: "login-system-7389c",
    storageBucket: "login-system-7389c.firebasestorage.app",
    messagingSenderId: "942989377510",
    appId: "1:942989377510:web:f5a901a957e9ffa87e6027"
};

const firebaseApp = initializeApp(firebaseConfig);
const db = getFirestore(firebaseApp);

// ميدلوير عالمي لحماية جميع الصفحات (عدا index.html وملفات static)
app.use(async (req, res, next) => {
    const publicFiles = [
        '/index.html',
        '/favicon.ico',
        '/styles.css',
        '/styles/',
        '/public/',
        '/js/',
        '/css/',
        '/images/',
        '/fonts/',
        '/manifest.json',
        '/firebase-messaging-sw.js',
        '/dashboard.html' // السماح بالوصول لهذه الصفحة إذا كانت الجلسة نشطة
    ];
    if (
        req.path === '/' ||
        req.path === '/index.html' ||
        publicFiles.some(f => req.path.startsWith(f)) ||
        req.path.startsWith('/api/login')
    ) {
        return next();
    }
    // تحقق من الجلسة وتاريخ الانتهاء
    if (!req.session || !req.session.loggedIn) {
        if (req.path.endsWith('.html')) {
            return res.redirect('/index.html');
        }
        return res.status(401).json({ error: 'غير مصرح. يجب تسجيل الدخول أولاً.' });
    }
    // تحقق من تاريخ الانتهاء
    if (req.session.endDate) {
        const now = new Date();
        const endDate = new Date(req.session.endDate);
        if (now > endDate) {
            req.session.destroy(() => {});
            if (req.path.endsWith('.html')) {
                return res.redirect('/index.html');
            }
            return res.status(401).json({ error: 'انتهى الاشتراك. يرجى التواصل مع الدعم.' });
        }
    }
    next();
});

// نقطة نهاية لتسجيل الدخول (API)
app.post('/api/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        // تحقق من Firestore بنفس منطق الواجهة الأمامية
        const usersRef = collection(db, "Users");
        const q = query(usersRef, where("username", "==", username), where("password", "==", password));
        const querySnapshot = await getDocs(q);
        if (querySnapshot.empty) {
            return res.status(401).json({ success: false, error: 'اسم المستخدم أو كلمة المرور غير صحيحة.' });
        }
        const userDoc = querySnapshot.docs[0].data();
        let endDate;
        if (userDoc.enddate && typeof userDoc.enddate.toDate === 'function') {
            endDate = userDoc.enddate.toDate();
        } else if (userDoc.enddate && userDoc.enddate._seconds) {
            // دعم Timestamp من فايربيز Node
            endDate = new Date(userDoc.enddate._seconds * 1000);
        } else {
            return res.status(401).json({ success: false, error: 'بيانات انتهاء الاشتراك غير متوفرة أو غير صالحة. يرجى التواصل مع الدعم.' });
        }
        const now = new Date();
        if (now > endDate) {
            return res.status(401).json({ success: false, error: 'انتهى الاشتراك. يرجى التواصل مع الدعم.' });
        }
        // حساب الأيام المتبقية
        const diffTime = Math.abs(endDate.getTime() - now.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        // إنشاء الجلسة
        req.session.loggedIn = true;
        req.session.username = username;
        req.session.endDate = endDate;
        return res.json({ success: true, remainingDays: diffDays });
    } catch (error) {
        return res.status(500).json({ success: false, error: 'حدث خطأ أثناء التحقق من بيانات الدخول.' });
    }
});

// نقطة نهاية لتسجيل الخروج
app.post('/api/logout', (req, res) => {
    req.session.destroy(() => {
        res.json({ success: true });
    });
});

// نقطة نهاية للتحقق من حالة الاشتراك دورياً
app.get('/api/check-subscription-status', async (req, res) => {
    console.log('[/api/check-subscription-status] - Received request.');
    try {
        if (!req.session || !req.session.loggedIn || !req.session.username) {
            console.log('[/api/check-subscription-status] - User not logged in or session invalid.');
            return res.json({ success: true, subscriptionValid: false, message: 'Not logged in or session invalid.' });
        }

        const username = req.session.username;
        console.log(`[/api/check-subscription-status] - Checking subscription for user: ${username}`);
        const usersRef = collection(db, "Users");
        const q = query(usersRef, where("username", "==", username));
        const querySnapshot = await getDocs(q);

        if (querySnapshot.empty) {
            console.log(`[/api/check-subscription-status] - User '${username}' not found in database. Destroying session.`);
            req.session.destroy(() => {}); // تدمير الجلسة لأسباب أمنية
            return res.json({ success: true, subscriptionValid: false, message: 'User not found or invalid.' });
        }

        const userDoc = querySnapshot.docs[0].data();
        let endDate;

        if (userDoc.enddate && typeof userDoc.enddate.toDate === 'function') {
            endDate = userDoc.enddate.toDate();
        } else if (userDoc.enddate && userDoc.enddate._seconds) {
            endDate = new Date(userDoc.enddate._seconds * 1000);
        } else {
            console.log(`[/api/check-subscription-status] - Subscription end date missing or invalid for user: ${username}. Destroying session.`);
            req.session.destroy(() => {});
            return res.json({ success: true, subscriptionValid: false, message: 'Subscription end date missing or invalid.' });
        }

        const now = new Date();
        console.log(`[/api/check-subscription-status] - Current Date: ${now.toISOString()}, End Date for ${username}: ${endDate.toISOString()}`);

        if (now > endDate) {
            console.log(`[/api/check-subscription-status] - Subscription expired for user: ${username}. Destroying session.`);
            req.session.destroy(() => {}); // تدمير الجلسة تلقائياً عند انتهاء الاشتراك
            return res.json({ success: true, subscriptionValid: false, message: 'Subscription expired.' });
        } else {
            console.log(`[/api/check-subscription-status] - Subscription is valid for user: ${username}.`);
            // تحديث endDate في الجلسة للتأكد من أنه يطابق آخر بيانات من Firebase
            req.session.endDate = endDate; 
            return res.json({ success: true, subscriptionValid: true });
        }
    } catch (error) {
        console.error('[/api/check-subscription-status] - Error in check-subscription-status API:', error);
        return res.status(500).json({ success: false, error: 'Internal server error during subscription check.' });
    }
});

// استيراد وتسجيل موجه API الرسائل
const messageApiRouter = require('./message-api');
const { getRecipientInfoByMessageId } = require('./message-api');
app.use('/api', messageApiRouter);

// إضافة router لـ API العمليات
app.use('/api', operationRouter);

// ميدلوير للتشخيص وتتبع الطلبات المتعلقة بالعمليات
app.use('/api/operation', (req, res, next) => {
    console.log(`[${new Date().toISOString()}] طلب للعملية: ${req.path} - بطريقة: ${req.method}`);
    next();
});

// ميدلوير للتشخيص وتتبع الطلبات المتعلقة بإرسال الرسائل
app.use('/api/send-', (req, res, next) => {
    console.log(`[${new Date().toISOString()}] طلب إرسال رسالة: ${req.path} - بطريقة: ${req.method}`);
    next();
});

// كائن لتخزين حالة اتصالات واتساب
const connections = {}; // سيحتوي الآن على { client, qr, pairingCode, status, info, phoneNumber }

// تخزين كائن connections في app لاستخدامه في API
app.set('whatsappConnections', connections);

// استضافة الملفات الثابتة
app.use(express.static(path.join(__dirname, 'public')));

const PORT = process.env.PORT || 3045;

// إضافة مهمة دورية لتنظيف الملفات المؤقتة القديمة
setInterval(() => {
    try {
        const tempUtils = require('./temp-utils');
        const cleanedCount = tempUtils.cleanupOldTempFiles();
        if (cleanedCount > 0) {
            console.log(`تم تنظيف ${cleanedCount} ملفات مؤقتة قديمة بواسطة المهمة الدورية`);
        }
    } catch (error) {
        console.error('Error in scheduled cleanup task:', error);
    }
}, 3600000); // تنفيذ المهمة كل ساعة

// إضافة مسار لصفحة تفاصيل الحساب
app.get('/account-details.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'account-details.html'));
});

// إضافة مسار لصفحة إرسال الرسائل
app.get('/send-message.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'send-message.html'));
});

// نقطة نهاية Server-Sent Events لتحديثات حالة الرسائل
app.get('/api/message-status-updates/:accountName', (req, res) => {
    const { accountName } = req.params;

    console.log(`SSE connection established for account: ${accountName}`);

    // إعداد headers لـ SSE
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

    // إرسال رسالة اتصال أولية
    res.write(`event: connected\ndata: ${JSON.stringify({ message: 'Connected to message status updates', accountName })}\n\n`);

    // إضافة العميل إلى قائمة العملاء المتصلين
    if (!sseClients.has(accountName)) {
        sseClients.set(accountName, new Set());
    }
    sseClients.get(accountName).add(res);

    // التعامل مع إغلاق الاتصال
    req.on('close', () => {
        console.log(`SSE connection closed for account: ${accountName}`);
        const clients = sseClients.get(accountName);
        if (clients) {
            clients.delete(res);
            if (clients.size === 0) {
                sseClients.delete(accountName);
            }
        }
    });

    // إرسال heartbeat كل 30 ثانية للحفاظ على الاتصال
    const heartbeat = setInterval(() => {
        try {
            res.write(`event: heartbeat\ndata: ${JSON.stringify({ timestamp: Date.now() })}\n\n`);
        } catch (error) {
            console.error('Error sending heartbeat:', error);
            clearInterval(heartbeat);
            const clients = sseClients.get(accountName);
            if (clients) {
                clients.delete(res);
            }
        }
    }, 30000);

    // تنظيف heartbeat عند إغلاق الاتصال
    req.on('close', () => {
        clearInterval(heartbeat);
    });
});

// وظيفة لإنشاء اتصال واتساب باستخدام whatsapp-web.js
async function createWhatsAppConnection(accountName, phoneNumberForPairing = null) {
    console.log(`Attempting to create WhatsApp connection for: ${accountName}`);

    // التحقق من وجود اتصال سابق
    if (connections[accountName] && connections[accountName].client) {
        console.log(`Connection already exists for ${accountName}, checking status...`);
        const status = connections[accountName].status;
        if (status === 'connected' || status === 'authenticated') {
            console.log(`Active connection found for ${accountName}, reusing...`);
            return connections[accountName];
        }
    }

    try {
        // إنشاء مجلد جلسة إذا لم يكن موجوداً
        const sessionDir = `./sessions/${accountName}`;
        if (!fs.existsSync(sessionDir)) {
            fs.mkdirSync(sessionDir, { recursive: true });
        }

        // حفظ معلومات الحساب الأولية في JSON إذا لم تكن موجودة بالفعل
        const savedAccountInfo = loadAccountData(accountName);
        if (!savedAccountInfo) {
            const initialAccountInfo = {
                name: accountName,
                number: phoneNumberForPairing || '',
                status: 'initializing',
                createdAt: new Date().toISOString()
            };
            saveAccountData(accountName, initialAccountInfo);
            console.log(`Initial account data saved for ${accountName}`);
        }

        // مسار مجلد الجلسة لـ LocalAuth
        const wwebjsSessionPath = path.join(__dirname, '.wwebjs_auth', `session-${accountName}`);
        console.log(`Session path for ${accountName}: ${wwebjsSessionPath}`);

        const client = new Client({
            authStrategy: new LocalAuth({ clientId: accountName, dataPath: path.join(__dirname, '.wwebjs_auth') }),
            puppeteer: {
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--single-process',
                    '--disable-gpu'
                ],
            },
        });

        // تخزين معلومات الاتصال الأولية
        connections[accountName] = {
            client,
            sock: client, // إضافة sock كمرجع للعميل
            phoneNumber: phoneNumberForPairing,
            qr: '',
            pairingCode: '',
            status: 'initializing',
            info: loadAccountData(accountName),
            lastConnect: new Date().toISOString()
        };

        client.on('qr', async (qr) => {
            console.log(`QR RECEIVED for ${accountName}`);
            // إذا كنا في وضع طلب رمز الاقتران، لا نعتمد على QR بنفس الطريقة
            if (!connections[accountName].pairingCode) {
                try {
                connections[accountName].qr = await qrcode.toDataURL(qr);
                connections[accountName].status = 'qr_ready';
                // تحديث حالة الحساب في ملف JSON
                const accountInfo = loadAccountData(accountName);
                if (accountInfo) {
                    accountInfo.status = 'qr_ready';
                    saveAccountData(accountName, accountInfo);
                }
                } catch (qrErr) {
                    console.error(`Error generating QR for ${accountName}:`, qrErr);
                    connections[accountName].status = 'qr_error';
                    // تحديث حالة الحساب في ملف JSON
                    const accountInfo = loadAccountData(accountName);
                    if (accountInfo) {
                        accountInfo.status = 'qr_error';
                        saveAccountData(accountName, accountInfo);
                    }
                }
            }
        });

        client.on('ready', async () => {
            console.log(`Client is ready for ${accountName}!`);

            // تحديث حالة الاتصال
            if (connections[accountName]) {
                connections[accountName].status = 'connected';
                connections[accountName].qr = '';
                connections[accountName].pairingCode = '';
                connections[accountName].sock = client;
                connections[accountName].lastConnect = new Date().toISOString();

                try {
                    const clientInfo = client.info;
                    const profilePicUrl = await client.getProfilePicUrl(clientInfo.wid._serialized).catch(() => null);

                    // تحديث معلومات الحساب
                    const accountInfo = {
                        name: clientInfo.pushname || 'غير معروف',
                        number: convertCusToRealPhone(clientInfo.wid._serialized),
                        profilePictureUrl: profilePicUrl,
                        status: 'connected',
                        jid: clientInfo.wid._serialized,
                        id: clientInfo.wid.user,
                        lastConnected: new Date().toISOString()
                    };

                    connections[accountName].info = accountInfo;
                    saveAccountData(accountName, accountInfo);

                    console.log(`Connection updated and ready for ${accountName}`);
                } catch (error) {
                    console.error(`Error updating connection info for ${accountName}:`, error);
                }
            } else {
                console.error(`Connection object not found for ${accountName} in ready event`);
            }
        });

        client.on('authenticated', (session) => {
            console.log(`AUTHENTICATED for ${accountName}`);
            connections[accountName].status = 'authenticated';
            // تحديث الحالة في ملف JSON
            const accountInfo = loadAccountData(accountName);
            if (accountInfo) {
                accountInfo.status = 'authenticated';
                accountInfo.lastAuthenticated = new Date().toISOString();
                saveAccountData(accountName, accountInfo);
            }
        });

        client.on('auth_failure', msg => {
            console.error(`AUTHENTICATION FAILURE for ${accountName}:`, msg);
            connections[accountName].status = 'auth_failure';
            // تحديث الحالة في ملف JSON
            const accountInfo = loadAccountData(accountName);
            if (accountInfo) {
                accountInfo.status = 'auth_failure';
                accountInfo.lastAuthFailure = new Date().toISOString();
                accountInfo.authFailureMessage = msg;
                saveAccountData(accountName, accountInfo);
            }
        });

        // إضافة استماع لأحداث message_ack لتتبع حالة الرسائل
        client.on('message_ack', (message, ack) => {
            console.log(`Message ACK received for ${accountName}:`, {
                messageId: message.id.id,
                ack: ack,
                from: message.from,
                to: message.to
            });

            // تحويل رقم ACK إلى نص
            let status = 'unknown';
            switch (ack) {
                case -1:
                    status = 'failed';
                    break;
                case 0:
                    status = 'pending';
                    break;
                case 1:
                    status = 'sent';
                    break;
                case 2:
                    status = 'delivered';
                    break;
                case 3:
                    status = 'read';
                    break;
                case 4:
                    status = 'played';
                    break;
            }

            // البحث عن معلومات المستلم من معرف الرسالة
            const recipientInfo = getRecipientInfoByMessageId(message.id.id);

            // إرسال تحديث الحالة لجميع العملاء المتصلين
            broadcastMessageStatusUpdate(accountName, {
                messageId: message.id.id,
                status: status,
                ack: ack,
                timestamp: Date.now(),
                recipient: message.to,
                operationId: recipientInfo?.operationId,
                recipientIndex: recipientInfo?.recipientInfo?.recipientIndex,
                messageType: recipientInfo?.recipientInfo?.messageType
            });
        });

        // مستمع الرسائل الواردة للرد التلقائي المتكامل مع الذكاء الاصطناعي
        client.on('message', async (message) => {
            try {
                // تجاهل الرسائل المرسلة من نفس الحساب
                if (message.fromMe) return;

                // تجاهل رسائل المجموعات (يمكن تعديل هذا لاحقاً)
                if (message.from.includes('@g.us')) return;

                // تجاهل الرسائل من الحالات
                if (message.from.includes('status@broadcast')) return;

                console.log(`🔔 Received message from ${message.from} for account ${accountName}: "${message.body}"`);

                // تحميل إعدادات وضع الرد التلقائي
                const modeSettingsPath = path.join(autoReplyDir, 'mode.json');
                let autoReplyMode = 'global'; // الوضع الافتراضي

                if (fs.existsSync(modeSettingsPath)) {
                    try {
                        const modeData = fs.readFileSync(modeSettingsPath, 'utf8');
                        const modeSettings = JSON.parse(modeData);
                        autoReplyMode = modeSettings.mode || 'global';
                    } catch (error) {
                        console.error('Error reading auto-reply mode settings:', error);
                    }
                }

                console.log(`📋 Auto-reply mode: ${autoReplyMode}`);

                // === المرحلة الأولى: فحص النظام المتقدم مع الذكاء الاصطناعي ===
                let advancedReplyFound = false;

                // فحص الإعدادات المتقدمة حسب الوضع المحدد
                if (autoReplyMode === 'account') {
                    // في الوضع الخاص، فحص إعدادات الحساب المتقدمة أولاً
                    const accountAdvancedSettings = loadAdvancedAutoReplySettings(accountName);
                    if (accountAdvancedSettings && accountAdvancedSettings.enabled) {
                        console.log(`🤖 Checking account-specific advanced auto-reply for: ${accountName}`);
                        advancedReplyFound = await processAdvancedAutoReply(message, accountAdvancedSettings, accountName, client);
                    }
                } else {
                    // في الوضع العام، فحص الإعدادات العامة المتقدمة
                    const globalAdvancedSettings = loadAdvancedAutoReplySettings();
                    if (globalAdvancedSettings && globalAdvancedSettings.enabled) {
                        console.log(`🌐 Checking global advanced auto-reply`);
                        advancedReplyFound = await processAdvancedAutoReply(message, globalAdvancedSettings, accountName, client);
                    }
                }

                // إذا تم العثور على رد متقدم، إنهاء المعالجة
                if (advancedReplyFound) {
                    console.log(`✅ Advanced auto-reply sent successfully, skipping regular auto-reply`);
                    return;
                }

                console.log(`⏭️ No advanced auto-reply match found, checking regular auto-reply...`);

                // === المرحلة الثانية: فحص النظام العادي ===
                let autoReplySettings = null;
                let useGlobal = false;

                // تطبيق منطق الرد التلقائي حسب الوضع المحدد
                if (autoReplyMode === 'global') {
                    // في الوضع العام، استخدام الإعدادات العامة فقط
                    const globalSettings = loadAutoReplySettings('global');
                    if (globalSettings && globalSettings.enabled) {
                        autoReplySettings = globalSettings;
                        useGlobal = true;
                        console.log(`🌐 Using global auto-reply settings`);
                    } else {
                        console.log(`❌ Global auto-reply is disabled`);
                    }
                } else {
                    // في الوضع الخاص، استخدام الإعدادات الخاصة بالحساب فقط
                    const accountSettings = loadAutoReplySettings('account', accountName);
                    if (accountSettings && accountSettings.enabled) {
                        autoReplySettings = accountSettings;
                        console.log(`👤 Using account-specific auto-reply settings for: ${accountName}`);
                    } else {
                        console.log(`❌ Account-specific auto-reply is disabled for: ${accountName}`);
                    }
                }

                // إذا لم تكن هناك إعدادات رد تلقائي مفعلة، إنهاء المعالجة
                if (!autoReplySettings) {
                    console.log(`❌ No auto-reply settings found or enabled for account ${accountName}`);
                    return;
                }

                console.log(`📤 Processing regular auto-reply for account ${accountName}...`);

                // الحصول على معلومات المرسل
                const contact = await message.getContact();
                const senderName = contact.pushname || contact.name || contact.number || 'صديق';

                // معالجة الرد التلقائي المتعدد الأنواع مع تحسينات الذكاء الاصطناعي
                const messageData = autoReplySettings.message;
                let sentMessages = [];

                // إرسال النص إذا كان موجوداً
                if (messageData.text && messageData.text.trim()) {
                    try {
                        // تطبيق معالجة النصوص المتقدمة
                        const textProcessor = new AdvancedTextProcessor();
                        let textMessage = messageData.text.replace(/{name}/g, senderName);

                        // تطبيع الأرقام في النص إذا كان يحتوي على أرقام عربية
                        textMessage = textProcessor.normalizeNumbers(textMessage);

                        // التحقق من حالة العميل قبل الإرسال
                        if (client && client.info && client.info.wid) {
                            await client.sendMessage(message.from, textMessage);
                            sentMessages.push('text');
                            console.log(`✅ Auto-reply text sent to ${message.from} from account ${accountName}: "${textMessage}"`);
                        } else {
                            console.error(`❌ Client not ready for account ${accountName}`);
                        }

                        // تأخير قصير بين الرسائل
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    } catch (error) {
                        console.error(`❌ Error sending auto-reply text to ${message.from}:`, error.message);
                    }
                }

                // إرسال الصورة إذا كانت موجودة
                if (messageData.imagePath && fs.existsSync(messageData.imagePath)) {
                    try {
                        // التحقق من حالة العميل قبل الإرسال
                        if (client && client.info && client.info.wid) {
                            const media = MessageMedia.fromFilePath(messageData.imagePath);
                            await client.sendMessage(message.from, media);
                            sentMessages.push('image');
                            console.log(`✅ Auto-reply image sent to ${message.from} from account ${accountName}`);
                        } else {
                            console.error(`❌ Client not ready for account ${accountName}`);
                        }

                        // تأخير قصير بين الرسائل
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    } catch (error) {
                        console.error(`❌ Error sending auto-reply image to ${message.from}:`, error.message);
                    }
                }

                // إرسال الملف إذا كان موجوداً
                if (messageData.filePath && fs.existsSync(messageData.filePath)) {
                    try {
                        if (client && client.info && client.info.wid) {
                            const media = MessageMedia.fromFilePath(messageData.filePath);
                            await client.sendMessage(message.from, media);
                            sentMessages.push('file');
                            console.log(`✅ Auto-reply file sent to ${message.from} from account ${accountName}`);
                        } else {
                            console.error(`❌ Client not ready for account ${accountName}`);
                        }

                        // تأخير قصير بين الرسائل
                        await new Promise(resolve => setTimeout(resolve, 500));
                    } catch (error) {
                        console.error(`❌ Error sending auto-reply file to ${message.from}:`, error.message);
                    }
                }

                // إرسال ملفات المجلد إذا كانت موجودة
                if (messageData.folderFiles && messageData.folderFiles.length > 0) {
                    for (const filePath of messageData.folderFiles) {
                        if (fs.existsSync(filePath)) {
                            try {
                                if (client && client.info && client.info.wid) {
                                    const media = MessageMedia.fromFilePath(filePath);
                                    await client.sendMessage(message.from, media);
                                    console.log(`✅ Auto-reply folder file sent to ${message.from} from account ${accountName}: ${filePath}`);
                                } else {
                                    console.error(`❌ Client not ready for account ${accountName}`);
                                }

                                // تأخير بين الملفات
                                await new Promise(resolve => setTimeout(resolve, 1000));
                            } catch (error) {
                                console.error(`❌ Error sending folder file ${filePath} to ${message.from}:`, error.message);
                            }
                        }
                    }
                    if (messageData.folderFiles.length > 0) {
                        sentMessages.push('folder');
                    }
                }

                // تسجيل ملخص الرسائل المرسلة
                if (sentMessages.length > 0) {
                    console.log(`🎉 Auto-reply completed successfully for ${message.from} from account ${accountName}. Sent: ${sentMessages.join(', ')}`);
                } else {
                    console.log(`⚠️ No auto-reply content available for ${message.from} from account ${accountName}`);
                }

            } catch (error) {
                console.error(`Error in auto-reply for account ${accountName}:`, error);
            }
        });

        client.on('disconnected', (reason) => {
            console.log(`Client was logged out for ${accountName}:`, reason);
            connections[accountName].status = 'disconnected';
            connections[accountName].sock = null; // مسح sock عند قطع الاتصال
            // تحديث الحالة في ملف JSON
            const accountInfo = loadAccountData(accountName);
            if (accountInfo) {
                accountInfo.status = 'disconnected';
                accountInfo.lastDisconnected = new Date().toISOString();
                accountInfo.disconnectReason = reason;
                saveAccountData(accountName, accountInfo);
            }
        });

        console.log(`Initializing client for ${accountName}...`);
        await client.initialize(); // التهيئة ستبدأ عملية الـ QR أو تنتظر رمز الاقتران إذا طُلب

        // إذا تم توفير رقم هاتف لرمز الاقتران، قم بطلبه الآن بعد تهيئة العميل
        if (phoneNumberForPairing && client) {
            try {
                console.log(`Requesting pairing code for ${accountName} with phone ${phoneNumberForPairing}`);
                // تنظيف رقم الهاتف قبل إرساله
                const cleanedPhoneNumber = phoneNumberForPairing.replace(/[^\d]/g, '');
                const code = await client.requestPairingCode(cleanedPhoneNumber);
                                    if (code) {
                    console.log(`Pairing code for ${accountName} (${cleanedPhoneNumber}): ${code}`);
                    connections[accountName].pairingCode = code;
                    connections[accountName].status = 'pairing_code_ready';
                    // تحديث الحالة في ملف JSON
                    const accountInfo = loadAccountData(accountName);
                    if (accountInfo) {
                        accountInfo.status = 'pairing_code_ready';
                        accountInfo.pairingCodeRequested = new Date().toISOString();
                        saveAccountData(accountName, accountInfo);
                    }
                                    } else {
                    console.error(`Failed to get pairing code for ${accountName}.`);
                    connections[accountName].status = 'pairing_code_error';
                    // تحديث الحالة في ملف JSON
                    const accountInfo = loadAccountData(accountName);
                    if (accountInfo) {
                        accountInfo.status = 'pairing_code_error';
                        accountInfo.pairingCodeError = new Date().toISOString();
                        saveAccountData(accountName, accountInfo);
                    }
                }
            } catch (pairError) {
                console.error(`Error requesting pairing code for ${accountName}:`, pairError);
                connections[accountName].status = 'pairing_code_error';
                // تحديث الحالة في ملف JSON
                const accountInfo = loadAccountData(accountName);
                if (accountInfo) {
                    accountInfo.status = 'pairing_code_error';
                    accountInfo.pairingCodeError = new Date().toISOString();
                    accountInfo.pairingCodeErrorMessage = pairError.message;
                    saveAccountData(accountName, accountInfo);
                }
                                    }
                                } else {
             console.log(`Client initialized for ${accountName}. Waiting for QR or ready state if pairing code was not requested.`);
                                }

        return true;
                            } catch (error) {
        console.error(`Error creating WhatsApp connection for ${accountName}:`, error);
        if (connections[accountName]) {
            connections[accountName].status = 'error';
        }

        // تحديث حالة الخطأ في ملف JSON
        const accountInfo = loadAccountData(accountName);
        if (accountInfo) {
            accountInfo.status = 'error';
            accountInfo.lastError = new Date().toISOString();
            accountInfo.errorMessage = error.message;
            saveAccountData(accountName, accountInfo);
        }

        return false;
    }
}

// API لإنشاء اتصال جديد
app.post('/api/create-connection', async (req, res) => {
    try {
        const { accountName, phoneNumber, connectionType } = req.body;

        if (!accountName) {
            return res.status(400).json({ error: 'اسم الحساب مطلوب' });
        }

        // إنشاء مجلد الجلسات إذا لم يكن موجوداً
        const sessionDir = `./sessions/${accountName}`;
        if (!fs.existsSync(sessionDir)) {
            fs.mkdirSync(sessionDir, { recursive: true });
        }

        // التحقق مما إذا كان الحساب موجوداً بالفعل في الاتصالات النشطة
        if (connections[accountName] &&
            connections[accountName].status !== 'disconnected' &&
            connections[accountName].status !== 'error' &&
            connections[accountName].status !== 'auth_failure' &&
            connections[accountName].status !== 'pairing_code_error') {
            return res.status(400).json({ error: 'الحساب موجود بالفعل أو قيد المعالجة' });
        }

        // حفظ بيانات الحساب الأولية
        const savedAccountInfo = loadAccountData(accountName);
        if (!savedAccountInfo) {
            // إنشاء معلومات الحساب الأولية
            const initialAccountInfo = {
                name: accountName,
                number: phoneNumber || '',
                status: 'created',
                createdAt: new Date().toISOString(),
                connectionType: connectionType || 'qr' // حفظ نوع الاتصال الذي اختاره المستخدم
            };
            saveAccountData(accountName, initialAccountInfo);
            console.log(`Initial account data created and saved for ${accountName}`);
        } else {
            // تحديث البيانات الموجودة
            savedAccountInfo.number = phoneNumber || savedAccountInfo.number;
            savedAccountInfo.status = 'updating';
            savedAccountInfo.connectionType = connectionType || 'qr';
            savedAccountInfo.lastUpdated = new Date().toISOString();
            saveAccountData(accountName, savedAccountInfo);
            console.log(`Updated existing account data for ${accountName}`);
        }

        // إذا كان المستخدم قد اختار الاتصال برمز الاقتران وتم توفير رقم هاتف
        if (connectionType === 'pairing' && phoneNumber) {
            // إنشاء اتصال جديد مع رقم الهاتف لطلب رمز الاقتران
            const success = await createWhatsAppConnection(accountName, phoneNumber);

            if (success) {
                // انتظار قليلاً للتأكد من تهيئة العميل
                await new Promise(resolve => setTimeout(resolve, 3000));

                // طلب رمز الاقتران
                const result = await requestPairingCodeForAccount(accountName, phoneNumber);

                if (result.success) {
                    return res.json({
                        success: true,
                        accountName,
                        connectionType: 'pairing',
                        pairingCode: result.code,
                        status: connections[accountName].status,
                        message: `رمز الاقتران لـ ${accountName} هو: ${result.code}`
                    });
                } else {
                    return res.status(500).json({
                        error: `فشل في طلب رمز الاقتران: ${result.error}`,
                        connectionType: 'pairing'
                    });
                }
            } else {
                return res.status(500).json({ error: 'حدث خطأ أثناء تهيئة الاتصال لطلب رمز الاقتران' });
            }
        } else {
            // إنشاء اتصال جديد باستخدام QR (الطريقة الافتراضية)
            const success = await createWhatsAppConnection(accountName, null);

            if (success) {
                return res.status(200).json({
                    success: true,
                    accountName,
                    connectionType: 'qr',
                    message: "تم إنشاء الحساب بنجاح. جاري تهيئة الاتصال، يرجى التحقق من رمز QR."
                });
            } else {
                return res.status(500).json({ error: 'حدث خطأ أثناء تهيئة الاتصال الأولي' });
            }
        }
    } catch (error) {
        console.error('Error in /api/create-connection:', error);
        return res.status(500).json({ error: 'حدث خطأ أثناء إنشاء الاتصال' });
    }
});

// تعديل دالة requestPairingCodeForAccount في ملف server.js
async function requestPairingCodeForAccount(accountName, phoneNumber) {
    try {
        if (!connections[accountName] || !connections[accountName].client) {
            throw new Error('عميل واتساب غير مهيأ');
        }

        // تنظيف رقم الهاتف
        const cleanedPhoneNumber = phoneNumber.replace(/[^\d]/g, '');

        console.log(`طلب رمز اقتران للحساب ${accountName} مع رقم الهاتف ${cleanedPhoneNumber}`);

        // استخدام معلمة واحدة فقط لتجنب مشكلة التعريف في TypeScript
        const code = await connections[accountName].client.requestPairingCode(cleanedPhoneNumber);

        if (!code) {
            throw new Error('فشل في الحصول على رمز الاقتران (رمز فارغ)');
        }

        // تحديث حالة الاتصال
        connections[accountName].pairingCode = code;
        connections[accountName].status = 'pairing_code_ready';
        connections[accountName].phoneNumber = cleanedPhoneNumber;

        console.log(`تم الحصول على رمز الاقتران بنجاح للحساب ${accountName}: ${code}`);

        return { success: true, code, accountName };
    } catch (error) {
        console.error(`خطأ في طلب رمز الاقتران للحساب ${accountName}:`, error);
        return { success: false, error: error.message, accountName };
    }
}

// تحديث مسار الحصول على رمز الاقتران لجعله أكثر بساطة
app.get('/api/get-pairing-code/:accountName', async (req, res) => {
    try {
        const { accountName } = req.params;

        // التحقق من وجود الحساب
        let savedAccountInfo = loadAccountData(accountName);

        if (!savedAccountInfo) {
            const phoneNumber = req.query.phoneNumber;
            if (!phoneNumber) {
                return res.status(400).json({
                    error: 'لم يتم العثور على حساب بهذا الاسم. يرجى توفير رقم الهاتف لإنشاء حساب جديد.'
                });
            }

            // إنشاء بيانات الحساب قبل محاولة الاتصال
            savedAccountInfo = {
                name: accountName,
                number: phoneNumber,
                status: 'created',
                createdAt: new Date().toISOString()
            };

            // تأكد من وجود مجلد الجلسة
            const sessionDir = `./sessions/${accountName}`;
            if (!fs.existsSync(sessionDir)) {
                fs.mkdirSync(sessionDir, { recursive: true });
            }

            saveAccountData(accountName, savedAccountInfo);
            console.log(`Created new account data for ${accountName} with phone ${phoneNumber} from get-pairing-code`);
        }

        if (!connections[accountName]) {
            // استخدام رقم الهاتف المحفوظ أو المقدم في الطلب
            const phoneNumber = savedAccountInfo?.number || req.query.phoneNumber;

            if (!phoneNumber) {
                return res.status(400).json({
                    error: 'لم يتم العثور على رقم هاتف محفوظ لهذا الحساب. يرجى توفير رقم الهاتف عبر المعلمة phoneNumber'
                });
            }

            // إنشاء اتصال جديد باستخدام رقم الهاتف
            const success = await createWhatsAppConnection(accountName, phoneNumber);

            if (!success) {
                return res.status(500).json({
                    error: 'فشل في إنشاء اتصال جديد للحساب. يرجى مراجعة السجلات.'
                });
            }

            // انتظار قليلاً للتأكد من تهيئة العميل
            await new Promise(resolve => setTimeout(resolve, 3000));
        }

        // محاولة طلب رمز الاقتران
        const phoneNumber = connections[accountName]?.phoneNumber ||
                           savedAccountInfo?.number ||
                           req.query.phoneNumber;

        if (!phoneNumber) {
            return res.status(400).json({
                error: 'لم يتم العثور على رقم هاتف محفوظ لهذا الحساب. يرجى توفير رقم الهاتف عبر المعلمة phoneNumber'
            });
        }

        const result = await requestPairingCodeForAccount(accountName, phoneNumber);

        if (result.success) {
            return res.json({
                success: true,
                pairingCode: result.code,
                status: connections[accountName].status,
                accountName: accountName,
                message: `رمز الاقتران لـ ${accountName} هو: ${result.code}`
            });
        } else {
            return res.status(500).json({
                error: `فشل في طلب رمز الاقتران: ${result.error}`
            });
        }
    } catch (error) {
        console.error('Error in /api/get-pairing-code:', error);
        return res.status(500).json({ error: 'حدث خطأ داخلي أثناء طلب رمز الاقتران' });
    }
});

// تحسين مسار طلب رمز الاقتران مع الهاتف
app.post('/api/request-pairing-code/:accountName', async (req, res) => {
    try {
        const { accountName } = req.params;
        const { phoneNumber } = req.body;

        if (!accountName) {
            return res.status(400).json({ error: 'اسم الحساب مطلوب' });
        }
        if (!phoneNumber) {
            return res.status(400).json({ error: 'رقم الهاتف مطلوب لطلب رمز الاقتران' });
        }

        // التحقق من وجود الحساب
        let savedAccountInfo = loadAccountData(accountName);

        // إذا لم يكن الحساب موجوداً، قم بإنشائه أولاً
        if (!savedAccountInfo) {
            // إنشاء معلومات الحساب الأولية
            savedAccountInfo = {
                name: accountName,
                number: phoneNumber,
                status: 'created',
                createdAt: new Date().toISOString(),
                connectionType: 'pairing'
            };
            // تأكد من وجود مجلد الجلسة
            const sessionDir = `./sessions/${accountName}`;
            if (!fs.existsSync(sessionDir)) {
                fs.mkdirSync(sessionDir, { recursive: true });
            }
            saveAccountData(accountName, savedAccountInfo);
            console.log(`Created new account data for ${accountName} with phone ${phoneNumber}`);
        } else {
            // تحديث رقم الهاتف إذا تم توفيره
            savedAccountInfo.number = phoneNumber;
            savedAccountInfo.connectionType = 'pairing';
            savedAccountInfo.lastUpdated = new Date().toISOString();
            saveAccountData(accountName, savedAccountInfo);
            console.log(`Updated phone number for ${accountName} to ${phoneNumber}`);
        }

        // التحقق مما إذا كان الاتصال موجودًا بالفعل
        if (connections[accountName] &&
            (connections[accountName].status === 'connected' || connections[accountName].status === 'authenticated')) {
            return res.status(400).json({
                error: 'الحساب متصل بالفعل. يجب قطع الاتصال أولاً إذا كنت تريد طلب رمز اقتران جديد.',
                connectionType: 'pairing'
            });
        }

        // إذا كان هناك اتصال سابق في حالة خطأ، قم بحذفه
        if (connections[accountName] &&
            (connections[accountName].status === 'error' ||
             connections[accountName].status === 'auth_failure' ||
             connections[accountName].status === 'pairing_code_error' ||
             connections[accountName].status === 'disconnected')) {
            try {
                if (connections[accountName].client) {
                    await connections[accountName].client.destroy().catch(e => console.warn(`Destroy error: ${e.message}`));
                }
                delete connections[accountName];
            } catch (e) {
                console.warn(`Failed to clean up previous connection: ${e.message}`);
            }
        }

        // إنشاء اتصال جديد مع رقم الهاتف
        const success = await createWhatsAppConnection(accountName, phoneNumber);

        if (!success) {
            return res.status(500).json({
                error: 'فشل في إنشاء اتصال جديد للحساب. يرجى مراجعة السجلات.',
                connectionType: 'pairing'
            });
        }

        // انتظار قليلاً للتأكد من تهيئة العميل
        await new Promise(resolve => setTimeout(resolve, 3000));

        // طلب رمز الاقتران
        const result = await requestPairingCodeForAccount(accountName, phoneNumber);

        if (result.success) {
            return res.json({
                success: true,
                pairingCode: result.code,
                status: connections[accountName].status,
                accountName: accountName,
                connectionType: 'pairing',
                message: `رمز الاقتران لـ ${accountName} هو: ${result.code}`
            });
        } else {
            return res.status(500).json({
                error: `فشل في طلب رمز الاقتران: ${result.error}`,
                connectionType: 'pairing'
            });
        }
    } catch (error) {
        console.error(`Error in /api/request-pairing-code/${req.params.accountName}:`, error);
        return res.status(500).json({
            error: `حدث خطأ أثناء طلب رمز الاقتران: ${error.message}`,
            connectionType: 'pairing'
        });
    }
});

// API لإعادة الاتصال بحساب موجود
app.post('/api/reconnect/:accountName', async (req, res) => {
    try {
        const { accountName } = req.params;
        const sessionDir = path.join(__dirname, '.wwebjs_auth', `session-${accountName}`);

        if (!fs.existsSync(sessionDir)) {
             // إذا لم يكن هناك مجلد جلسة، فهذا يعني أنه اتصال جديد
            console.log(`No session folder found for ${accountName}, attempting to create a new connection.`);
            // يمكنك اختيار الحصول على phoneNumber من req.body إذا لزم الأمر
            const phoneNumber = req.body.phoneNumber || (connections[accountName]?.phoneNumber || '');
            await createWhatsAppConnection(accountName, phoneNumber);
            return res.status(200).json({
                success: true,
                message: `بدء اتصال جديد لـ ${accountName}. يرجى التحقق من QR.`,
                accountName
            });
        }

        // إذا كان الاتصال موجودًا بالفعل ويحاول إعادة الاتصال
        if (connections[accountName] && connections[accountName].client) {
            console.log(`Attempting to re-initialize existing client for ${accountName}`);
            try {
                await connections[accountName].client.initialize();
                 return res.status(200).json({
                    success: true,
                    message: `تمت محاولة إعادة تهيئة الاتصال لـ ${accountName}.`,
                    accountName
                });
            } catch (initError) {
                console.error(`Error re-initializing client for ${accountName}:`, initError);
                // إذا فشلت إعادة التهيئة، قد نرغب في محاولة إنشاء اتصال جديد بالكامل
                delete connections[accountName]; // إزالة القديم
                const phoneNumber = req.body.phoneNumber || '';
                await createWhatsAppConnection(accountName, phoneNumber);
                return res.status(200).json({
                    success: true,
                    message: `فشلت إعادة التهيئة، بدء اتصال جديد لـ ${accountName}. يرجى التحقق من QR.`,
                    accountName
                });
            }
        } else {
            // إذا لم يكن هناك كائن اتصال، أنشئ واحدًا جديدًا (سيستخدم LocalAuth الجلسة المحفوظة إذا وجدت)
            console.log(`No active client object for ${accountName}, creating new connection (will use saved session if available).`);
            const phoneNumber = req.body.phoneNumber || '';
            await createWhatsAppConnection(accountName, phoneNumber);
        return res.status(200).json({
            success: true,
                message: `تمت محاولة الاتصال بالحساب ${accountName} باستخدام الجلسة المحفوظة إن وجدت.`,
                accountName
        });
        }

    } catch (error) {
        console.error('Error reconnecting:', error);
        return res.status(500).json({ error: 'حدث خطأ أثناء إعادة الاتصال' });
    }
});

// API للحصول على QR code (لم تعد هناك حاجة لرمز الاقتران بنفس الطريقة)
app.get('/api/get-qr/:accountName', (req, res) => {
    const { accountName } = req.params;

    if (!connections[accountName]) {
        return res.status(404).json({ error: 'الحساب غير موجود أو لم تبدأ عملية الاتصال بعد' });
    }

    const conn = connections[accountName];

    // التحقق أولاً من رمز الاقتران إذا كان متاحًا
    if (conn.status === 'pairing_code_ready' && conn.pairingCode) {
        return res.json({
            pairingCode: conn.pairingCode,
            qr: '', // لا يوجد QR في هذه الحالة
            status: conn.status,
            message: `أدخل رمز الاقتران هذا في هاتفك: ${conn.pairingCode}`
        });
    }
    // التحقق مما إذا كان QR لا يزال متاحًا (لم يتم الاتصال بعد ولم يتم طلب رمز اقتران)
    else if (conn.status === 'qr_ready' && conn.qr) {
        return res.json({
            qr: conn.qr,
            pairingCode: '',
            status: conn.status,
            message: 'امسح رمز QR هذا بهاتفك.'
        });
    } else if (conn.status === 'connected' || conn.status === 'authenticated') {
         return res.json({
            qr: '',
            pairingCode: '',
            status: conn.status,
            message: 'Client is already connected or authenticated.'
        });
    } else {
        // حالات أخرى مثل initializing, error, auth_failure, pairing_code_error
        return res.json({
            qr: conn.qr || '',
            pairingCode: conn.pairingCode || '',
            status: conn.status,
            message: 'Client in a different state or error occurred. Check status for details.'
        });
    }
});

// API للحصول على حالة الاتصال
app.get('/api/connection-status/:accountName', (req, res) => {
    const { accountName } = req.params;

    try {
        if (!connections[accountName]) {
            // التحقق من وجود مجلد الجلسة
            const sessionDir = `./sessions/${accountName}`;
            if (fs.existsSync(sessionDir)) {
                // محاولة قراءة معلومات الحساب من ملف JSON
                const savedAccountInfo = loadAccountData(accountName);

                return res.json({
                    exists: true,
                    connected: false,
                    status: 'disconnected',
                    info: savedAccountInfo,
                    message: 'الحساب موجود ولكنه غير متصل حاليًا'
                });
            }
            return res.status(404).json({ error: 'الحساب غير موجود' });
        }

        return res.json({
            exists: true,
            connected: connections[accountName].status === 'connected',
            status: connections[accountName].status,
            info: connections[accountName].info
        });
    } catch (error) {
        console.error(`Error checking connection status for ${accountName}:`, error);
        return res.status(500).json({ error: 'حدث خطأ أثناء التحقق من حالة الاتصال' });
    }
});

// API لتحميل الحسابات المتاحة من مجلدات الجلسات
app.get('/api/available-accounts', (req, res) => {
    try {
        // قراءة مجلدات الجلسات
        const sessionDirs = fs.readdirSync('./sessions');

        // تجميع معلومات الحسابات
        const accounts = sessionDirs.map(dir => {
            const isConnected = !!connections[dir];
            let accountInfo = null;
            let phoneNumber = '';

            // محاولة قراءة معلومات الحساب من ملف JSON
            const savedAccountInfo = loadAccountData(dir);

            if (savedAccountInfo) {
                // استخدام المعلومات المحفوظة في ملف JSON
                accountInfo = savedAccountInfo;
                phoneNumber = savedAccountInfo.number || '';
            } else {
                // محاولة قراءة رقم الهاتف من الملف القديم إذا لم يكن هناك ملف JSON
                const phoneNumberPath = path.join('./sessions', dir, 'phone_number.txt');
                if (fs.existsSync(phoneNumberPath)) {
                    try {
                        phoneNumber = fs.readFileSync(phoneNumberPath, 'utf8');
                    } catch (err) {
                        console.error(`Error reading phone number for ${dir}:`, err);
                    }
                }
            }

            return {
                accountName: dir,
                connected: isConnected,
                status: isConnected ? connections[dir].status : (accountInfo ? accountInfo.status || 'disconnected' : 'disconnected'),
                info: isConnected ? connections[dir].info : accountInfo,
                phoneNumber: isConnected ? connections[dir].phoneNumber : phoneNumber
            };
        });

        return res.json({ accounts });
    } catch (error) {
        console.error('Error loading available accounts:', error);
        return res.status(500).json({ error: 'حدث خطأ أثناء تحميل الحسابات المتاحة' });
    }
});

// API للحصول على معلومات الحساب
app.get('/api/account-info/:accountName', (req, res) => {
    const { accountName } = req.params;

    try {
        // التحقق من وجود الحساب في الاتصالات النشطة
        if (connections[accountName]) {
            return res.json({ info: connections[accountName].info });
        }

        // إذا لم يكن الحساب متصلاً، حاول قراءة المعلومات من ملف JSON
        const savedAccountInfo = loadAccountData(accountName);
        if (savedAccountInfo) {
            return res.json({ info: savedAccountInfo });
        }

        // إذا لم يتم العثور على معلومات الحساب
        return res.status(404).json({ error: 'الحساب غير موجود أو لا توجد معلومات متاحة' });
    } catch (error) {
        console.error(`Error fetching account info for ${accountName}:`, error);
        return res.status(500).json({ error: 'حدث خطأ أثناء جلب معلومات الحساب' });
    }
});

// API للحصول على تفاصيل الحساب
app.get('/api/account-details/:accountName', (req, res) => {
    const { accountName } = req.params;

    try {
        // التحقق من وجود الحساب
        if (!connections[accountName] && !fs.existsSync(`./sessions/${accountName}`)) {
            return res.status(404).json({ error: 'الحساب غير موجود' });
        }

        // الحصول على معلومات الحساب من الاتصال النشط أو من ملف JSON
        let accountInfo = null;
        let connected = false;
        let status = 'disconnected';

        if (connections[accountName]) {
            accountInfo = connections[accountName].info;
            connected = connections[accountName].status === 'connected';
            status = connections[accountName].status;
        } else {
            // محاولة قراءة المعلومات من ملف JSON
            accountInfo = loadAccountData(accountName);
        }

        if (!accountInfo) {
            return res.status(404).json({ error: 'لا توجد معلومات متاحة للحساب' });
        }

        // إعداد البيانات للإرجاع
        const responseData = {
            name: accountInfo.name || 'غير معروف',
            verifiedName: accountInfo.verifiedName || '',
            id: accountInfo.id || accountInfo.jid || '',
            lid: accountInfo.lid || null, // إرجاع معرف LID إذا كان موجودًا
            number: accountInfo.number || '',
            profilePictureUrl: accountInfo.profilePictureUrl || null,
            status: 'connected',
            jid: accountInfo.jid || '',
            user: accountInfo.user || {
                server: '',
                user: '',
                _serialized: ''
            },
            platform: accountInfo.platform || '',
            connected,
            status
        };

        return res.json(responseData);
    } catch (error) {
        console.error(`Error fetching account details for ${accountName}:`, error);
        return res.status(500).json({ error: 'حدث خطأ أثناء جلب تفاصيل الحساب' });
    }
});

// API لاستخراج بيانات المجموعات من ملف groups_data.json
app.get('/api/extract-groups-data/:accountName', async (req, res) => {
    const { accountName } = req.params;

    try {
        // التحقق من وجود الحساب
        if (!connections[accountName]) {
            return res.status(404).json({ error: 'الحساب غير موجود' });
        }

        // التحقق من اتصال الحساب
        if (connections[accountName].status !== 'connected') {
            return res.status(400).json({ error: 'الحساب غير متصل' });
        }

        const client = connections[accountName].client;

        // الحصول على معرف المستخدم الحالي
        const currentUserWid = client.info.wid._serialized;
        console.log(`استخراج بيانات المجموعات للحساب ${accountName}`);
        console.log(`معرف المستخدم الحالي: ${currentUserWid}`);

        // الحصول على جميع المحادثات
        const chats = await client.getChats();

        // تصفية المحادثات للحصول على المجموعات فقط
        const groups = chats.filter(chat => chat.isGroup);

        console.log(`تم العثور على ${groups.length} مجموعة للمستخدم ${accountName}`);

        // معالجة كل مجموعة لاستخراج البيانات المطلوبة
        const groupsData = await Promise.all(groups.map(async (group) => {
            try {
                // الحصول على المشاركين في المجموعة مع معلومات إضافية
                const participants = group.participants.map(participant => {
                    return {
                        id: participant.id._serialized,
                        isAdmin: participant.isAdmin || false,
                        isSuperAdmin: participant.isSuperAdmin || false,
                        admin: participant.isAdmin ? 'admin' : (participant.isSuperAdmin ? 'superadmin' : false)
                    };
                });

                // تحديد إذا كان المستخدم الحالي مشرفًا
                const currentUserParticipant = participants.find(p => p.id === currentUserWid);
                const isCurrentUserAdmin = currentUserParticipant &&
                    (currentUserParticipant.isAdmin || currentUserParticipant.isSuperAdmin);

                // الحصول على رابط المجموعة إذا كان المستخدم مشرفًا
                let inviteCode = null;
                if (isCurrentUserAdmin) {
                    try {
                        inviteCode = await group.getInviteCode();
                    } catch (inviteErr) {
                        console.error(`خطأ في الحصول على رمز الدعوة للمجموعة ${group.name}:`, inviteErr.message);
                    }
                }

                // الحصول على معلومات العضوية المطلوبة إذا كان المستخدم مشرفًا
                let membershipRequests = [];
                if (isCurrentUserAdmin) {
                    try {
                        membershipRequests = await client.getGroupMembershipRequests(group.id._serialized);
                    } catch (membershipErr) {
                        console.error(`خطأ في الحصول على طلبات العضوية للمجموعة ${group.name}:`, membershipErr.message);
                    }
                }

                // إضافة معلومات إضافية للمشاركين
                const enhancedParticipants = participants.map(participant => {
                    let phoneNumber = null;
                    let realPhoneNumber = null;

                    // محاولة استخراج رقم الهاتف من المعرف
                    if (participant.id.endsWith('@c.us')) {
                        phoneNumber = participant.id.split('@')[0];
                        realPhoneNumber = convertCusToRealPhone(participant.id);
                    } else if (participant.id.endsWith('@lid')) {
                        phoneNumber = participant.id.split('@')[0];
                        realPhoneNumber = convertLidToRealPhone(participant.id);
                    }

                    return {
                        ...participant,
                        phoneNumber: phoneNumber,
                        realPhoneNumber: realPhoneNumber
                    };
                });

                return {
                    id: group.id._serialized,
                    name: group.name,
                    description: group.description || '',
                    owner: group.owner || '',
                    createdAt: group.createdAt ? new Date(group.createdAt * 1000).toISOString() : null,
                    participants: enhancedParticipants,
                    isCurrentUserAdmin: isCurrentUserAdmin,
                    inviteCode: inviteCode,
                    membershipRequests: membershipRequests.length,
                    membershipRequestsData: membershipRequests,
                    size: participants.length,
                };
            } catch (groupError) {
                console.error(`خطأ في معالجة المجموعة:`, groupError);
                return {
                    id: group.id._serialized,
                    name: group.name,
                    error: groupError.message
                };
            }
        }));

        // حفظ البيانات في ملف JSON
        const sessionDir = `./sessions/${accountName}`;
        if (!fs.existsSync(sessionDir)) {
            fs.mkdirSync(sessionDir, { recursive: true });
        }
        const groupsDataPath = path.join(sessionDir, 'groups_data.json');

        // تنظيم البيانات قبل الحفظ
        const groupsDataToSave = {
            accountName,
            extractedAt: new Date().toISOString(),
            count: groupsData.length,
            groups: groupsData
        };

        fs.writeFileSync(groupsDataPath, JSON.stringify(groupsDataToSave, null, 2), 'utf8');

        console.log(`تم استخراج وحفظ بيانات ${groupsData.length} مجموعة لـ ${accountName}`);

        return res.json({
            success: true,
            count: groupsData.length,
            groups: groupsData
        });
    } catch (error) {
        console.error(`خطأ في استخراج بيانات المجموعات للحساب ${accountName}:`, error);
        return res.status(500).json({
            error: 'حدث خطأ أثناء استخراج بيانات المجموعات',
            message: error.message
        });
    }
});

// API للحصول على جهات الاتصال
app.get('/api/contacts/:accountName', async (req, res) => {
    const { accountName } = req.params;
    const { refresh = 'false' } = req.query;

    try {
        // التحقق من وجود الحساب واتصاله
        if (!connections[accountName]) {
            return res.status(404).json({ error: 'الحساب غير موجود أو غير متصل' });
        }

        const client = connections[accountName].client;

        // التحقق من حالة الاتصال
        if (connections[accountName].status !== 'connected') {
            return res.status(400).json({ error: 'الحساب غير متصل حاليًا' });
        }

        // إذا لم يتم طلب التحديث، حاول استخدام البيانات المخزنة
        if (refresh === 'false') {
            const cachedContacts = loadContactsData(accountName);
            if (cachedContacts && cachedContacts.length > 0) {
                console.log(`Using cached contacts for ${accountName} (${cachedContacts.length} contacts)`);
                return res.json({
                    contacts: cachedContacts,
                    total: cachedContacts.length,
                    lastUpdated: getContactsLastUpdated(accountName)?.toISOString() || new Date().toISOString(),
                    fromCache: true
                });
            }
        }

        // إنشاء مصفوفة لتخزين جهات الاتصال
        const contacts = [];

        // إعداد الاستجابة لإرسال أحداث SSE
        res.setHeader('Content-Type', 'text/event-stream');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');

        // إنشاء مصفوفة لتخزين معلومات التقدم
        const progressInfo = {
            total: 0,
            loaded: 0,
            step: 'initialization'
        };

        // وظيفة لإرسال تحديثات التقدم
        const sendProgressUpdate = () => {
            const data = JSON.stringify({
                progress: progressInfo.loaded / Math.max(progressInfo.total, 1) * 100,
                step: progressInfo.step,
                loaded: progressInfo.loaded,
                total: progressInfo.total
            });

            res.write(`data: ${data}\n\n`);
        };

        // إرسال تحديث أولي
        sendProgressUpdate();

        // إنشاء مؤقت لإرسال تحديثات التقدم كل ثانية
        const progressInterval = setInterval(sendProgressUpdate, 1000);

        // إضافة مستمع لإغلاق الاتصال
        req.on('close', () => {
            clearInterval(progressInterval);
        });

        try {
            // تحديث التقدم
            progressInfo.step = 'fetching_contacts';
            sendProgressUpdate();

            // الحصول على جهات الاتصال باستخدام مكتبة whatsapp-web.js
            console.log(`Fetching contacts for ${accountName} using whatsapp-web.js`);

            // الحصول على جهات الاتصال
            const whatsappContacts = await client.getContacts();

            // تصفية جهات الاتصال للأشخاص (وليس المجموعات)
            const filteredContacts = whatsappContacts.filter(contact => {
                return !contact.isGroup && !contact.isMe && contact.id &&
                       contact.id.user && contact.id._serialized &&
                       contact.id._serialized.endsWith('@c.us');
            });

            console.log(`Found ${filteredContacts.length} contacts for ${accountName} (with @c.us ID)`);

            // تحديث إجمالي عدد جهات الاتصال
            progressInfo.total = filteredContacts.length;
            sendProgressUpdate();

            // تحسين استرجاع صور الملفات الشخصية باستخدام دفعات
            const batchSize = 10; // عدد جهات الاتصال في كل دفعة للمعالجة المتوازية

            console.log(`بدء معالجة ${filteredContacts.length} جهة اتصال بحجم دفعة ${batchSize} لـ ${accountName}`);

            try {
                for (let i = 0; i < filteredContacts.length; i += batchSize) {
                    const currentBatchStart = i;
                    const currentBatchEnd = Math.min(i + batchSize, filteredContacts.length);
                    console.log(`معالجة الدفعة ${Math.floor(i/batchSize) + 1}/${Math.ceil(filteredContacts.length/batchSize)}: جهات الاتصال ${currentBatchStart+1}-${currentBatchEnd} من ${filteredContacts.length}`);

                    const batch = filteredContacts.slice(i, i + batchSize);
                    const batchPromises = batch.map(async (contact, batchIndex) => {
                        try {
                            // الرقم الحالي في الدفعة الكاملة
                            const currentIndex = i + batchIndex;

                            // الحصول على معرف جهة الاتصال
                            const contactId = contact.id._serialized;

                            // تنسيق رقم الهاتف بدون علامة +
                            const phoneNumber = contact.id.user;

                            // إضافة جهة الاتصال بمعلومات أساسية قبل استكمال الباقي
                            // هذا يضمن أن لدينا بيانات حتى لو فشلت المعالجة الإضافية
                            const contactData = {
                                id: contactId,
                                pushName: contact.pushname || '', // الاسم المسجل في واتساب
                                savedName: contact.name || '', // الاسم المحفوظ في جهات الاتصال
                                name: contact.pushname || contact.name || phoneNumber, // الاسم الذي سيتم عرضه
                                verifiedName: contact.verifiedName || '', // الاسم الموثق (للحسابات التجارية)
                                number: phoneNumber,
                                profilePictureUrl: null,
                                isBusiness: contact.isBusiness || false
                            };

                            // إضافة جهة الاتصال بشكل مبكر - سنحدث الصورة لاحقًا
                            contacts.push(contactData);

                            // تحاول الحصول على صورة الملف الشخصي بحد أقصى من الوقت
                            try {
                                const profilePicturePromise = client.getProfilePicUrl(contactId);
                                // إضافة مهلة زمنية لتجنب التجميد
                                const timeoutPromise = new Promise((_, reject) =>
                                    setTimeout(() => reject(new Error('استغرق الحصول على الصورة وقتًا طويلاً')), 5000)
                                );

                                // استخدام Promise.race للحصول على نتيجة أو فشل بعد المهلة
                                const profilePictureUrl = await Promise.race([profilePicturePromise, timeoutPromise])
                                    .catch(() => null);

                                // تحديث الصورة في البيانات المحفوظة مسبقًا
                                if (profilePictureUrl) {
                                    contactData.profilePictureUrl = profilePictureUrl;
                                }
                            } catch (imageError) {
                                console.warn(`فشل في الحصول على صورة جهة الاتصال ${currentIndex+1}/${filteredContacts.length} (${phoneNumber}): ${imageError.message}`);
                            }

                            // تحديث عدد جهات الاتصال التي تم تحميلها
                            progressInfo.loaded++;

                            // إرسال تحديثات للتقدم بشكل أكثر تواترًا
                            if (currentIndex % 5 === 0 || currentIndex === filteredContacts.length - 1) {
                                sendProgressUpdate();
                            }
                        } catch (contactError) {
                            console.error(`خطأ في معالجة جهة الاتصال ${i + batchIndex + 1}/${filteredContacts.length}: ${contactError.message}`);

                            // زيادة عداد التحميل حتى لو حدث خطأ
                            progressInfo.loaded++;

                            // إضافة جهة اتصال بسيطة حتى لا نفقد معلومات
                            try {
                                if (contact && contact.id) {
                                    const phoneNumber = contact.id.user;
                                    contacts.push({
                                        id: contact.id._serialized || 'unknown-' + phoneNumber,
                                        name: phoneNumber,
                                        number: phoneNumber,
                                        pushName: '',
                                        savedName: '',
                                        verifiedName: '',
                                        profilePictureUrl: null,
                                        isBusiness: false
                                    });
                                }
                            } catch (recoveryError) {
                                console.error(`فشل في إضافة جهة اتصال بسيطة: ${recoveryError.message}`);
                            }
                        }
                    });

                    // تنفيذ مع معالجة أخطاء على مستوى الدفعة
                    try {
                        // انتظار اكتمال معالجة الدفعة الحالية
                        await Promise.all(batchPromises);
                        console.log(`تم استكمال الدفعة ${Math.floor(i/batchSize) + 1} بنجاح`);
                    } catch (batchError) {
                        console.error(`خطأ أثناء معالجة دفعة جهات الاتصال: ${batchError.message}`);
                        // نستمر في المعالجة رغم الخطأ لضمان الحصول على أكبر قدر ممكن من البيانات
                    }

                    // إرسال تحديث التقدم بعد كل دفعة
                    sendProgressUpdate();

                    // تعزيز استقرار البرنامج بإتاحة وقت للتنفيذ الآخر
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                console.log(`تم استكمال معالجة جميع جهات الاتصال (${contacts.length} جهات اتصال)`);
            } catch (processingError) {
                console.error(`خطأ غير متوقع أثناء معالجة جهات الاتصال: ${processingError.message}`);
                console.error(processingError.stack);

                // إذا حدث خطأ، لكن قمنا بالفعل بمعالجة بعض جهات الاتصال، نستمر
                if (contacts.length === 0) {
                    // فشلنا في الحصول على أي جهات اتصال
                    clearInterval(progressInterval);
                    res.write(`event: error\ndata: ${JSON.stringify({ message: 'فشل استخراج جهات الاتصال: ' + processingError.message })}\n\n`);
                    res.end();
                    return;
                }

                console.log(`سنستمر بـ ${contacts.length} جهة اتصال تم استخراجها قبل الخطأ`);
            }

            // إذا لم يتم العثور على جهات اتصال، أضف الحساب الحالي كجهة اتصال افتراضية
            if (contacts.length === 0) {
                console.log(`No contacts found for ${accountName}, adding self as default contact`);

                // تحديث التقدم
                progressInfo.step = 'adding_self';
                progressInfo.total = 1;
                progressInfo.loaded = 0;
                sendProgressUpdate();

                // الحصول على معلومات الحساب الحالي
                const clientInfo = client.info;

                try {
                    // الحصول على صورة الملف الشخصي
                    const profilePictureUrl = await client.getProfilePicUrl(clientInfo.wid._serialized).catch(() => null);

                    // إضافة الحساب الحالي كجهة اتصال افتراضية
                    contacts.push({
                        id: clientInfo.wid._serialized,
                        pushName: clientInfo.pushname || 'أنا', // الاسم المسجل في واتساب
                        savedName: 'أنا', // الاسم المحفوظ في جهات الاتصال
                        name: clientInfo.pushname || 'أنا', // الاسم الذي سيتم عرضه
                        verifiedName: '',
                        number: clientInfo.wid.user,
                        profilePictureUrl,
                        isBusiness: false
                    });

                    // تحديث عدد جهات الاتصال التي تم تحميلها
                    progressInfo.loaded = 1;
                    sendProgressUpdate();
                } catch (selfContactError) {
                    console.error(`Error adding self as contact: ${selfContactError.message}`);
                }
            }

            // تحديث التقدم
            progressInfo.step = 'sorting_contacts';
            sendProgressUpdate();

            // إزالة التكرار في جهات الاتصال بناءً على رقم الهاتف
            const uniqueContacts = [];
            const phoneMap = new Map();

            // تجميع جهات الاتصال المكررة
            for (const contact of contacts) {
                const phoneNumber = contact.number;

                if (phoneMap.has(phoneNumber)) {
                    // دمج معلومات جهة الاتصال الحالية مع جهة الاتصال الموجودة
                    const existingContact = phoneMap.get(phoneNumber);

                    // دمج الأسماء (الأولوية للقيم غير الفارغة)
                    existingContact.pushName = existingContact.pushName || contact.pushName;
                    existingContact.savedName = existingContact.savedName || contact.savedName;
                    existingContact.name = existingContact.name || contact.name;
                    existingContact.verifiedName = existingContact.verifiedName || contact.verifiedName;

                    // احتفظ بصورة الملف الشخصي إذا كانت متوفرة
                    existingContact.profilePictureUrl = existingContact.profilePictureUrl || contact.profilePictureUrl;

                    // احتفظ بمعلومات الأعمال
                    existingContact.isBusiness = existingContact.isBusiness || contact.isBusiness;
                } else {
                    // إضافة جهة الاتصال إلى الخريطة
                    phoneMap.set(phoneNumber, contact);
                    uniqueContacts.push(contact);
                }
            }

            // فرز جهات الاتصال أبجديًا
            const sortedContacts = uniqueContacts.sort((a, b) => {
                const nameA = a.pushName || a.savedName || a.name || a.number;
                const nameB = b.pushName || b.savedName || b.name || b.number;
                return nameA.localeCompare(nameB);
            });

            // تحديث التقدم
            progressInfo.step = 'saving_contacts';
            sendProgressUpdate();



            // حفظ جهات الاتصال في ملف JSON
            try {
                // التأكد من صحة المتغير قبل حفظه
                if (!Array.isArray(sortedContacts)) {
                    console.error(`خطأ: المتغير sortedContacts ليس مصفوفة صالحة لـ ${accountName}، النوع: ${typeof sortedContacts}`);
                    // إرسال خطأ إلى العميل وإنهاء الاستجابة
                    clearInterval(progressInterval);
                    res.write(`event: error\ndata: ${JSON.stringify({ message: 'خطأ داخلي: بيانات جهات الاتصال غير صالحة.' })}\n\n`);
                    res.end();
                    return;
                } else if (sortedContacts.length === 0) {
                    console.warn(`تحذير: لا توجد جهات اتصال للحفظ لـ ${accountName}`);
                    // مع ذلك، سنحاول حفظ مصفوفة فارغة إذا كان هذا هو المقصود
                } else {
                    console.log(`جاري حفظ ${sortedContacts.length} جهة اتصال لـ ${accountName}...`);
                    if (sortedContacts.length > 0) {
                        console.log(`نموذج من البيانات (أول جهة اتصال): ${JSON.stringify(sortedContacts[0], null, 2)}`);
                    }
                }

                const sessionDir = path.join(__dirname, 'sessions', accountName);
                if (!fs.existsSync(sessionDir)) {
                    fs.mkdirSync(sessionDir, { recursive: true });
                    console.log(`تم إنشاء مجلد: ${sessionDir}`);
                }

                console.log(`محاولة حفظ جهات الاتصال لـ ${accountName} باستخدام دالة saveContactsData...`);
                const success = saveContactsData(accountName, sortedContacts);

                if (success) {
                    console.log(`تم حفظ بيانات جهات الاتصال بنجاح لـ ${accountName} (${sortedContacts.length} جهات اتصال)`);
                    const verifyContacts = loadContactsData(accountName);
                    if (Array.isArray(verifyContacts)) {
                        console.log(`تأكيد: تم التحقق من وجود ${verifyContacts.length} جهة اتصال في الملف المحفوظ`);
                        progressInfo.step = 'completed';
                        progressInfo.loaded = progressInfo.total;
                        sendProgressUpdate();
                        clearInterval(progressInterval);

                        // التحقق من حفظ البيانات بنجاح
                        const contactsPath = path.join(__dirname, 'sessions', accountName, 'contacts.json');
                        if (fs.existsSync(contactsPath)) {
                            console.log(`تم التأكد من وجود ملف جهات الاتصال: ${contactsPath}`);

                            // التأكد من حجم الملف ومحتواه
                            const stats = fs.statSync(contactsPath);
                            if (stats.size > 0) {
                                console.log(`حجم ملف جهات الاتصال: ${stats.size} بايت`);

                                // إرسال الاستجابة الناجحة
                                const jsonResponse = {
                                    contacts: sortedContacts,
                                    total: sortedContacts.length,
                                    lastUpdated: new Date().toISOString(),
                                    success: true
                                };

                                res.write(`event: contactsData\ndata: ${JSON.stringify(jsonResponse)}\n\n`);
                                res.end();
                                return;
                            } else {
                                console.error(`خطأ: ملف جهات الاتصال فارغ بعد الحفظ (0 بايت)`);
                            }
                        } else {
                            console.error(`خطأ: ملف جهات الاتصال غير موجود بعد محاولة الحفظ`);
                        }
                    } else {
                        console.error(`خطأ في التحقق: نتيجة loadContactsData ليست مصفوفة، النوع: ${typeof verifyContacts}`);
                        // محاولة إصلاح المشكلة بإعادة حفظ البيانات مباشرة
                        const contactsPath = path.join(__dirname, 'sessions', accountName, 'contacts.json');
                        fs.writeFileSync(contactsPath, JSON.stringify(sortedContacts, null, 2), 'utf8');
                        console.log(`تمت محاولة إصلاح ملف جهات الاتصال بالكتابة المباشرة`);

                        // إرسال الاستجابة الناجحة
                        const jsonResponse = {
                            contacts: sortedContacts,
                            total: sortedContacts.length,
                            lastUpdated: new Date().toISOString(),
                            recovered: true
                        };

                        res.write(`event: contactsData\ndata: ${JSON.stringify(jsonResponse)}\n\n`);
                        res.end();
                        return;
                    }
                } else {
                    console.error(`فشل في حفظ بيانات جهات الاتصال لـ ${accountName} (saveContactsData ردت بـ false)`);

                    // محاولة الكتابة المباشرة في الملف
                    try {
                        const contactsPath = path.join(__dirname, 'sessions', accountName, 'contacts.json');
                        fs.writeFileSync(contactsPath, JSON.stringify(sortedContacts, null, 2), 'utf8');
                        console.log(`تمت محاولة إصلاح المشكلة بالكتابة المباشرة في الملف: ${contactsPath}`);

                        // التحقق من نجاح الكتابة المباشرة
                        if (fs.existsSync(contactsPath) && fs.statSync(contactsPath).size > 0) {
                            console.log(`نجحت الكتابة المباشرة، حجم الملف: ${fs.statSync(contactsPath).size} بايت`);

                            // إرسال الاستجابة الناجحة
                            const jsonResponse = {
                                contacts: sortedContacts,
                                total: sortedContacts.length,
                                lastUpdated: new Date().toISOString(),
                                directSave: true
                            };

                            res.write(`event: contactsData\ndata: ${JSON.stringify(jsonResponse)}\n\n`);
                            res.end();
                            return;
                        }
                    } catch (directSaveError) {
                        console.error(`فشل في الكتابة المباشرة في ملف جهات الاتصال: ${directSaveError.message}`);
                    }

                    // إرسال خطأ إلى العميل وإنهاء الاستجابة
                    clearInterval(progressInterval);
                    res.write(`event: error\ndata: ${JSON.stringify({ message: 'فشل حفظ جهات الاتصال على الخادم.' })}\n\n`);
                    res.end();
                    return;
                }
            } catch (saveError) {
                console.error(`خطأ في حفظ بيانات جهات الاتصال: ${saveError.message}`);
                console.error(saveError.stack); // طباعة تتبع الخطأ كاملاً للتشخيص

                // محاولة الكتابة المباشرة في الملف كمحاولة أخيرة للإصلاح
                try {
                    const contactsPath = path.join(__dirname, 'sessions', accountName, 'contacts.json');
                    fs.writeFileSync(contactsPath, JSON.stringify(sortedContacts, null, 2), 'utf8');
                    console.log(`تمت محاولة الإنقاذ بعد الخطأ بالكتابة المباشرة في الملف: ${contactsPath}`);

                    if (fs.existsSync(contactsPath) && fs.statSync(contactsPath).size > 0) {
                        const jsonResponse = {
                            contacts: sortedContacts,
                            total: sortedContacts.length,
                            lastUpdated: new Date().toISOString(),
                            emergency: true
                        };

                        res.write(`event: contactsData\ndata: ${JSON.stringify(jsonResponse)}\n\n`);
                        res.end();
                        return;
                    }
                } catch (finalError) {
                    console.error(`فشل في المحاولة النهائية لحفظ جهات الاتصال: ${finalError.message}`);
                }
            }

            // تحديث التقدم
            progressInfo.step = 'completed';
            progressInfo.loaded = progressInfo.total;
            sendProgressUpdate();

            // إيقاف مؤقت التقدم
            clearInterval(progressInterval);

            // إرسال البيانات النهائية
            const jsonResponse = {
                contacts: sortedContacts,
                total: sortedContacts.length,
                lastUpdated: new Date().toISOString()
            };

            // إرسال البيانات كحدث خاص
            res.write(`event: contactsData\ndata: ${JSON.stringify(jsonResponse)}\n\n`);

            // إنهاء الاستجابة
            res.end();
            return;
        } catch (error) {
            // إيقاف إرسال تحديثات التقدم
            clearInterval(progressInterval);

            console.error(`Error fetching contacts for ${accountName}:`, error);
            throw error;
        }
    } catch (error) {
        console.error(`Error fetching contacts for ${accountName}:`, error);
        return res.status(500).json({ error: 'حدث خطأ أثناء جلب جهات الاتصال' });
    }
});

// API للحصول على جهات الاتصال من ملف JSON المخزن (دون تحديث)
app.get('/api/cached-contacts/:accountName', (req, res) => {
    const { accountName } = req.params;
    const { page = '1', pageSize = '20' } = req.query;

    try {
        console.log(`طلب استدعاء جهات الاتصال المخزنة لـ ${accountName}`);

        // التحقق من وجود الحساب
        const sessionDir = path.join(__dirname, 'sessions', accountName);
        if (!fs.existsSync(sessionDir)) {
            fs.mkdirSync(sessionDir, { recursive: true });
            console.log(`تم إنشاء مجلد جلسة جديد لـ ${accountName}: ${sessionDir}`);
        }

        // تحميل جهات الاتصال من الملف المخزن
        console.log(`محاولة تحميل جهات الاتصال لـ ${accountName} باستخدام دالة loadContactsData...`);
        const contacts = loadContactsData(accountName);

        // إذا لم يكن هناك جهات اتصال (الدالة المعدلة ستنشئ ملف فارغ وتعيد مصفوفة فارغة)
        if (!contacts || contacts.length === 0) {
            console.log(`لم يتم العثور على جهات اتصال مخزنة لـ ${accountName}`);
            return res.json({
                contacts: [],
                lastUpdated: new Date().toISOString(),
                pagination: {
                    page: 1,
                    pageSize: parseInt(pageSize),
                    totalPages: 0,
                    totalItems: 0
                },
                message: 'تم إنشاء ملف جهات اتصال فارغ'
            });
        }

        // التحقق من صحة البيانات والتأكد من أنها تحتوي على الحقول المطلوبة
        const validContacts = contacts.filter(contact => {
            return contact && typeof contact === 'object' && (contact.id || contact.number);
        });

        if (validContacts.length < contacts.length) {
            console.warn(`تحذير: تم استبعاد ${contacts.length - validContacts.length} جهة اتصال غير صالحة`);
            // حفظ البيانات المنقحة لاستخدامها لاحقًا
            saveContactsData(accountName, validContacts);
        }

        console.log(`تم العثور على ${validContacts.length} جهة اتصال صالحة مخزنة لـ ${accountName}`);

        // الحصول على تاريخ آخر تحديث
        const lastUpdated = getContactsLastUpdated(accountName);

        // تفعيل التصفح بالصفحات إذا تم طلبه
        const pageNum = parseInt(page);
        const pageSizeNum = parseInt(pageSize);

        // إذا كان pageSize كبير (مثل >=1000)، نفترض أنه يطلب كل البيانات
        if (pageSizeNum >= 1000) {
            console.log(`إرسال كل جهات الاتصال المخزنة (${validContacts.length}) لـ ${accountName}`);
            return res.json({
                contacts: validContacts,
                lastUpdated: lastUpdated || new Date().toISOString(),
                pagination: {
                    page: 1,
                    pageSize: validContacts.length,
                    totalPages: 1,
                    totalItems: validContacts.length
                }
            });
        }

        // حساب الفهارس للصفحة المطلوبة
        const startIndex = (pageNum - 1) * pageSizeNum;
        const endIndex = Math.min(startIndex + pageSizeNum, validContacts.length);

        // استخراج جهات الاتصال للصفحة الحالية
        const paginatedContacts = validContacts.slice(startIndex, endIndex);

        // إنشاء معلومات الصفحات
        const pagination = {
            page: pageNum,
            pageSize: pageSizeNum,
            totalPages: Math.ceil(validContacts.length / pageSizeNum),
            totalItems: validContacts.length
        };

        console.log(`إرسال ${paginatedContacts.length} جهة اتصال (الصفحة ${pageNum}/${pagination.totalPages}) لـ ${accountName}`);

        // إرسال البيانات
        res.json({
            contacts: paginatedContacts,
            pagination,
            lastUpdated: lastUpdated || new Date().toISOString()
        });
    } catch (error) {
        console.error(`خطأ في تحميل جهات الاتصال المخزنة لـ ${accountName}:`, error);
        console.error(error.stack); // طباعة تتبع الخطأ كاملاً للتشخيص

        // محاولة التعافي والإرجاع
        try {
            // إنشاء ملف فارغ في حالة الخطأ
            const contactsPath = path.join(__dirname, 'sessions', accountName, 'contacts.json');
            if (!fs.existsSync(path.dirname(contactsPath))) {
                fs.mkdirSync(path.dirname(contactsPath), { recursive: true });
            }
            fs.writeFileSync(contactsPath, JSON.stringify([], null, 2), 'utf8');

            res.status(500).json({
                error: 'حدث خطأ أثناء استرجاع جهات الاتصال المخزنة',
                message: 'تم إنشاء ملف جهات اتصال فارغ كإجراء للتعافي',
                contacts: [],
                pagination: {
                    page: 1,
                    pageSize: parseInt(pageSize),
                    totalPages: 0,
                    totalItems: 0
                },
                lastUpdated: new Date().toISOString()
            });
        } catch (recoveryError) {
            console.error(`فشل في التعافي من خطأ تحميل جهات الاتصال لـ ${accountName}:`, recoveryError);
            res.status(500).json({ error: 'حدث خطأ أثناء استرجاع جهات الاتصال المخزنة' });
        }
    }
});

// دالة مساعدة للكشف عن صحة ملفات الجلسة
function checkSessionHealth(accountName) {
    try {
        // 1. التحقق من مجلد الجلسة وملفاته الأساسية
        const sessionDir = path.join(__dirname, '.wwebjs_auth', `session-${accountName}`);
        if (!fs.existsSync(sessionDir)) {
            return { valid: false, reason: 'session_dir_not_found' };
        }

        // 2. التحقق من وجود ملف الجلسة الأساسي
        // في whatsapp-web.js، عادة ما تكون هناك ملفات متعددة، لكن سنتحقق من وجود مجلد الاعتماد
        const mainSessionFileName = path.join(sessionDir, 'Default', 'Local Storage', 'leveldb');
        if (!fs.existsSync(mainSessionFileName)) {
            return { valid: false, reason: 'session_files_missing' };
        }

        // 3. إذا كان الاتصال موجودًا، تحقق من حالته
        if (connections[accountName]) {
            if (connections[accountName].status === 'error' ||
                connections[accountName].status === 'auth_failure' ||
                connections[accountName].status === 'disconnected') {
                return { valid: false, reason: connections[accountName].status };
            }

            return { valid: true, status: connections[accountName].status };
        }

        // 4. التحقق إذا كانت هناك معلومات حساب محفوظة للجلسة غير المتصلة
        const savedAccountInfo = loadAccountData(accountName);
        if (!savedAccountInfo) {
            // لم نجد معلومات الحساب، لكن المجلد موجود، لذا نعتبره محتملاً
            return { valid: true, status: 'disconnected', note: 'no_account_info' };
        }

        return { valid: true, status: 'disconnected' };
    } catch (error) {
        console.error(`Error checking session health for ${accountName}:`, error);
        return { valid: false, reason: 'error_checking', error: error.message };
    }
}

// API للحصول على جميع الحسابات
app.get('/api/accounts', (req, res) => {
    try {
        const accountsList = [];

        // 1. جمع الحسابات النشطة من الاتصالات
        for (const accountName in connections) {
            // محاولة قراءة معلومات الحساب من ملف JSON
            const savedAccountInfo = loadAccountData(accountName);

            // استخدام المعلومات المحفوظة في ملف JSON إذا كانت متوفرة، أو من الاتصال النشط
            const info = connections[accountName]?.info || savedAccountInfo;
            const currentPhoneNumber = connections[accountName]?.phoneNumber || savedAccountInfo?.number;

            // فحص صحة الجلسة
            const sessionHealth = checkSessionHealth(accountName);

            accountsList.push({
                accountName,
                info: info,
                phoneNumber: currentPhoneNumber,
                status: connections[accountName]?.status || (savedAccountInfo ? 'disconnected' : 'unknown'),
                sessionHealth,
                needsRepair: sessionHealth.valid === false // إضافة علامة للحسابات التي تحتاج إلى إصلاح
            });
        }

        // 2. إضافة الحسابات المحفوظة التي ليست بالضرورة متصلة حاليًا
        const sessionAuthDir = path.join(__dirname, '.wwebjs_auth');
        if (fs.existsSync(sessionAuthDir)) {
            const sessionFolders = fs.readdirSync(sessionAuthDir).filter(folder => folder.startsWith('session-'));
            sessionFolders.forEach(folderName => {
                const accountNameFromFolder = folderName.replace('session-', '');
                if (!accountsList.find(acc => acc.accountName === accountNameFromFolder)) {
                    const savedAccountInfo = loadAccountData(accountNameFromFolder);

                    // فحص صحة الجلسة
                    const sessionHealth = checkSessionHealth(accountNameFromFolder);

                    accountsList.push({
                        accountName: accountNameFromFolder,
                        info: savedAccountInfo,
                        phoneNumber: savedAccountInfo?.number,
                        status: 'disconnected',
                        sessionHealth,
                        needsRepair: sessionHealth.valid === false // إضافة علامة للحسابات التي تحتاج إلى إصلاح
                    });
                }
            });
        }

        return res.json({ accounts: accountsList });
    } catch (error) {
        console.error('Error loading accounts:', error);
        return res.status(500).json({ error: 'حدث خطأ أثناء تحميل الحسابات' });
    }
});

// API لحذف الحساب وإزالة الجلسة المرتبطة به
app.delete('/api/delete-account/:accountName', async (req, res) => {
    try {
        const { accountName } = req.params;
        const sessionDir = path.join(__dirname, '.wwebjs_auth', `session-${accountName}`);
        const legacySessionDir = path.join(__dirname, 'sessions', accountName); // مجلد الجلسة القديم لـ Baileys

        let accountExists = false;
        if (connections[accountName] || fs.existsSync(sessionDir) || fs.existsSync(legacySessionDir)) {
            accountExists = true;
        }

        if (!accountExists) {
            return res.status(404).json({ error: 'الحساب غير موجود' });
        }

        // إذا كان الاتصال موجودًا، قم بتسجيل الخروج وتدميره
        if (connections[accountName] && connections[accountName].client) {
            try {
                console.log(`Logging out and destroying client for ${accountName}...`);
                await connections[accountName].client.logout(); // يسجل الخروج من WhatsApp Web
                await connections[accountName].client.destroy(); // يغلق المتصفح ويحرر الموارد
                console.log(`Client logged out and destroyed for ${accountName}`);
            } catch (e) {
                console.error(`Error logging out or destroying client for ${accountName}:`, e);
            }
            delete connections[accountName];
        }

        // حذف مجلد الجلسة الخاص بـ whatsapp-web.js
        if (fs.existsSync(sessionDir)) {
            try {
                // استخدام fs.rmSync لحذف المجلد ومحتوياته بشكل متكرر
                fs.rmSync(sessionDir, { recursive: true, force: true });
                console.log(`Session directory ${sessionDir} deleted for ${accountName}`);
            } catch (err) {
                console.error(`Error deleting session directory ${sessionDir} for ${accountName}:`, err);
            }
        }

        // حذف مجلد الجلسة القديم الخاص بـ Baileys إذا كان موجودًا
        if (fs.existsSync(legacySessionDir)) {
            try {
                fs.rmSync(legacySessionDir, { recursive: true, force: true });
                console.log(`Legacy session directory ${legacySessionDir} deleted for ${accountName}`);
            } catch (err) {
                console.error(`Error deleting legacy session directory ${legacySessionDir} for ${accountName}:`, err);
            }
        }


        // حذف ملف بيانات الحساب JSON
        const accountDataPath = path.join(__dirname, 'accounts_data', `${accountName}.json`);
        if (fs.existsSync(accountDataPath)) {
            try {
                fs.unlinkSync(accountDataPath);
                console.log(`Account data file ${accountDataPath} deleted for ${accountName}`);
            } catch (err) {
                console.error(`Error deleting account data file ${accountDataPath} for ${accountName}:`, err);
            }
        }

        // حذف ملف بيانات جهات الاتصال JSON
        const contactsDataPath = path.join(__dirname, 'contacts_data', `${accountName}.json`);
        if (fs.existsSync(contactsDataPath)) {
            try {
                fs.unlinkSync(contactsDataPath);
                console.log(`Contacts data file ${contactsDataPath} deleted for ${accountName}`);
            } catch (err) {
                console.error(`Error deleting contacts data file ${contactsDataPath} for ${accountName}:`, err);
            }
        }


        return res.status(200).json({
            success: true,
            message: `تم حذف الحساب ${accountName} وجميع بيانات الجلسة المرتبطة به بنجاح`
        });
    } catch (error) {
        console.error('Error deleting account:', error);
        return res.status(500).json({ error: 'حدث خطأ أثناء حذف الحساب' });
    }
});

// ===== APIs الرد التلقائي =====

// مجلد لحفظ إعدادات الرد التلقائي
const autoReplyDir = path.join(__dirname, 'auto-reply-settings');
if (!fs.existsSync(autoReplyDir)) {
    fs.mkdirSync(autoReplyDir, { recursive: true });
}

// وظائف مساعدة لإدارة إعدادات الرد التلقائي
function loadAutoReplySettings(type, accountName = null) {
    try {
        const fileName = type === 'global' ? 'global.json' : `${accountName}.json`;
        const filePath = path.join(autoReplyDir, fileName);

        if (fs.existsSync(filePath)) {
            const data = fs.readFileSync(filePath, 'utf8');
            return JSON.parse(data);
        }

        return {
            enabled: false,
            messageType: 'text',
            message: {}
        };
    } catch (error) {
        console.error('Error loading auto-reply settings:', error);
        return {
            enabled: false,
            messageType: 'text',
            message: {}
        };
    }
}

function saveAutoReplySettings(type, settings, accountName = null) {
    try {
        const fileName = type === 'global' ? 'global.json' : `${accountName}.json`;
        const filePath = path.join(autoReplyDir, fileName);

        fs.writeFileSync(filePath, JSON.stringify(settings, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving auto-reply settings:', error);
        return false;
    }
}

// وظيفة لتحميل إعدادات الرد التلقائي المتقدم
function loadAdvancedAutoReplySettings(accountName = null) {
    try {
        let fileName = 'advanced.json'; // الإعدادات العامة
        if (accountName) {
            fileName = `advanced_${accountName}.json`; // إعدادات خاصة بالحساب
        }

        const advancedSettingsPath = path.join(autoReplyDir, fileName);
        if (fs.existsSync(advancedSettingsPath)) {
            const data = fs.readFileSync(advancedSettingsPath, 'utf8');
            return JSON.parse(data);
        }
        return null;
    } catch (error) {
        console.error('Error loading advanced auto-reply settings:', error);
        return null;
    }
}

// وظيفة تطابق نصي بسيطة وفعالة
function calculateSimpleTextSimilarity(text1, text2) {
    try {
        // تنظيف وتطبيع النصوص
        const clean1 = normalizeArabicText(text1.toLowerCase().trim());
        const clean2 = normalizeArabicText(text2.toLowerCase().trim());

        if (!clean1 || !clean2) return 0;

        // تقطيع النصوص إلى كلمات
        const words1 = clean1.split(/\s+/).filter(word => word.length > 1);
        const words2 = clean2.split(/\s+/).filter(word => word.length > 1);

        if (words1.length === 0 || words2.length === 0) return 0;

        // حساب الكلمات المشتركة
        let commonWords = 0;
        const set2 = new Set(words2);

        for (const word of words1) {
            if (set2.has(word)) {
                commonWords++;
            }
        }

        // حساب نسبة التطابق
        const similarity = (commonWords * 2) / (words1.length + words2.length);

        // إضافة نقاط إضافية للتطابق الجزئي
        let partialMatch = 0;
        for (const word1 of words1) {
            for (const word2 of words2) {
                if (word1.includes(word2) || word2.includes(word1)) {
                    partialMatch += 0.5;
                    break;
                }
            }
        }

        const finalScore = Math.min(1, similarity + (partialMatch / Math.max(words1.length, words2.length)));

        return finalScore;

    } catch (error) {
        console.error('خطأ في حساب التطابق النصي:', error);
        return 0;
    }
}

// وظيفة تطبيع النص العربي
function normalizeArabicText(text) {
    if (!text) return '';

    return text
        // تحويل الأرقام العربية إلى إنجليزية
        .replace(/[٠-٩]/g, (d) => '٠١٢٣٤٥٦٧٨٩'.indexOf(d))
        // توحيد الألف
        .replace(/[أإآ]/g, 'ا')
        // توحيد التاء المربوطة والهاء
        .replace(/ة/g, 'ه')
        // توحيد الياء
        .replace(/ى/g, 'ي')
        // إزالة التشكيل
        .replace(/[\u064B-\u0652]/g, '')
        // إزالة علامات الترقيم
        .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\w\s]/g, ' ')
        // تنظيف المسافات
        .replace(/\s+/g, ' ')
        .trim();
}

// وظيفة لمعالجة الرد التلقائي المتقدم مع المنطق الجديد
async function processAdvancedAutoReply(message, advancedSettings, accountName, client) {
    try {
        const messageBody = message.body.trim();

        if (!client || !messageBody) {
            console.log(`❌ Invalid client or empty message body`);
            return false;
        }

        console.log(`🔍 معالجة الرد التلقائي للرسالة: "${messageBody}"`);

        // تحديد نوع الرسالة: رقم/إيموجي أم نص
        const isNumberOrEmoji = isMessageNumberOrEmoji(messageBody);

        if (isNumberOrEmoji) {
            console.log(`🔢 رسالة رقم أو إيموجي - البحث في عمود الكود بتطابق 100%`);

            // إنشاء معالج النصوص لتحويل الأرقام العربية
            const modernTextProcessor = new ModernAITextProcessor();
            const normalizedMessage = modernTextProcessor.convertArabicNumbers(messageBody);
            console.log(`🔄 الأصلي: "${messageBody}" → المحول: "${normalizedMessage}"`);

            // البحث في عمود الكود بتطابق 100%
            const exactCodeMatch = advancedSettings.rules.find(rule => {
                if (!rule.enabled || !rule.code) return false;

                // مقارنة مباشرة
                if (rule.code === messageBody) return true;

                // مقارنة بعد تحويل الأرقام العربية
                const normalizedCode = modernTextProcessor.convertArabicNumbers(rule.code);
                if (normalizedCode === normalizedMessage) return true;

                // مقارنة متقاطعة
                if (rule.code === normalizedMessage) return true;

                return false;
            });

            if (exactCodeMatch) {
                console.log(`🎯 تم العثور على تطابق دقيق في عمود الكود: "${exactCodeMatch.code}"`);
                await sendAdvancedAutoReply(client, message.from, exactCodeMatch, accountName);
                return true;
            }

            console.log(`❌ لم يتم العثور على تطابق في عمود الكود للرسالة: "${messageBody}"`);
            return false;

        } else {
            console.log(`💬 رسالة نصية - البحث في عمود الرسالة النصية`);

            // فلترة القواعد النصية المفعلة
            const textRules = advancedSettings.rules.filter(rule =>
                rule.enabled && rule.messages && rule.messages.text && rule.messages.text.trim()
            );

            if (textRules.length === 0) {
                console.log(`❌ لا توجد قواعد نصية متاحة للمطابقة`);
                return false;
            }

            console.log(`🔍 فحص ${textRules.length} قاعدة نصية للعثور على أفضل تطابق...`);

            // البحث عن أفضل تطابق باستخدام النظام البسيط
            let bestMatch = null;
            let bestScore = 0;

            for (const rule of textRules) {
                const similarity = calculateSimpleTextSimilarity(messageBody, rule.messages.text);

                console.log(`📊 القاعدة "${rule.code}": ${(similarity * 100).toFixed(2)}% - "${rule.messages.text.substring(0, 50)}..."`);

                if (similarity > bestScore) {
                    bestScore = similarity;
                    bestMatch = rule;
                }
            }

            if (bestMatch && bestScore > 0) {
                console.log(`🏆 الفائز: "${bestMatch.code}" بنسبة ${(bestScore * 100).toFixed(2)}%`);
                console.log(`📤 إرسال الرد الكامل للصف الفائز...`);
                await sendAdvancedAutoReply(client, message.from, bestMatch, accountName);
                return true;
            }

            console.log(`❌ لم يتم العثور على أي تطابق في عمود الرسالة النصية`);
            return false;
        }

    } catch (error) {
        console.error('❌ خطأ في معالجة الرد التلقائي:', error);
        return false;
    }
}

// وظيفة لتحديد إذا كانت الرسالة رقم أو إيموجي
function isMessageNumberOrEmoji(message) {
    // فحص إذا كانت الرسالة رقم فقط (عربي أو إنجليزي)
    const isNumber = /^[\d٠-٩]+$/.test(message.trim());

    // فحص إذا كانت الرسالة إيموجي فقط
    const emojiRegex = /^[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]+$/u;
    const isEmoji = emojiRegex.test(message.trim());

    return isNumber || isEmoji;
}

// وظيفة لحفظ إعدادات الرد التلقائي المتقدم
function saveAdvancedAutoReplySettings(settings, accountName = null) {
    try {
        let fileName = 'advanced.json'; // الإعدادات العامة
        if (accountName) {
            fileName = `advanced_${accountName}.json`; // إعدادات خاصة بالحساب
        }

        const advancedSettingsPath = path.join(autoReplyDir, fileName);

        // إضافة timestamp للحفظ
        settings.last_updated = new Date().toISOString();

        fs.writeFileSync(advancedSettingsPath, JSON.stringify(settings, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving advanced auto-reply settings:', error);
        return false;
    }
}

// ===== نظام الذكاء الاصطناعي المتقدم =====

// إعداد مكتبات الذكاء الاصطناعي
const stemmer = natural.PorterStemmer;

// دوال تطبيع النصوص والأرقام المتقدمة
class AdvancedTextProcessor {
    constructor() {
        // قاموس الأرقام العربية والإنجليزية
        this.arabicNumbers = '٠١٢٣٤٥٦٧٨٩';
        this.englishNumbers = '**********';

        // قاموس المرادفات العربية
        this.synonyms = {
            'مرحبا': ['أهلا', 'اهلا', 'هلا', 'السلام عليكم', 'صباح الخير', 'مساء الخير'],
            'شكرا': ['شكراً', 'تسلم', 'يعطيك العافية', 'جزاك الله خير'],
            'السعر': ['الثمن', 'التكلفة', 'كم', 'بكم', 'سعر'],
            'مساعدة': ['مساعده', 'دعم', 'مساندة', 'عون'],
            'معلومات': ['معلومه', 'بيانات', 'تفاصيل', 'شرح']
        };

        // الكلمات المستبعدة (Stop Words)
        this.arabicStopWords = ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك', 'التي', 'الذي', 'كان', 'كانت', 'يكون', 'تكون', 'هو', 'هي', 'أن', 'إن', 'لا', 'ما', 'لم', 'لن', 'قد', 'كل', 'بعض', 'جميع'];
    }

    // تطبيع الأرقام العربية إلى إنجليزية
    normalizeNumbers(text) {
        let result = text;
        for (let i = 0; i < this.arabicNumbers.length; i++) {
            const arabicNum = this.arabicNumbers[i];
            const englishNum = this.englishNumbers[i];
            result = result.replace(new RegExp(arabicNum, 'g'), englishNum);
        }
        return result;
    }

    // تطبيع النصوص العربية المتقدم
    normalizeArabicText(text) {
        let normalized = text
            // تحويل إلى أحرف صغيرة
            .toLowerCase()
            // إزالة التشكيل والحركات
            .replace(/[\u064B-\u0652\u0670\u0640]/g, '')
            // توحيد الألف
            .replace(/[آأإ]/g, 'ا')
            // توحيد التاء المربوطة
            .replace(/[ة]/g, 'ه')
            // توحيد الياء
            .replace(/[ى]/g, 'ي')
            // توحيد الهمزة
            .replace(/[ؤئ]/g, 'ء')
            // إزالة علامات الترقيم
            .replace(/[.,;:!?()[\]{}"'`~@#$%^&*+=|\\<>]/g, ' ')
            // إزالة المسافات الزائدة
            .replace(/\s+/g, ' ')
            .trim();

        return normalized;
    }

    // معالجة شاملة للنص
    processText(text) {
        if (!text || typeof text !== 'string') return '';

        // تطبيع الأرقام
        let processed = this.normalizeNumbers(text);

        // تطبيع النص العربي
        processed = this.normalizeArabicText(processed);

        return processed;
    }

    // استخراج الكلمات المفتاحية
    extractKeywords(text) {
        const processed = this.processText(text);
        const words = processed.split(/\s+/).filter(word => word.length > 1);

        // إزالة الكلمات المستبعدة
        const filteredWords = words.filter(word => !this.arabicStopWords.includes(word));

        // إرجاع الكلمات الفريدة
        return [...new Set(filteredWords)];
    }

    // البحث عن المرادفات
    findSynonyms(word) {
        const processed = this.processText(word);
        const synonyms = [];

        for (const [key, values] of Object.entries(this.synonyms)) {
            if (key === processed || values.some(syn => this.processText(syn) === processed)) {
                synonyms.push(key, ...values);
            }
        }

        return [...new Set(synonyms.map(syn => this.processText(syn)))];
    }
}

// نظام الذكاء الاصطناعي المتقدم للمطابقة
class AdvancedAIMatcher {
    constructor() {
        this.textProcessor = new AdvancedTextProcessor();
        this.stemmer = natural.PorterStemmer;
    }

    // حساب التشابه باستخدام خوارزميات متعددة
    calculateSimilarity(text1, text2) {
        const scores = {
            exact: this.exactMatch(text1, text2),
            fuzzy: this.fuzzyMatch(text1, text2),
            semantic: this.semanticMatch(text1, text2),
            keyword: this.keywordMatch(text1, text2),
            ngram: this.ngramMatch(text1, text2),
            levenshtein: this.levenshteinMatch(text1, text2)
        };

        // حساب النتيجة المرجحة
        const weights = {
            exact: 0.3,
            fuzzy: 0.2,
            semantic: 0.25,
            keyword: 0.15,
            ngram: 0.05,
            levenshtein: 0.05
        };

        let totalScore = 0;
        for (const [method, score] of Object.entries(scores)) {
            totalScore += score * weights[method];
        }

        return {
            total: totalScore,
            breakdown: scores
        };
    }

    // مطابقة دقيقة
    exactMatch(text1, text2) {
        const processed1 = this.textProcessor.processText(text1);
        const processed2 = this.textProcessor.processText(text2);

        if (processed1 === processed2) return 1.0;
        if (processed1.includes(processed2) || processed2.includes(processed1)) return 0.8;

        return 0;
    }

    // مطابقة ضبابية باستخدام Fuse.js
    fuzzyMatch(text1, text2) {
        const options = {
            includeScore: true,
            threshold: 0.4,
            ignoreLocation: true,
            ignoreFieldNorm: true
        };

        const fuse = new Fuse([text2], options);
        const result = fuse.search(text1);

        if (result.length > 0) {
            return 1 - result[0].score; // Fuse.js يعطي نتيجة أقل للمطابقة الأفضل
        }

        return 0;
    }

    // مطابقة دلالية باستخدام المرادفات
    semanticMatch(text1, text2) {
        const keywords1 = this.textProcessor.extractKeywords(text1);
        const keywords2 = this.textProcessor.extractKeywords(text2);

        let matches = 0;
        let totalComparisons = 0;

        for (const word1 of keywords1) {
            const synonyms1 = this.textProcessor.findSynonyms(word1);
            synonyms1.push(word1);

            for (const word2 of keywords2) {
                totalComparisons++;
                const synonyms2 = this.textProcessor.findSynonyms(word2);
                synonyms2.push(word2);

                // فحص التطابق المباشر أو عبر المرادفات
                if (synonyms1.some(syn1 => synonyms2.some(syn2 => syn1 === syn2))) {
                    matches++;
                }
            }
        }

        return totalComparisons > 0 ? matches / totalComparisons : 0;
    }

    // مطابقة الكلمات المفتاحية
    keywordMatch(text1, text2) {
        const keywords1 = this.textProcessor.extractKeywords(text1);
        const keywords2 = this.textProcessor.extractKeywords(text2);

        if (keywords1.length === 0 || keywords2.length === 0) return 0;

        const commonKeywords = keywords1.filter(word => keywords2.includes(word));
        const totalKeywords = Math.max(keywords1.length, keywords2.length);

        return commonKeywords.length / totalKeywords;
    }

    // مطابقة N-gram
    ngramMatch(text1, text2) {
        const ngrams1 = natural.NGrams.bigrams(this.textProcessor.processText(text1).split(' '));
        const ngrams2 = natural.NGrams.bigrams(this.textProcessor.processText(text2).split(' '));

        if (ngrams1.length === 0 || ngrams2.length === 0) return 0;

        const ngrams1Str = ngrams1.map(gram => gram.join(' '));
        const ngrams2Str = ngrams2.map(gram => gram.join(' '));

        const commonNgrams = ngrams1Str.filter(gram => ngrams2Str.includes(gram));
        const totalNgrams = Math.max(ngrams1Str.length, ngrams2Str.length);

        return commonNgrams.length / totalNgrams;
    }

    // مطابقة Levenshtein Distance
    levenshteinMatch(text1, text2) {
        try {
            const processed1 = this.textProcessor.processText(text1);
            const processed2 = this.textProcessor.processText(text2);

            const maxLength = Math.max(processed1.length, processed2.length);
            if (maxLength === 0) return 1;

            const distance = this.calculateLevenshteinDistance(processed1, processed2);
            return Math.max(0, 1 - (distance / maxLength));
        } catch (error) {
            console.error('Error in levenshteinMatch:', error);
            return 0;
        }
    }

    // تنفيذ خوارزمية Levenshtein Distance
    calculateLevenshteinDistance(str1, str2) {
        const matrix = [];

        // إنشاء المصفوفة
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }

        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }

        // ملء المصفوفة
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1, // استبدال
                        matrix[i][j - 1] + 1,     // إدراج
                        matrix[i - 1][j] + 1      // حذف
                    );
                }
            }
        }

        return matrix[str2.length][str1.length];
    }
}

// وظيفة البحث المتقدمة باستخدام الذكاء الاصطناعي المحسن
async function findAIMatch(messageBody, rules, threshold = 0.5, messageAnalysis = null) {
    try {
        const matcher = new AdvancedAIMatcher();
        const textProcessor = new AdvancedTextProcessor();
        const enabledRules = rules.filter(rule => rule.enabled && rule.messages && rule.messages.text);

        if (enabledRules.length === 0) {
            console.log('🚫 AI Analysis: No enabled rules found');
            return null;
        }

        console.log(`🤖 AI Analysis: Processing message "${messageBody}"`);
        console.log(`📊 Analyzing against ${enabledRules.length} rules with threshold ${(threshold * 100).toFixed(1)}%`);

        // تطبيع الرسالة الواردة
        const normalizedMessage = textProcessor.processText(messageBody);
        const messageKeywords = textProcessor.extractKeywords(messageBody);

        console.log(`🔧 Normalized message: "${normalizedMessage}"`);
        console.log(`🔑 Extracted keywords: [${messageKeywords.join(', ')}]`);

        let bestMatch = null;
        let bestScore = 0;
        let analysisResults = [];

        for (const rule of enabledRules) {
            const ruleText = rule.messages.text;
            const normalizedRuleText = textProcessor.processText(ruleText);
            const ruleKeywords = textProcessor.extractKeywords(ruleText);

            // حساب التشابه باستخدام النصوص الأصلية والمطبعة
            const originalSimilarity = matcher.calculateSimilarity(messageBody, ruleText);
            const normalizedSimilarity = matcher.calculateSimilarity(normalizedMessage, normalizedRuleText);

            // حساب تشابه الكلمات المفتاحية
            const keywordSimilarity = calculateKeywordSimilarity(messageKeywords, ruleKeywords);

            // حساب النتيجة النهائية مع الأوزان
            const finalScore = Math.max(
                originalSimilarity.total * 0.4 + normalizedSimilarity.total * 0.4 + keywordSimilarity * 0.2,
                originalSimilarity.total,
                normalizedSimilarity.total
            );

            analysisResults.push({
                rule: rule.code || rule.id,
                text: ruleText,
                normalizedText: normalizedRuleText,
                keywords: ruleKeywords,
                originalScore: originalSimilarity.total,
                normalizedScore: normalizedSimilarity.total,
                keywordScore: keywordSimilarity,
                finalScore: finalScore,
                breakdown: originalSimilarity.breakdown
            });

            console.log(`🔍 Rule "${rule.code || rule.id}": ${(finalScore * 100).toFixed(1)}%`);
            console.log(`   📝 Original: "${ruleText}"`);
            console.log(`   🔧 Normalized: "${normalizedRuleText}"`);
            console.log(`   🔑 Keywords: [${ruleKeywords.join(', ')}]`);
            console.log(`   📊 Scores: Orig=${(originalSimilarity.total * 100).toFixed(1)}%, Norm=${(normalizedSimilarity.total * 100).toFixed(1)}%, Keys=${(keywordSimilarity * 100).toFixed(1)}%, Final=${(finalScore * 100).toFixed(1)}%`);

            if (finalScore > bestScore) {
                bestScore = finalScore;
                bestMatch = rule;
            }
        }

        // ترتيب النتائج حسب النتيجة النهائية
        analysisResults.sort((a, b) => b.finalScore - a.finalScore);

        console.log(`🏆 Best match: ${bestScore >= threshold ? 'FOUND' : 'NOT FOUND'}`);
        console.log(`📈 Best score: ${(bestScore * 100).toFixed(1)}% (threshold: ${(threshold * 100).toFixed(1)}%)`);

        if (bestMatch && bestScore >= threshold) {
            console.log(`✅ Selected rule: "${bestMatch.code || bestMatch.id}" - "${bestMatch.messages.text}"`);
            return { rule: bestMatch, score: bestScore, analysis: analysisResults[0] };
        }

        console.log(`❌ No rule met the threshold. Best was ${(bestScore * 100).toFixed(1)}%`);

        // عرض أفضل 3 نتائج للتشخيص
        console.log(`📋 Top 3 matches:`);
        analysisResults.slice(0, 3).forEach((result, index) => {
            console.log(`   ${index + 1}. "${result.rule}": ${(result.finalScore * 100).toFixed(1)}%`);
        });

        return null;

    } catch (error) {
        console.error('❌ Error in AI matching:', error);
        return null;
    }
}

// وظيفة مساعدة لحساب تشابه الكلمات المفتاحية
function calculateKeywordSimilarity(keywords1, keywords2) {
    if (!keywords1.length || !keywords2.length) return 0;

    const set1 = new Set(keywords1);
    const set2 = new Set(keywords2);
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    return intersection.size / union.size; // Jaccard similarity
}

// تحسين معالجة الرسائل المتقدمة
class MessageAnalyzer {
    constructor() {
        this.textProcessor = new AdvancedTextProcessor();
        this.aiMatcher = new AdvancedAIMatcher();
    }

    // تحليل شامل للرسالة
    analyzeMessage(message) {
        const analysis = {
            original: message,
            processed: this.textProcessor.processText(message),
            keywords: this.textProcessor.extractKeywords(message),
            type: this.detectMessageType(message),
            language: this.detectLanguage(message),
            sentiment: this.analyzeSentiment(message),
            entities: this.extractEntities(message)
        };

        console.log(`📊 Message Analysis:`, analysis);
        return analysis;
    }

    // كشف نوع الرسالة
    detectMessageType(message) {
        const processed = this.textProcessor.processText(message);

        // أنماط الأسئلة
        const questionPatterns = ['كم', 'ماذا', 'متى', 'أين', 'كيف', 'لماذا', 'هل', '؟'];
        if (questionPatterns.some(pattern => processed.includes(pattern))) {
            return 'question';
        }

        // أنماط الطلبات
        const requestPatterns = ['أريد', 'أحتاج', 'يرجى', 'من فضلك', 'ممكن'];
        if (requestPatterns.some(pattern => processed.includes(pattern))) {
            return 'request';
        }

        // أنماط التحية
        const greetingPatterns = ['مرحبا', 'اهلا', 'السلام عليكم', 'صباح الخير'];
        if (greetingPatterns.some(pattern => processed.includes(pattern))) {
            return 'greeting';
        }

        // أنماط الشكر
        const thankPatterns = ['شكرا', 'تسلم', 'يعطيك العافية'];
        if (thankPatterns.some(pattern => processed.includes(pattern))) {
            return 'thanks';
        }

        return 'general';
    }

    // كشف اللغة
    detectLanguage(message) {
        const arabicChars = (message.match(/[\u0600-\u06FF]/g) || []).length;
        const englishChars = (message.match(/[a-zA-Z]/g) || []).length;
        const totalChars = arabicChars + englishChars;

        if (totalChars === 0) return 'unknown';

        const arabicRatio = arabicChars / totalChars;

        if (arabicRatio > 0.7) return 'arabic';
        if (arabicRatio < 0.3) return 'english';
        return 'mixed';
    }

    // تحليل المشاعر البسيط
    analyzeSentiment(message) {
        const processed = this.textProcessor.processText(message);

        const positiveWords = ['شكرا', 'ممتاز', 'رائع', 'جميل', 'أحب', 'سعيد'];
        const negativeWords = ['سيء', 'مشكلة', 'خطأ', 'لا أحب', 'زعلان', 'غاضب'];

        const positiveCount = positiveWords.filter(word => processed.includes(word)).length;
        const negativeCount = negativeWords.filter(word => processed.includes(word)).length;

        if (positiveCount > negativeCount) return 'positive';
        if (negativeCount > positiveCount) return 'negative';
        return 'neutral';
    }

    // استخراج الكيانات (أرقام، تواريخ، إلخ)
    extractEntities(message) {
        const entities = {
            numbers: [],
            dates: [],
            emails: [],
            phones: []
        };

        // استخراج الأرقام
        const numberMatches = message.match(/\d+/g);
        if (numberMatches) {
            entities.numbers = numberMatches.map(num => parseInt(num));
        }

        // استخراج الإيميلات
        const emailMatches = message.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g);
        if (emailMatches) {
            entities.emails = emailMatches;
        }

        // استخراج أرقام الهواتف
        const phoneMatches = message.match(/(\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/g);
        if (phoneMatches) {
            entities.phones = phoneMatches;
        }

        return entities;
    }
}

// وظيفة لإرسال الرد التلقائي المتقدم
async function sendAdvancedAutoReply(client, chatId, rule, accountName) {
    try {
        const messages = rule.messages;
        let sentMessages = [];

        // الحصول على معلومات المرسل
        const chat = await client.getChatById(chatId);
        const contact = await chat.getContact();
        const senderName = contact.pushname || contact.name || contact.number || 'صديق';

        // إرسال النص إذا كان موجوداً
        if (messages.text && messages.text.trim()) {
            try {
                const textMessage = messages.text.replace(/{name}/g, senderName);

                // التحقق من حالة العميل قبل الإرسال
                if (client && client.info && client.info.wid) {
                    await client.sendMessage(chatId, textMessage);
                    sentMessages.push('text');
                    console.log(`Advanced auto-reply text sent to ${chatId} from account ${accountName}: ${textMessage}`);
                } else {
                    console.error(`Client not ready for account ${accountName}`);
                }

                // تأخير قصير بين الرسائل
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                console.error(`Error sending advanced auto-reply text to ${chatId}:`, error.message);
            }
        }

        // إرسال الصورة إذا كانت موجودة
        if (messages.image && fs.existsSync(path.join(__dirname, 'public', messages.image))) {
            try {
                // التحقق من حالة العميل قبل الإرسال
                if (client && client.info && client.info.wid) {
                    const media = MessageMedia.fromFilePath(path.join(__dirname, 'public', messages.image));
                    await client.sendMessage(chatId, media);
                    sentMessages.push('image');
                    console.log(`Advanced auto-reply image sent to ${chatId} from account ${accountName}`);
                } else {
                    console.error(`Client not ready for account ${accountName}`);
                }

                // تأخير قصير بين الرسائل
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                console.error(`Error sending advanced auto-reply image to ${chatId}:`, error.message);
            }
        }

        // إرسال الملف إذا كان موجوداً
        if (messages.file && fs.existsSync(path.join(__dirname, 'public', messages.file))) {
            try {
                // التحقق من حالة العميل قبل الإرسال
                if (client && client.info && client.info.wid) {
                    const media = MessageMedia.fromFilePath(path.join(__dirname, 'public', messages.file));
                    await client.sendMessage(chatId, media);
                    sentMessages.push('file');
                    console.log(`Advanced auto-reply file sent to ${chatId} from account ${accountName}`);
                } else {
                    console.error(`Client not ready for account ${accountName}`);
                }

                // تأخير قصير بين الرسائل
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                console.error(`Error sending advanced auto-reply file to ${chatId}:`, error.message);
            }
        }

        // إرسال ملفات المجلد إذا كانت موجودة
        if (messages.folder && Array.isArray(messages.folder) && messages.folder.length > 0) {
            for (const folderFile of messages.folder) {
                if (fs.existsSync(path.join(__dirname, 'public', folderFile))) {
                    try {
                        const media = MessageMedia.fromFilePath(path.join(__dirname, 'public', folderFile));
                        await client.sendMessage(chatId, media);
                        console.log(`Advanced auto-reply folder file sent to ${chatId} from account ${accountName}`);

                        // تأخير قصير بين الرسائل
                        await new Promise(resolve => setTimeout(resolve, 500));
                    } catch (error) {
                        console.error(`Error sending advanced auto-reply folder file to ${chatId}:`, error);
                    }
                }
            }
            if (messages.folder.length > 0) {
                sentMessages.push('folder');
            }
        }

        console.log(`Advanced auto-reply completed for ${chatId}. Sent: ${sentMessages.join(', ')}`);
        return true;
    } catch (error) {
        console.error('Error sending advanced auto-reply:', error);
        return false;
    }
}

// API لتحميل إعدادات الرد التلقائي العام
app.get('/api/auto-reply/global/settings', (req, res) => {
    try {
        const settings = loadAutoReplySettings('global');
        res.json({
            success: true,
            settings: settings
        });
    } catch (error) {
        console.error('Error loading global auto-reply settings:', error);
        res.status(500).json({
            success: false,
            error: 'حدث خطأ في تحميل إعدادات الرد التلقائي العام'
        });
    }
});

// API لتحميل إعدادات الرد التلقائي العام
app.get('/api/auto-reply/global/load', (req, res) => {
    try {
        const settings = loadAutoReplySettings('global');
        res.json({
            success: true,
            settings: settings
        });
    } catch (error) {
        console.error('Error loading global auto-reply settings:', error);
        res.json({
            success: false,
            error: 'حدث خطأ في تحميل الإعدادات'
        });
    }
});

// API لحذف إعدادات الرد التلقائي العام
app.delete('/api/auto-reply/global/delete', (req, res) => {
    try {
        const settingsPath = path.join(__dirname, 'auto-reply-settings', 'global.json');

        // حذف ملف الإعدادات
        if (fs.existsSync(settingsPath)) {
            fs.unlinkSync(settingsPath);
        }

        // حذف ملفات الوسائط المرتبطة
        const globalFilesDir = path.join(__dirname, 'public', 'auto-reply-files', 'global');
        if (fs.existsSync(globalFilesDir)) {
            const files = fs.readdirSync(globalFilesDir);
            files.forEach(file => {
                fs.unlinkSync(path.join(globalFilesDir, file));
            });
            fs.rmdirSync(globalFilesDir);
        }

        res.json({
            success: true,
            message: 'تم حذف جميع إعدادات الرد التلقائي العام'
        });
    } catch (error) {
        console.error('Error deleting global auto-reply settings:', error);
        res.json({
            success: false,
            error: 'حدث خطأ في حذف الإعدادات'
        });
    }
});

// API لحفظ إعدادات الرد التلقائي العام
app.post('/api/auto-reply/global/save', upload.any(), (req, res) => {
    try {
        const settings = JSON.parse(req.body.settings);

        // إنشاء مجلد خاص للملفات العامة
        const globalFilesDir = path.join(__dirname, 'public', 'auto-reply-files');
        if (!fs.existsSync(globalFilesDir)) {
            fs.mkdirSync(globalFilesDir, { recursive: true });
        }

        // تحميل الإعدادات الحالية للحفاظ على الملفات الموجودة
        const currentSettings = loadAutoReplySettings('global');
        if (currentSettings && currentSettings.message) {
            // الحفاظ على الملفات الموجودة إذا لم يتم رفع ملفات جديدة
            if (currentSettings.message.imagePath && !req.files.find(f => f.fieldname === 'image')) {
                settings.message.imagePath = currentSettings.message.imagePath;
            }
            if (currentSettings.message.filePath && !req.files.find(f => f.fieldname === 'file')) {
                settings.message.filePath = currentSettings.message.filePath;
            }
            if (currentSettings.message.folderFiles && !req.files.find(f => f.fieldname.startsWith('folder_'))) {
                settings.message.folderFiles = currentSettings.message.folderFiles;
            }
        }

        // حفظ الملفات الجديدة
        if (req.files && req.files.length > 0) {
            req.files.forEach(file => {
                const timestamp = Date.now();
                const fileName = `global_${timestamp}_${file.originalname}`;
                const filePath = path.join(globalFilesDir, fileName);

                // نسخ الملف إلى المجلد المخصص
                fs.copyFileSync(file.path, filePath);

                // تحديد نوع الملف وحفظ المسار
                if (file.fieldname === 'image') {
                    settings.message.imagePath = `/auto-reply-files/${fileName}`;
                } else if (file.fieldname === 'file') {
                    settings.message.filePath = `/auto-reply-files/${fileName}`;
                } else if (file.fieldname.startsWith('folder_')) {
                    if (!settings.message.folderFiles) {
                        settings.message.folderFiles = [];
                    }
                    settings.message.folderFiles.push(`/auto-reply-files/${fileName}`);
                }

                // حذف الملف المؤقت
                fs.unlinkSync(file.path);
            });
        }

        const saved = saveAutoReplySettings('global', settings);

        if (saved) {
            res.json({
                success: true,
                message: 'تم حفظ إعدادات الرد التلقائي العام بنجاح'
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'حدث خطأ في حفظ الإعدادات'
            });
        }
    } catch (error) {
        console.error('Error saving global auto-reply settings:', error);
        res.status(500).json({
            success: false,
            error: 'حدث خطأ في حفظ إعدادات الرد التلقائي العام'
        });
    }
});

// API لتحميل إعدادات الرد التلقائي للحساب
app.get('/api/auto-reply/account/:accountName/settings', (req, res) => {
    try {
        const { accountName } = req.params;
        const settings = loadAutoReplySettings('account', accountName);
        res.json({
            success: true,
            settings: settings
        });
    } catch (error) {
        console.error('Error loading account auto-reply settings:', error);
        res.status(500).json({
            success: false,
            error: 'حدث خطأ في تحميل إعدادات الرد التلقائي للحساب'
        });
    }
});

// API لحفظ إعدادات الرد التلقائي للحساب (النظام الجديد متعدد الأنواع)
app.post('/api/auto-reply/account/:accountName/save', upload.fields([
    { name: 'image', maxCount: 1 },
    { name: 'file', maxCount: 1 }
].concat(
    // إضافة حقول ديناميكية لملفات المجلد
    Array.from({ length: 50 }, (_, i) => ({ name: `folder_${i}`, maxCount: 1 }))
)), (req, res) => {
    try {
        const { accountName } = req.params;
        const settings = JSON.parse(req.body.settings);

        // تحميل الإعدادات الحالية للحفاظ على الملفات الموجودة
        const currentSettings = loadAutoReplySettings('account', accountName) || { message: {} };

        // حفظ الملفات الجديدة إذا كانت موجودة
        if (req.files) {
            // معالجة الصورة
            if (req.files.image && req.files.image[0]) {
                // حذف الصورة القديمة إذا كانت موجودة
                if (currentSettings.message.imagePath && fs.existsSync(currentSettings.message.imagePath)) {
                    try {
                        fs.unlinkSync(currentSettings.message.imagePath);
                    } catch (error) {
                        console.warn('Could not delete old image:', error.message);
                    }
                }

                const imagePath = path.join(autoReplyDir, `${accountName}_image_${Date.now()}_` + req.files.image[0].originalname);
                fs.copyFileSync(req.files.image[0].path, imagePath);
                settings.message.imagePath = imagePath;
                fs.unlinkSync(req.files.image[0].path);
            } else if (currentSettings.message.imagePath) {
                // الحفاظ على الصورة الحالية إذا لم يتم رفع صورة جديدة
                settings.message.imagePath = currentSettings.message.imagePath;
            }

            // معالجة الملف
            if (req.files.file && req.files.file[0]) {
                // حذف الملف القديم إذا كان موجوداً
                if (currentSettings.message.filePath && fs.existsSync(currentSettings.message.filePath)) {
                    try {
                        fs.unlinkSync(currentSettings.message.filePath);
                    } catch (error) {
                        console.warn('Could not delete old file:', error.message);
                    }
                }

                const filePath = path.join(autoReplyDir, `${accountName}_file_${Date.now()}_` + req.files.file[0].originalname);
                fs.copyFileSync(req.files.file[0].path, filePath);
                settings.message.filePath = filePath;
                fs.unlinkSync(req.files.file[0].path);
            } else if (currentSettings.message.filePath) {
                // الحفاظ على الملف الحالي إذا لم يتم رفع ملف جديد
                settings.message.filePath = currentSettings.message.filePath;
            }

            // معالجة ملفات المجلد
            const folderFiles = [];
            const folderFileKeys = Object.keys(req.files).filter(key => key.startsWith('folder_')).sort();

            if (folderFileKeys.length > 0) {
                // حذف ملفات المجلد القديمة
                if (currentSettings.message.folderFiles && currentSettings.message.folderFiles.length > 0) {
                    currentSettings.message.folderFiles.forEach(oldFilePath => {
                        if (fs.existsSync(oldFilePath)) {
                            try {
                                fs.unlinkSync(oldFilePath);
                            } catch (error) {
                                console.warn('Could not delete old folder file:', error.message);
                            }
                        }
                    });
                }

                // حفظ ملفات المجلد الجديدة
                folderFileKeys.forEach(key => {
                    const file = req.files[key][0];
                    const folderFilePath = path.join(autoReplyDir, `${accountName}_folder_${Date.now()}_` + file.originalname);
                    fs.copyFileSync(file.path, folderFilePath);
                    folderFiles.push(folderFilePath);
                    fs.unlinkSync(file.path);
                });

                settings.message.folderFiles = folderFiles;
            } else if (currentSettings.message.folderFiles) {
                // الحفاظ على ملفات المجلد الحالية إذا لم يتم رفع ملفات جديدة
                settings.message.folderFiles = currentSettings.message.folderFiles;
            }
        } else {
            // الحفاظ على جميع الملفات الحالية إذا لم يتم رفع ملفات جديدة
            if (currentSettings.message.imagePath) {
                settings.message.imagePath = currentSettings.message.imagePath;
            }
            if (currentSettings.message.filePath) {
                settings.message.filePath = currentSettings.message.filePath;
            }
            if (currentSettings.message.folderFiles) {
                settings.message.folderFiles = currentSettings.message.folderFiles;
            }
        }

        const saved = saveAutoReplySettings('account', settings, accountName);

        if (saved) {
            res.json({
                success: true,
                message: 'تم حفظ إعدادات الرد التلقائي للحساب بنجاح',
                settings: settings
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'حدث خطأ في حفظ الإعدادات'
            });
        }
    } catch (error) {
        console.error('Error saving account auto-reply settings:', error);
        res.status(500).json({
            success: false,
            error: 'حدث خطأ في حفظ إعدادات الرد التلقائي للحساب'
        });
    }
});

// API لحذف إعدادات الرد التلقائي للحساب
app.delete('/api/auto-reply/account/:accountName/delete', (req, res) => {
    try {
        const { accountName } = req.params;
        const settingsPath = path.join(autoReplyDir, `${accountName}.json`);

        // حذف ملف الإعدادات إذا كان موجوداً
        if (fs.existsSync(settingsPath)) {
            fs.unlinkSync(settingsPath);
        }

        // حذف الملفات المرفقة (الصور والملفات)
        const autoReplyFiles = fs.readdirSync(autoReplyDir).filter(file =>
            file.startsWith(`${accountName}_image_`) ||
            file.startsWith(`${accountName}_file_`) ||
            file.startsWith(`${accountName}_folder_`)
        );

        autoReplyFiles.forEach(file => {
            const filePath = path.join(autoReplyDir, file);
            try {
                fs.unlinkSync(filePath);
                console.log(`تم حذف الملف: ${file}`);
            } catch (error) {
                console.error(`خطأ في حذف الملف ${file}:`, error);
            }
        });

        res.json({
            success: true,
            message: 'تم حذف جميع إعدادات الرد التلقائي للحساب بنجاح',
            deletedFiles: autoReplyFiles.length
        });
    } catch (error) {
        console.error('Error deleting account auto-reply settings:', error);
        res.status(500).json({
            success: false,
            error: 'حدث خطأ في حذف إعدادات الرد التلقائي للحساب'
        });
    }
});

// API لتحميل إعدادات وضع الرد التلقائي
app.get('/api/auto-reply/mode/settings', (req, res) => {
    try {
        const settingsPath = path.join(autoReplyDir, 'mode.json');
        let mode = 'global'; // الوضع الافتراضي

        if (fs.existsSync(settingsPath)) {
            const data = fs.readFileSync(settingsPath, 'utf8');
            const settings = JSON.parse(data);
            mode = settings.mode || 'global';
        }

        res.json({
            success: true,
            mode: mode
        });
    } catch (error) {
        console.error('Error loading auto-reply mode settings:', error);
        res.status(500).json({
            success: false,
            error: 'حدث خطأ في تحميل إعدادات وضع الرد التلقائي'
        });
    }
});

// API لحفظ إعدادات وضع الرد التلقائي
app.post('/api/auto-reply/mode/save', (req, res) => {
    try {
        const { mode } = req.body;

        if (!mode || !['global', 'account'].includes(mode)) {
            return res.status(400).json({
                success: false,
                error: 'وضع الرد التلقائي غير صحيح'
            });
        }

        const settingsPath = path.join(autoReplyDir, 'mode.json');
        const settings = { mode: mode };

        fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2));

        res.json({
            success: true,
            message: 'تم حفظ إعدادات وضع الرد التلقائي بنجاح'
        });
    } catch (error) {
        console.error('Error saving auto-reply mode settings:', error);
        res.status(500).json({
            success: false,
            error: 'حدث خطأ في حفظ إعدادات وضع الرد التلقائي'
        });
    }
});

// ===== API للرد التلقائي المتقدم =====











// API لتحميل إعدادات الرد التلقائي المتقدم
app.get('/api/auto-reply/advanced/load', (req, res) => {
    try {
        const accountName = req.query.account; // اسم الحساب من query parameter
        const settings = loadAdvancedAutoReplySettings(accountName);

        if (settings) {
            res.json({
                success: true,
                settings: settings
            });
        } else {
            // إرجاع إعدادات افتراضية
            const defaultSettings = {
                version: "1.0",
                enabled: false,
                rules: [],
                ai_fallback: {
                    enabled: true,
                    similarity_threshold: 0.7,
                    default_response: {
                        text: "شكراً لك، سنرد عليك قريباً",
                        image: null,
                        file: null,
                        folder: []
                    }
                },
                account_name: accountName || null,
                created_at: new Date().toISOString(),
                last_updated: new Date().toISOString()
            };

            res.json({
                success: true,
                settings: defaultSettings
            });
        }
    } catch (error) {
        console.error('Error loading advanced auto-reply settings:', error);
        res.status(500).json({
            success: false,
            error: 'حدث خطأ في تحميل إعدادات الرد التلقائي المتقدم'
        });
    }
});

// API لحفظ إعدادات الرد التلقائي المتقدم
app.post('/api/auto-reply/advanced/save', (req, res) => {
    try {
        const settings = req.body;
        const accountName = req.query.account; // اسم الحساب من query parameter

        // التحقق من صحة البيانات
        if (!settings || typeof settings !== 'object') {
            return res.status(400).json({
                success: false,
                error: 'بيانات غير صحيحة'
            });
        }

        // إضافة معلومات الحساب
        settings.account_name = accountName || null;

        // حفظ الإعدادات
        const saved = saveAdvancedAutoReplySettings(settings, accountName);

        if (saved) {
            res.json({
                success: true,
                message: 'تم حفظ إعدادات الرد التلقائي المتقدم بنجاح'
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'خطأ في حفظ الإعدادات'
            });
        }
    } catch (error) {
        console.error('Error saving advanced auto-reply settings:', error);
        res.status(500).json({
            success: false,
            error: 'حدث خطأ في حفظ إعدادات الرد التلقائي المتقدم'
        });
    }
});

// API لإضافة قاعدة جديدة للرد التلقائي المتقدم
app.post('/api/auto-reply/advanced/add-rule', upload.fields([
    { name: 'image', maxCount: 1 },
    { name: 'file', maxCount: 1 }
].concat(
    // إضافة حقول ديناميكية لملفات المجلد
    Array.from({ length: 50 }, (_, i) => ({ name: `folder_${i}`, maxCount: 1 }))
)), (req, res) => {
    try {
        const ruleData = JSON.parse(req.body.ruleData);
        const filePaths = {};

        // معالجة الصورة
        if (req.files.image && req.files.image[0]) {
            const imageFile = req.files.image[0];
            const imagePath = `/auto-reply-files/advanced_${ruleData.id}_image_${imageFile.originalname}`;
            filePaths.image = imagePath;
        }

        // معالجة الملف
        if (req.files.file && req.files.file[0]) {
            const file = req.files.file[0];
            const filePath = `/auto-reply-files/advanced_${ruleData.id}_file_${file.originalname}`;
            filePaths.file = filePath;
        }

        // معالجة ملفات المجلد
        const folderFiles = [];
        for (let i = 0; i < 50; i++) {
            const fieldName = `folder_${i}`;
            if (req.files[fieldName] && req.files[fieldName][0]) {
                const file = req.files[fieldName][0];
                const filePath = `/auto-reply-files/advanced_${ruleData.id}_folder_${i}_${file.originalname}`;
                folderFiles.push(filePath);
            }
        }
        if (folderFiles.length > 0) {
            filePaths.folder = folderFiles;
        }

        res.json({
            success: true,
            message: 'تم إضافة القاعدة بنجاح',
            filePaths: filePaths
        });
    } catch (error) {
        console.error('Error adding advanced auto-reply rule:', error);
        res.status(500).json({
            success: false,
            error: 'حدث خطأ في إضافة القاعدة'
        });
    }
});

// API لتحديث قاعدة موجودة للرد التلقائي المتقدم
app.post('/api/auto-reply/advanced/update-rule', upload.fields([
    { name: 'image', maxCount: 1 },
    { name: 'file', maxCount: 1 }
].concat(
    // إضافة حقول ديناميكية لملفات المجلد
    Array.from({ length: 50 }, (_, i) => ({ name: `folder_${i}`, maxCount: 1 }))
)), (req, res) => {
    try {
        const ruleData = JSON.parse(req.body.ruleData);
        const ruleIndex = parseInt(req.body.ruleIndex);
        const filePaths = {};

        // معالجة الصورة الجديدة
        if (req.files.image && req.files.image[0]) {
            const imageFile = req.files.image[0];
            const imagePath = `/auto-reply-files/advanced_${ruleData.id}_image_${imageFile.originalname}`;
            filePaths.image = imagePath;
        }

        // معالجة الملف الجديد
        if (req.files.file && req.files.file[0]) {
            const file = req.files.file[0];
            const filePath = `/auto-reply-files/advanced_${ruleData.id}_file_${file.originalname}`;
            filePaths.file = filePath;
        }

        // معالجة ملفات المجلد الجديدة
        const folderFiles = [];
        for (let i = 0; i < 50; i++) {
            const fieldName = `folder_${i}`;
            if (req.files[fieldName] && req.files[fieldName][0]) {
                const file = req.files[fieldName][0];
                const filePath = `/auto-reply-files/advanced_${ruleData.id}_folder_${i}_${file.originalname}`;
                folderFiles.push(filePath);
            }
        }
        if (folderFiles.length > 0) {
            filePaths.folder = folderFiles;
        }

        res.json({
            success: true,
            message: 'تم تحديث القاعدة بنجاح',
            filePaths: filePaths
        });
    } catch (error) {
        console.error('Error updating advanced auto-reply rule:', error);
        res.status(500).json({
            success: false,
            error: 'حدث خطأ في تحديث القاعدة'
        });
    }
});

// الصفحة الرئيسية
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// وظيفة لاستعادة الاتصالات المحفوظة عند بدء التشغيل
async function restoreConnections() {
    try {
        const authDir = path.join(__dirname, '.wwebjs_auth');
        if (!fs.existsSync(authDir)) {
            console.log('No .wwebjs_auth directory found. Skipping connection restoration.');
            return;
        }

        const sessionFolders = fs.readdirSync(authDir).filter(folder => folder.startsWith('session-'));
        console.log(`Found ${sessionFolders.length} saved sessions. Attempting to restore connections...`);

        for (const folderName of sessionFolders) {
            const accountName = folderName.replace('session-', '');
            try {
                // التحقق مما إذا كان الاتصال قيد التهيئة بالفعل لتجنب التكرار
                if (connections[accountName] && (connections[accountName].status === 'initializing' || connections[accountName].status === 'qr_ready')) {
                    console.log(`Connection for ${accountName} is already being initialized. Skipping duplicate restoration.`);
                    continue;
                }

                console.log(`Restoring connection for ${accountName}...`);
                // لا نحتاج إلى رقم الهاتف هنا لأن LocalAuth تتعامل معه
                // createWhatsAppConnection ستستخدم LocalAuth التي ستحمل الجلسة المحفوظة
                await createWhatsAppConnection(accountName, null);
            } catch (error) {
                console.error(`Error restoring connection for ${accountName}:`, error);
            }
        }
        console.log('Connection restoration process completed.');
    } catch (error) {
        console.error('Error in restoreConnections:', error);
    }
}

// إضافة مسار API جديد لإصلاح الحسابات المعطوبة
app.post('/api/repair-account/:accountName', async (req, res) => {
    try {
        const { accountName } = req.params;

        // التحقق من وجود الحساب
        const sessionDir = path.join(__dirname, '.wwebjs_auth', `session-${accountName}`);
        if (!fs.existsSync(sessionDir)) {
            return res.status(404).json({ error: 'الحساب غير موجود' });
        }

        // إذا كان الاتصال موجوداً، قم بتدميره أولاً
        if (connections[accountName]) {
            try {
                if (connections[accountName].client) {
                    await connections[accountName].client.destroy().catch(e => console.warn(`Destroy error: ${e.message}`));
                }
                delete connections[accountName];
                console.log(`تم تدمير الاتصال الحالي للحساب ${accountName}`);
            } catch (e) {
                console.warn(`فشل في تنظيف الاتصال السابق: ${e.message}`);
            }
        }

        // حذف مجلد الجلسة المعطوب
        try {
            fs.rmSync(sessionDir, { recursive: true, force: true });
            console.log(`تم حذف مجلد الجلسة المعطوب للحساب ${accountName}`);
        } catch (err) {
            console.error(`خطأ في حذف مجلد الجلسة المعطوب للحساب ${accountName}:`, err);
        }

        // إنشاء اتصال جديد
        const savedAccountInfo = loadAccountData(accountName);
        const phoneNumber = savedAccountInfo?.number || req.body.phoneNumber;

        const success = await createWhatsAppConnection(accountName, phoneNumber);

        if (success) {
            return res.status(200).json({
                success: true,
                message: `تم إصلاح الحساب ${accountName} بنجاح، يرجى متابعة حالة الاتصال`
            });
        } else {
            return res.status(500).json({ error: 'فشل في إصلاح الحساب، يرجى المحاولة مرة أخرى' });
        }
    } catch (error) {
        console.error('Error repairing account:', error);
        return res.status(500).json({ error: 'حدث خطأ أثناء إصلاح الحساب' });
    }
});

// بدء الخادم واستعادة الاتصالات
app.listen(PORT, async () => {
    console.log(`Server running on port ${PORT}`);
    // تم تعليق استدعاء restoreConnections ليتم استدعاؤها فقط عند الطلب من لوحة التحكم
    // await restoreConnections();
});

// مسار API جديد لاستعادة الاتصالات عند الطلب من لوحة التحكم
app.post('/api/restore-connections', async (req, res) => {
    try {
        await restoreConnections();
        res.status(200).json({ success: true, message: 'تم استعادة الاتصالات بنجاح.' });
    } catch (error) {
        console.error('خطأ أثناء استعادة الاتصالات:', error);
        res.status(500).json({ success: false, message: 'حدث خطأ أثناء استعادة الاتصالات.' });
    }
});

// API للحصول على ملف groups_data.json مباشرة
app.get('/api/get-groups-json/:accountName', (req, res) => {
    const { accountName } = req.params;

    try {
        // مسار ملف بيانات المجموعات
        const groupsDataPath = path.join(__dirname, 'sessions', accountName, 'groups_data.json');

        // التحقق من وجود الملف
        if (!fs.existsSync(groupsDataPath)) {
            return res.status(404).json({ error: 'ملف بيانات المجموعات غير موجود' });
        }

        // قراءة الملف وإرساله
        const groupsData = JSON.parse(fs.readFileSync(groupsDataPath, 'utf8'));
        return res.json(groupsData);
    } catch (error) {
        console.error(`Error reading groups JSON for ${accountName}:`, error);
        return res.status(500).json({ error: 'حدث خطأ أثناء قراءة ملف بيانات المجموعات' });
    }
});

// API لتنفيذ عمليات على المجموعات
app.post('/api/groups/:accountName/:operation', async (req, res) => {
    const { accountName, operation } = req.params;
    const { groupId, data } = req.body;

    try {
        // التحقق من وجود الحساب
        if (!connections[accountName]) {
            return res.status(404).json({ error: 'الحساب غير موجود' });
        }

        // التحقق من اتصال الحساب
        if (connections[accountName].status !== 'connected') {
            return res.status(400).json({ error: 'الحساب غير متصل' });
        }

        if (!groupId) {
            return res.status(400).json({ error: 'معرف المجموعة مطلوب' });
        }

        const client = connections[accountName].client;

        // الحصول على المحادثات
        const chats = await client.getChats();

        // البحث عن المجموعة المطلوبة
        const group = chats.find(chat => chat.isGroup && chat.id._serialized === groupId);

        if (!group) {
            return res.status(404).json({ error: 'المجموعة غير موجودة' });
        }

        // تنفيذ العملية المطلوبة
        switch (operation) {
            case 'get-invite-code':
                try {
                    const inviteCode = await group.getInviteCode();
                    return res.json({
                        success: true,
                        inviteCode: inviteCode,
                        inviteLink: `https://chat.whatsapp.com/${inviteCode}`
                    });
                } catch (error) {
                    console.error(`Error getting invite code: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في الحصول على رمز الدعوة' });
                }

            case 'revoke-invite':
                try {
                    const newInviteCode = await group.revokeInvite();
                    return res.json({
                        success: true,
                        inviteCode: newInviteCode,
                        inviteLink: `https://chat.whatsapp.com/${newInviteCode}`
                    });
                } catch (error) {
                    console.error(`Error revoking invite: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في إلغاء وإعادة إنشاء رمز الدعوة' });
                }

            case 'set-description':
                if (!data || typeof data.description !== 'string') {
                    return res.status(400).json({ error: 'وصف المجموعة مطلوب' });
                }

                try {
                    const success = await group.setDescription(data.description);
                    return res.json({
                        success: success,
                        message: success ? 'تم تحديث وصف المجموعة بنجاح' : 'فشل في تحديث وصف المجموعة'
                    });
                } catch (error) {
                    console.error(`Error setting description: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في تحديث وصف المجموعة' });
                }

            case 'set-subject':
                if (!data || typeof data.subject !== 'string') {
                    return res.status(400).json({ error: 'اسم المجموعة مطلوب' });
                }

                try {
                    const success = await group.setSubject(data.subject);
                    return res.json({
                        success: success,
                        message: success ? 'تم تحديث اسم المجموعة بنجاح' : 'فشل في تحديث اسم المجموعة'
                    });
                } catch (error) {
                    console.error(`Error setting subject: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في تحديث اسم المجموعة' });
                }

            case 'promote-participants':
                if (!data || !Array.isArray(data.participantIds) || data.participantIds.length === 0) {
                    return res.status(400).json({ error: 'معرفات المشاركين مطلوبة' });
                }

                try {
                    const result = await group.promoteParticipants(data.participantIds);
                    return res.json({
                        success: result && result.status === 200,
                        result,
                        message: result && result.status === 200 ? 'تمت ترقية المشاركين بنجاح' : 'فشل في ترقية بعض المشاركين'
                    });
                } catch (error) {
                    console.error(`Error promoting participants: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في ترقية المشاركين' });
                }

            case 'demote-participants':
                if (!data || !Array.isArray(data.participantIds) || data.participantIds.length === 0) {
                    return res.status(400).json({ error: 'معرفات المشاركين مطلوبة' });
                }

                try {
                    const result = await group.demoteParticipants(data.participantIds);
                    return res.json({
                        success: result && result.status === 200,
                        result,
                        message: result && result.status === 200 ? 'تم إلغاء ترقية المشاركين بنجاح' : 'فشل في إلغاء ترقية بعض المشاركين'
                    });
                } catch (error) {
                    console.error(`Error demoting participants: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في إلغاء ترقية المشاركين' });
                }

            case 'add-participants':
                if (!data || !Array.isArray(data.participantIds) || data.participantIds.length === 0) {
                    return res.status(400).json({ error: 'معرفات المشاركين مطلوبة' });
                }

                try {
                    const result = await group.addParticipants(data.participantIds, data.options || {});
                    return res.json({
                        success: true,
                        result,
                        message: 'تمت محاولة إضافة المشاركين'
                    });
                } catch (error) {
                    console.error(`Error adding participants: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في إضافة المشاركين' });
                }

            case 'remove-participants':
                if (!data || !Array.isArray(data.participantIds) || data.participantIds.length === 0) {
                    return res.status(400).json({ error: 'معرفات المشاركين مطلوبة' });
                }

                try {
                    const result = await group.removeParticipants(data.participantIds);
                    return res.json({
                        success: result && result.status === 200,
                        result,
                        message: result && result.status === 200 ? 'تمت إزالة المشاركين بنجاح' : 'فشل في إزالة بعض المشاركين'
                    });
                } catch (error) {
                    console.error(`Error removing participants: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في إزالة المشاركين' });
                }

            case 'leave-group':
                try {
                    await group.leave();
                    return res.json({
                        success: true,
                        message: 'تم مغادرة المجموعة بنجاح'
                    });
                } catch (error) {
                    console.error(`Error leaving group: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في مغادرة المجموعة' });
                }

            case 'approve-membership-requests':
                if (!data || !Array.isArray(data.requesterIds) || data.requesterIds.length === 0) {
                    return res.status(400).json({ error: 'معرفات الطلبات مطلوبة' });
                }

                try {
                    const options = { requesterIds: data.requesterIds };
                    const result = await group.approveGroupMembershipRequests(options);
                    return res.json({
                        success: true,
                        result,
                        message: 'تمت الموافقة على طلبات العضوية'
                    });
                } catch (error) {
                    console.error(`Error approving membership requests: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في الموافقة على طلبات العضوية' });
                }

            case 'reject-membership-requests':
                if (!data || !Array.isArray(data.requesterIds) || data.requesterIds.length === 0) {
                    return res.status(400).json({ error: 'معرفات الطلبات مطلوبة' });
                }

                try {
                    const options = { requesterIds: data.requesterIds };
                    const result = await group.rejectGroupMembershipRequests(options);
                    return res.json({
                        success: true,
                        result,
                        message: 'تم رفض طلبات العضوية'
                    });
                } catch (error) {
                    console.error(`Error rejecting membership requests: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في رفض طلبات العضوية' });
                }

            case 'set-admins-only-messages':
                try {
                    const adminsOnly = data && typeof data.adminsOnly === 'boolean' ? data.adminsOnly : true;
                    const success = await group.setMessagesAdminsOnly(adminsOnly);
                    return res.json({
                        success,
                        message: success
                            ? `تم ${adminsOnly ? 'تفعيل' : 'تعطيل'} خاصية الرسائل للمشرفين فقط`
                            : 'فشل في تغيير إعدادات الرسائل'
                    });
                } catch (error) {
                    console.error(`Error setting messages admins only: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في تغيير إعدادات الرسائل' });
                }

            case 'set-admins-only-settings':
                try {
                    const adminsOnly = data && typeof data.adminsOnly === 'boolean' ? data.adminsOnly : true;
                    const success = await group.setInfoAdminsOnly(adminsOnly);
                    return res.json({
                        success,
                        message: success
                            ? `تم ${adminsOnly ? 'تفعيل' : 'تعطيل'} خاصية تعديل معلومات المجموعة للمشرفين فقط`
                            : 'فشل في تغيير إعدادات المجموعة'
                    });
                } catch (error) {
                    console.error(`Error setting info admins only: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في تغيير إعدادات المجموعة' });
                }

            default:
                return res.status(400).json({ error: 'عملية غير معتمدة' });
        }

    } catch (error) {
        console.error(`Error in group operation ${operation} for ${accountName}:`, error);
        return res.status(500).json({ error: 'حدث خطأ أثناء تنفيذ العملية' });
    }
});

// API لإنشاء مجموعة جديدة
app.post('/api/create-group/:accountName', async (req, res) => {
    const { accountName } = req.params;
    const { title, participants, options } = req.body;

    try {
        // التحقق من وجود الحساب
        if (!connections[accountName]) {
            return res.status(404).json({ error: 'الحساب غير موجود' });
        }

        // التحقق من اتصال الحساب
        if (connections[accountName].status !== 'connected') {
            return res.status(400).json({ error: 'الحساب غير متصل' });
        }

        // التحقق من توفر بيانات المجموعة المطلوبة
        if (!title || typeof title !== 'string') {
            return res.status(400).json({ error: 'عنوان المجموعة مطلوب' });
        }

        if (!participants || !Array.isArray(participants) || participants.length === 0) {
            return res.status(400).json({ error: 'يجب توفير مشارك واحد على الأقل' });
        }

        const client = connections[accountName].client;

        console.log(`Creating group "${title}" for ${accountName} with ${participants.length} participants`);

        // تجهيز الخيارات
        const createOptions = {};
        if (options) {
            if (typeof options.messageTimer === 'number') {
                createOptions.messageTimer = options.messageTimer;
            }
            if (typeof options.parentGroupId === 'string') {
                createOptions.parentGroupId = options.parentGroupId;
            }
            if (typeof options.autoSendInviteV4 === 'boolean') {
                createOptions.autoSendInviteV4 = options.autoSendInviteV4;
            }
            if (typeof options.comment === 'string') {
                createOptions.comment = options.comment;
            }
        }

        // إنشاء المجموعة
        const groupResult = await client.createGroup(title, participants, createOptions);

        // تحليل نتائج إنشاء المجموعة
        const groupData = {
            success: true,
            groupId: groupResult.gid._serialized,
            title: groupResult.title,
            participants: {}
        };

        // تحليل نتائج إضافة المشاركين
        for (const participantId in groupResult.participants) {
            const participant = groupResult.participants[participantId];
            groupData.participants[participantId] = {
                statusCode: participant.statusCode,
                message: participant.message,
                isGroupCreator: participant.isGroupCreator || false,
                isInviteV4Sent: participant.isInviteV4Sent || false
            };
        }

        console.log(`Group "${title}" created successfully with ID: ${groupData.groupId}`);

        // إضافة وصف للمجموعة إذا تم توفيره
        if (options && typeof options.description === 'string') {
            try {
                // الحصول على المجموعة المنشأة حديثًا
                const chats = await client.getChats();
                const newGroup = chats.find(chat => chat.isGroup && chat.id._serialized === groupData.groupId);

                if (newGroup) {
                    await newGroup.setDescription(options.description);
                    groupData.description = options.description;
                    console.log(`Description set for group "${title}"`);
                }
            } catch (descriptionError) {
                console.error(`Error setting description for new group: ${descriptionError.message}`);
                groupData.descriptionError = 'فشل في تعيين وصف المجموعة';
            }
        }

        // محاولة الحصول على رابط دعوة للمجموعة
        try {
            // الحصول على المجموعة المنشأة حديثًا
            const chats = await client.getChats();
            const newGroup = chats.find(chat => chat.isGroup && chat.id._serialized === groupData.groupId);

            if (newGroup) {
                const inviteCode = await newGroup.getInviteCode();
                groupData.inviteCode = inviteCode;
                groupData.inviteLink = `https://chat.whatsapp.com/${inviteCode}`;
                console.log(`Invite code generated for group "${title}": ${inviteCode}`);
            }
        } catch (inviteError) {
            console.error(`Error getting invite code for new group: ${inviteError.message}`);
            groupData.inviteError = 'فشل في الحصول على رابط الدعوة';
        }

        return res.json(groupData);

    } catch (error) {
        console.error(`Error creating group for ${accountName}:`, error);
        return res.status(500).json({ error: 'حدث خطأ أثناء إنشاء المجموعة', message: error.message });
    }
});

/**
 * تحويل معرف LID إلى معرف C.US
 * @param {string} lid معرف LID مثل **********@lid
 * @returns {string|null} معرف C.US مثل <EMAIL> أو null إذا لم يكن المدخل صالحاً
 */
function lidToCus(lid) {
    if (!lid || typeof lid !== 'string') return null;

    // التحقق من أن المعرف lid صالح
    const match = lid.match(/^(\d+)@lid$/);
    if (!match) return null;

    // استخراج الرقم واستبدال @lid بـ @c.us
    const phoneNumber = match[1];
    return `${phoneNumber}@c.us`;
}

/**
 * تحويل معرف C.US إلى معرف LID
 * @param {string} cus معرف C.US مثل <EMAIL>
 * @returns {string|null} معرف LID مثل **********@lid أو null إذا لم يكن المدخل صالحاً
 */
function cusToLid(cus) {
    if (!cus || typeof cus !== 'string') return null;

    // التحقق من أن المعرف c.us صالح
    const match = cus.match(/^(\d+)@c\.us$/);
    if (!match) return null;

    // استخراج الرقم واستبدال @c.us بـ @lid
    const phoneNumber = match[1];
    return `${phoneNumber}@lid`;
}

/**
 * تحويل معرف LID إلى رقم هاتف حقيقي
 * @param {string} lid معرف LID
 * @returns {string} رقم الهاتف الحقيقي
 */
function convertLidToRealPhone(lid) {
    if (!lid) return null;

    // إزالة @lid من المعرف
    const number = lid.replace('@lid', '');

    // التحقق من أن الرقم يبدأ بـ 2 (رقم مصري)
    if (number.startsWith('2')) {
        return number;
    }

    // إذا كان الرقم يبدأ بـ 1 (رقم فيسبوك)
    if (number.startsWith('1')) {
        // تحويل رقم فيسبوك إلى رقم مصري
        // نضيف 2 في البداية ونحذف 1
        return '2' + number.substring(1);
    }

    return number;
}

/**
 * تحويل معرف CUS إلى رقم هاتف حقيقي
 * @param {string} cus معرف CUS
 * @returns {string} رقم الهاتف الحقيقي
 */
function convertCusToRealPhone(cus) {
    if (!cus) return null;

    // إزالة @c.us من المعرف
    const number = cus.replace('@c.us', '');

    // التحقق من أن الرقم يبدأ بـ 2 (رقم مصري)
    if (number.startsWith('2')) {
        return number;
    }

    // إذا كان الرقم يبدأ بـ 1 (رقم فيسبوك)
    if (number.startsWith('1')) {
        // تحويل رقم فيسبوك إلى رقم مصري
        // نضيف 2 في البداية ونحذف 1
        return '2' + number.substring(1);
    }

    return number;
}

// In-memory storage for operations status (for demonstration/basic tracking)
const ongoingOperations = {};

/**
 * Helper function to update operation completion status and store results.
 * This is a basic in-memory implementation.
 * @param {string} operationId - Unique identifier for the operation.
 * @param {number} total - Total number of items processed or expected.
 * @param {number} success - Number of successful items.
 * @param {number} failed - Number of failed items.
 * @param {Array} results - Array of individual item results.
 * @param {string} [errorMessage=null] - General error message for the operation if it failed overall.
 */
function updateOperationCompletionStatus(operationId, total, success, failed, results, errorMessage = null) {
    console.log(`Updating operation ${operationId} status: Total=${total}, Success=${success}, Failed=${failed}`);

    // Ensure the operation exists in storage or create it
    if (!ongoingOperations[operationId]) {
        ongoingOperations[operationId] = {
            id: operationId,
            startTime: Date.now(),
            status: 'processing', // Initial status if not already set
            progress: 0,
            totalExpected: total,
            successCount: 0,
            failCount: 0,
            errors: [],
            results: []
        };
    }

    const operation = ongoingOperations[operationId];

    // Update counts
    operation.successCount = success;
    operation.failCount = failed;

    // Update results (can merge or replace depending on granularity needed)
    // For simplicity, we'll just store the final batch of results provided
    operation.results = results;

    // Determine overall status
    if (errorMessage) {
        operation.status = 'failed';
        operation.error = errorMessage;
        operation.progress = 100; // Consider failed operations as completed for progress bar
    } else if (success + failed >= total && total > 0) { // Operation complete if processed count reaches total
        operation.status = failed > 0 ? 'completed_with_errors' : 'completed';
        operation.progress = 100; // Always 100% when complete
    } else {
        // Calculate progress based on processed items
        const processedCount = success + failed;
        operation.progress = total > 0 ? Math.round((processedCount / total) * 100) : 0;
        // Keep status as 'processing' if not fully completed
        if (operation.status !== 'failed' && operation.status !== 'completed' && operation.status !== 'completed_with_errors') {
            operation.status = 'processing';
        }
    }

    operation.endTime = Date.now();
    operation.duration = operation.endTime - operation.startTime;

    console.log(`Operation ${operationId} updated: Status=${operation.status}, Progress=${operation.progress}%`);
    // console.log('Updated operation details:', operation); // Log full details for debugging
}

// Expose updateOperationCompletionStatus globally or pass it where needed
// For now, we'll just keep it in this file and ensure /api/operation uses it.


// API to get operation status by ID
app.get('/operation/:operationId', (req, res) => {
    const { operationId } = req.params;
    const operation = tempUtils.getOperationData(operationId);

    if (!operation) {
        return res.status(404).json({ error: 'العملية غير موجودة' });
    }

    res.json({
        operation: {
            id: operationId,
            status: operation.status,
            progress: operation.progress,
            successCount: operation.successCount,
            failCount: operation.failCount,
            errors: operation.errors,
            startTime: operation.startTime,
            endTime: operation.endTime
        }
    });
});



// API لإنشاء ملف الإحصائيات الأولي
app.post('/api/initialize-statistics', (req, res) => {
    try {
        const { sessionId, recipients, messageTypes } = req.body;

        if (!sessionId || !recipients || !messageTypes) {
            return res.status(400).json({
                success: false,
                error: 'معاملات مطلوبة مفقودة: sessionId, recipients, messageTypes'
            });
        }

        const statisticsPath = tempUtils.initializeStatisticsFile(sessionId, recipients, messageTypes);

        if (statisticsPath) {
            res.json({
                success: true,
                message: 'تم إنشاء ملف الإحصائيات بنجاح',
                sessionId: sessionId,
                statisticsPath: statisticsPath
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'فشل في إنشاء ملف الإحصائيات'
            });
        }
    } catch (error) {
        console.error('خطأ في إنشاء ملف الإحصائيات:', error);
        res.status(500).json({
            success: false,
            error: 'فشل في إنشاء ملف الإحصائيات',
            details: error.message
        });
    }
});

// API لقراءة ملف الإحصائيات
app.get('/api/get-statistics/:sessionId', (req, res) => {
    try {
        const { sessionId } = req.params;

        if (!sessionId) {
            return res.status(400).json({
                success: false,
                error: 'معرف الجلسة مطلوب'
            });
        }

        const statisticsPath = path.join(__dirname, 'temp', `statistics_${sessionId}.json`);

        if (!fs.existsSync(statisticsPath)) {
            return res.status(404).json({
                success: false,
                error: 'ملف الإحصائيات غير موجود'
            });
        }

        const statisticsData = JSON.parse(fs.readFileSync(statisticsPath, 'utf8'));

        res.json({
            success: true,
            statistics: statisticsData
        });

    } catch (error) {
        console.error('خطأ في قراءة ملف الإحصائيات:', error);
        res.status(500).json({
            success: false,
            error: 'فشل في قراءة ملف الإحصائيات',
            details: error.message
        });
    }
});

// API لتنظيف ملفات العمليات
app.post('/api/cleanup-operations', (req, res) => {
    try {
        const tempDir = path.join(__dirname, 'temp');

        if (!fs.existsSync(tempDir)) {
            return res.json({
                success: true,
                deletedCount: 0,
                message: 'مجلد temp غير موجود'
            });
        }

        const operationFiles = fs.readdirSync(tempDir).filter(file => file.startsWith('op-') && file.endsWith('.json'));
        let deletedCount = 0;

        operationFiles.forEach(file => {
            try {
                const filePath = path.join(tempDir, file);
                fs.unlinkSync(filePath);
                deletedCount++;
            } catch (error) {
                console.error(`خطأ في حذف ملف العملية ${file}:`, error);
            }
        });

        res.json({
            success: true,
            deletedCount: deletedCount,
            message: `تم حذف ${deletedCount} ملف عملية`
        });

    } catch (error) {
        console.error('خطأ في تنظيف ملفات العمليات:', error);
        res.status(500).json({
            success: false,
            error: 'فشل في تنظيف ملفات العمليات',
            details: error.message
        });
    }
});

// API لتحديث ملف الإحصائيات
app.post('/api/update-statistics', (req, res) => {
    try {
        const { sessionId, recipientId, messageType, success, error, operationId } = req.body;

        if (!sessionId || !recipientId || !messageType || success === undefined) {
            return res.status(400).json({
                success: false,
                error: 'معاملات مطلوبة مفقودة: sessionId, recipientId, messageType, success'
            });
        }

        const isCompleted = tempUtils.updateStatisticsFile(sessionId, recipientId, messageType, success, error, operationId);

        res.json({
            success: true,
            message: 'تم تحديث الإحصائيات بنجاح',
            isCompleted: isCompleted
        });
    } catch (error) {
        console.error('خطأ في تحديث الإحصائيات:', error);
        res.status(500).json({
            success: false,
            error: 'فشل في تحديث الإحصائيات',
            details: error.message
        });
    }
});








