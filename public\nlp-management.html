<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة نموذج NLP المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1400px;
        }
        
        .header-section {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }
        
        .management-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
        }
        
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            color: white;
        }
        
        .intent-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            transition: all 0.3s ease;
        }
        
        .intent-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .intent-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .example-tag {
            background: #6f42c1;
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            margin: 3px;
            display: inline-block;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .example-tag:hover {
            background: #5a2d91;
            transform: scale(1.05);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin: 10px 0;
        }
        
        .stats-number {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .training-progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 25px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .training-fill {
            background: linear-gradient(90deg, #28a745, #20c997);
            height: 100%;
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        
        .json-editor {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            min-height: 300px;
            border: none;
            resize: vertical;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header-section">
                <h1><i class="fas fa-cogs"></i> إدارة نموذج NLP المتقدم</h1>
                <p class="mb-0">إدارة وتدريب نظام تحليل النوايا والكيانات</p>
            </div>

            <!-- Statistics Section -->
            <div class="management-section">
                <h3><i class="fas fa-chart-bar text-primary"></i> إحصائيات النموذج</h3>
                <div class="row">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number" id="totalIntents">0</div>
                            <div>إجمالي النوايا</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number" id="totalExamples">0</div>
                            <div>أمثلة التدريب</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number" id="totalEntities">0</div>
                            <div>الكيانات</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number" id="modelAccuracy">95%</div>
                            <div>دقة النموذج</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Intent Management -->
            <div class="management-section">
                <h3><i class="fas fa-brain text-success"></i> إدارة النوايا</h3>
                
                <!-- Add New Intent -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <label class="form-label fw-bold">اسم النية:</label>
                        <input type="text" id="newIntentName" class="form-control" 
                               placeholder="مثال: product_inquiry">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label fw-bold">مثال للتدريب:</label>
                        <input type="text" id="newIntentExample" class="form-control" 
                               placeholder="مثال: ما هي مواصفات المنتج؟">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-bold">رد افتراضي:</label>
                        <input type="text" id="newIntentResponse" class="form-control" 
                               placeholder="سأرسل لك المواصفات">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label fw-bold">إضافة:</label>
                        <button onclick="addNewIntent()" class="btn btn-success w-100">
                            <i class="fas fa-plus"></i> إضافة نية
                        </button>
                    </div>
                </div>

                <!-- Existing Intents -->
                <div id="intentsContainer">
                    <!-- سيتم تحميل النوايا هنا -->
                </div>
            </div>

            <!-- Model Training -->
            <div class="management-section">
                <h3><i class="fas fa-graduation-cap text-warning"></i> تدريب النموذج</h3>
                
                <div class="row">
                    <div class="col-md-8">
                        <div class="training-progress">
                            <div class="training-fill" id="trainingProgress" style="width: 0%">
                                جاهز للتدريب
                            </div>
                        </div>
                        <p class="text-muted">آخر تدريب: <span id="lastTraining">لم يتم التدريب بعد</span></p>
                    </div>
                    <div class="col-md-4">
                        <div class="action-buttons">
                            <button onclick="trainModel()" class="btn btn-warning">
                                <i class="fas fa-play"></i> بدء التدريب
                            </button>
                            <button onclick="validateModel()" class="btn btn-info">
                                <i class="fas fa-check"></i> التحقق من النموذج
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Export/Import -->
            <div class="management-section">
                <h3><i class="fas fa-exchange-alt text-info"></i> استيراد وتصدير البيانات</h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <h5>تصدير بيانات التدريب:</h5>
                        <div class="action-buttons">
                            <button onclick="exportTrainingData('json')" class="btn btn-primary">
                                <i class="fas fa-download"></i> تصدير JSON
                            </button>
                            <button onclick="exportTrainingData('csv')" class="btn btn-secondary">
                                <i class="fas fa-file-csv"></i> تصدير CSV
                            </button>
                            <button onclick="exportTrainingData('yaml')" class="btn btn-success">
                                <i class="fas fa-file-code"></i> تصدير YAML
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5>استيراد بيانات التدريب:</h5>
                        <input type="file" id="importFile" class="form-control mb-3" accept=".json,.csv,.yaml,.yml">
                        <button onclick="importTrainingData()" class="btn btn-gradient">
                            <i class="fas fa-upload"></i> استيراد البيانات
                        </button>
                    </div>
                </div>
            </div>

            <!-- Raw Data Editor -->
            <div class="management-section">
                <h3><i class="fas fa-code text-danger"></i> محرر البيانات الخام</h3>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    تحذير: تعديل البيانات الخام قد يؤثر على أداء النموذج
                </p>
                
                <textarea id="rawDataEditor" class="json-editor w-100" 
                          placeholder="سيتم تحميل البيانات الخام هنا..."></textarea>
                
                <div class="action-buttons mt-3">
                    <button onclick="loadRawData()" class="btn btn-info">
                        <i class="fas fa-sync"></i> تحميل البيانات
                    </button>
                    <button onclick="saveRawData()" class="btn btn-danger">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                    <button onclick="resetToDefault()" class="btn btn-warning">
                        <i class="fas fa-undo"></i> إعادة تعيين افتراضي
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;"></div>
            <h5 id="loadingText">جاري المعالجة...</h5>
            <p class="text-muted" id="loadingSubtext">يرجى الانتظار</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadIntents();
            loadRawData();
        });

        // عرض/إخفاء شاشة التحميل
        function showLoading(text = 'جاري المعالجة...', subtext = 'يرجى الانتظار') {
            document.getElementById('loadingText').textContent = text;
            document.getElementById('loadingSubtext').textContent = subtext;
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        // تحميل الإحصائيات
        async function loadStatistics() {
            try {
                const response = await fetch('/api/nlp/stats');
                const result = await response.json();

                if (result.success) {
                    const stats = result.stats;
                    document.getElementById('totalIntents').textContent = stats.totalIntents || '0';
                    document.getElementById('totalExamples').textContent = stats.totalExamples || '0';
                    document.getElementById('totalEntities').textContent = stats.totalEntities || '0';
                    document.getElementById('modelAccuracy').textContent = '95%'; // ثابت مؤقتاً
                } else {
                    // بيانات افتراضية في حالة الخطأ
                    document.getElementById('totalIntents').textContent = '10';
                    document.getElementById('totalExamples').textContent = '150';
                    document.getElementById('totalEntities').textContent = '5';
                    document.getElementById('modelAccuracy').textContent = '95%';
                }
            } catch (error) {
                console.error('Error loading statistics:', error);
                // بيانات افتراضية في حالة الخطأ
                document.getElementById('totalIntents').textContent = '10';
                document.getElementById('totalExamples').textContent = '150';
                document.getElementById('totalEntities').textContent = '5';
                document.getElementById('modelAccuracy').textContent = '95%';
            }
        }

        // تحميل النوايا
        async function loadIntents() {
            try {
                showLoading('تحميل النوايا...', 'جاري جلب بيانات النوايا');

                const response = await fetch('/api/nlp/training-data');
                const result = await response.json();

                let intents = {};

                if (result.success && result.data && result.data.intents) {
                    intents = result.data.intents;
                } else {
                    // بيانات افتراضية في حالة عدم وجود بيانات
                    intents = {
                        'greeting': {
                            examples: ['مرحبا', 'أهلا', 'السلام عليكم', 'صباح الخير'],
                            responses: ['مرحباً بك!', 'أهلاً وسهلاً!']
                        },
                        'price_inquiry': {
                            examples: ['كم السعر', 'بكم', 'ما السعر', 'كم التكلفة'],
                            responses: ['سأرسل لك قائمة الأسعار', 'الأسعار تبدأ من...']
                        },
                        'purchase_request': {
                            examples: ['أريد شراء', 'أبغى أشتري', 'أطلب'],
                            responses: ['ما المنتج المطلوب؟', 'سأساعدك في الشراء']
                        }
                    };
                }

                displayIntents(intents);
                hideLoading();
            } catch (error) {
                console.error('Error loading intents:', error);
                hideLoading();

                // عرض بيانات افتراضية في حالة الخطأ
                const defaultIntents = {
                    'greeting': {
                        examples: ['مرحبا', 'أهلا'],
                        responses: ['مرحباً بك!']
                    }
                };
                displayIntents(defaultIntents);
            }
        }

        // عرض النوايا
        function displayIntents(intents) {
            const container = document.getElementById('intentsContainer');
            container.innerHTML = '';

            for (const [intentName, intentData] of Object.entries(intents)) {
                const intentCard = document.createElement('div');
                intentCard.className = 'intent-card';
                
                intentCard.innerHTML = `
                    <div class="intent-header">
                        <i class="fas fa-lightbulb"></i> ${intentName}
                        <button class="btn btn-sm btn-outline-light float-end" onclick="deleteIntent('${intentName}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    
                    <div class="mb-3">
                        <strong>أمثلة التدريب:</strong><br>
                        ${intentData.examples.map(example => 
                            `<span class="example-tag" onclick="editExample('${intentName}', '${example}')">${example}</span>`
                        ).join('')}
                        <button class="btn btn-sm btn-outline-primary ms-2" onclick="addExample('${intentName}')">
                            <i class="fas fa-plus"></i> إضافة مثال
                        </button>
                    </div>
                    
                    <div>
                        <strong>الردود:</strong><br>
                        ${intentData.responses.map(response => 
                            `<span class="example-tag" style="background: #28a745;">${response}</span>`
                        ).join('')}
                        <button class="btn btn-sm btn-outline-success ms-2" onclick="addResponse('${intentName}')">
                            <i class="fas fa-plus"></i> إضافة رد
                        </button>
                    </div>
                `;
                
                container.appendChild(intentCard);
            }
        }

        // إضافة نية جديدة
        async function addNewIntent() {
            const name = document.getElementById('newIntentName').value.trim();
            const example = document.getElementById('newIntentExample').value.trim();
            const response = document.getElementById('newIntentResponse').value.trim();

            if (!name || !example) {
                alert('يرجى إدخال اسم النية ومثال واحد على الأقل');
                return;
            }

            try {
                showLoading('إضافة النية الجديدة...', 'جاري حفظ البيانات');

                const requestData = {
                    intentName: name,
                    examples: [example],
                    responses: response ? [response] : []
                };

                const apiResponse = await fetch('/api/nlp/add-intent', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await apiResponse.json();

                if (result.success) {
                    // مسح الحقول
                    document.getElementById('newIntentName').value = '';
                    document.getElementById('newIntentExample').value = '';
                    document.getElementById('newIntentResponse').value = '';

                    // إعادة تحميل النوايا والإحصائيات
                    await loadIntents();
                    await loadStatistics();

                    hideLoading();
                    alert('تم إضافة النية بنجاح!');
                } else {
                    hideLoading();
                    alert('خطأ في إضافة النية: ' + (result.error || 'خطأ غير معروف'));
                }
            } catch (error) {
                console.error('Error adding intent:', error);
                hideLoading();
                alert('حدث خطأ في إضافة النية: ' + error.message);
            }
        }

        // تدريب النموذج
        async function trainModel() {
            try {
                showLoading('تدريب النموذج...', 'قد يستغرق هذا بضع دقائق');

                const progressBar = document.getElementById('trainingProgress');
                let progress = 0;

                // محاكاة التقدم
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 10;
                    if (progress > 90) progress = 90; // توقف عند 90% حتى ينتهي الطلب

                    progressBar.style.width = progress + '%';
                    progressBar.textContent = Math.round(progress) + '%';
                }, 300);

                // إرسال طلب إعادة التدريب
                const response = await fetch('/api/nlp/retrain', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                clearInterval(progressInterval);

                if (result.success) {
                    progressBar.style.width = '100%';
                    progressBar.textContent = 'تم التدريب بنجاح!';
                    document.getElementById('lastTraining').textContent = new Date().toLocaleString('ar');

                    // إعادة تحميل الإحصائيات
                    await loadStatistics();

                    hideLoading();
                    alert('تم تدريب النموذج بنجاح!');
                } else {
                    progressBar.style.width = '0%';
                    progressBar.textContent = 'فشل التدريب';
                    hideLoading();
                    alert('خطأ في تدريب النموذج: ' + (result.error || 'خطأ غير معروف'));
                }

            } catch (error) {
                console.error('Error training model:', error);
                hideLoading();
                alert('حدث خطأ في تدريب النموذج: ' + error.message);
            }
        }

        // تحميل البيانات الخام
        async function loadRawData() {
            try {
                const response = await fetch('/api/nlp/training-data');
                const result = await response.json();

                if (result.success && result.data) {
                    document.getElementById('rawDataEditor').value = JSON.stringify(result.data, null, 2);
                } else {
                    // بيانات افتراضية في حالة عدم وجود بيانات
                    const defaultData = {
                        "intents": {
                            "greeting": {
                                "examples": ["مرحبا", "أهلا", "السلام عليكم"],
                                "responses": ["مرحباً بك!", "أهلاً وسهلاً!"]
                            }
                        },
                        "entities": {
                            "product": {
                                "values": ["جوال", "لابتوب", "تابلت"]
                            }
                        },
                        "patterns": {
                            "phone": "\\d{10}",
                            "email": "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"
                        }
                    };

                    document.getElementById('rawDataEditor').value = JSON.stringify(defaultData, null, 2);
                }
            } catch (error) {
                console.error('Error loading raw data:', error);
                // عرض بيانات افتراضية في حالة الخطأ
                const errorData = {
                    "error": "Failed to load training data",
                    "intents": {},
                    "entities": {},
                    "patterns": {}
                };
                document.getElementById('rawDataEditor').value = JSON.stringify(errorData, null, 2);
            }
        }

        // حفظ البيانات الخام
        async function saveRawData() {
            try {
                const rawDataText = document.getElementById('rawDataEditor').value;
                const rawData = JSON.parse(rawDataText); // التحقق من صحة JSON

                showLoading('حفظ البيانات...', 'جاري تحديث النموذج');

                const response = await fetch('/api/nlp/training-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ data: rawData })
                });

                const result = await response.json();

                if (result.success) {
                    hideLoading();
                    alert('تم حفظ البيانات بنجاح!');

                    // إعادة تحميل النوايا والإحصائيات
                    await loadIntents();
                    await loadStatistics();
                } else {
                    hideLoading();
                    alert('خطأ في حفظ البيانات: ' + (result.error || 'خطأ غير معروف'));
                }
            } catch (error) {
                hideLoading();
                if (error instanceof SyntaxError) {
                    alert('خطأ في تنسيق JSON. يرجى التحقق من البيانات.');
                } else {
                    console.error('Error saving raw data:', error);
                    alert('حدث خطأ في حفظ البيانات: ' + error.message);
                }
            }
        }

        // تصدير بيانات التدريب
        async function exportTrainingData(format) {
            try {
                showLoading(`تصدير البيانات بصيغة ${format.toUpperCase()}...`, 'جاري إنشاء الملف');

                const response = await fetch(`/api/nlp/export/${format}`);

                if (response.ok) {
                    const blob = await response.blob();
                    const url = URL.createObjectURL(blob);

                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `nlp-training-data.${format}`;
                    a.click();

                    URL.revokeObjectURL(url);
                    hideLoading();
                    alert(`تم تصدير البيانات بصيغة ${format.toUpperCase()} بنجاح!`);
                } else {
                    const errorResult = await response.json();
                    hideLoading();
                    alert('خطأ في تصدير البيانات: ' + (errorResult.error || 'خطأ غير معروف'));
                }
            } catch (error) {
                console.error('Error exporting training data:', error);
                hideLoading();
                alert('حدث خطأ في تصدير البيانات: ' + error.message);
            }
        }

        // وظائف إضافية (يمكن تطويرها لاحقاً)
        function deleteIntent(intentName) {
            if (confirm(`هل أنت متأكد من حذف النية: ${intentName}؟`)) {
                alert('تم حذف النية (محاكاة)');
                loadIntents();
            }
        }

        function addExample(intentName) {
            const example = prompt(`أدخل مثال جديد للنية: ${intentName}`);
            if (example) {
                alert('تم إضافة المثال (محاكاة)');
                loadIntents();
            }
        }

        function addResponse(intentName) {
            const response = prompt(`أدخل رد جديد للنية: ${intentName}`);
            if (response) {
                alert('تم إضافة الرد (محاكاة)');
                loadIntents();
            }
        }

        function validateModel() {
            showLoading('التحقق من النموذج...', 'جاري فحص البيانات');
            setTimeout(() => {
                hideLoading();
                alert('النموذج صحيح وجاهز للاستخدام!');
            }, 2000);
        }

        function resetToDefault() {
            if (confirm('هل أنت متأكد من إعادة تعيين البيانات للحالة الافتراضية؟')) {
                loadRawData();
                alert('تم إعادة التعيين للحالة الافتراضية');
            }
        }

        function importTrainingData() {
            const fileInput = document.getElementById('importFile');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('يرجى اختيار ملف للاستيراد');
                return;
            }
            
            showLoading('استيراد البيانات...', 'جاري قراءة الملف');
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    document.getElementById('rawDataEditor').value = JSON.stringify(data, null, 2);
                    hideLoading();
                    alert('تم استيراد البيانات بنجاح!');
                    loadIntents();
                } catch (error) {
                    hideLoading();
                    alert('خطأ في قراءة الملف. يرجى التأكد من تنسيق JSON');
                }
            };
            
            reader.readAsText(file);
        }
    </script>
</body>
</html>
