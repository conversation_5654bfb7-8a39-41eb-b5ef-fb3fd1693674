// اختبار شامل للنظام المدمج - Rasa NLP + Auto-Reply System
const { AdvancedIntentClassifier } = require('./rasa-like-nlp');
const { ModernAITextProcessor, ModernMessageAnalyzer, ModernAIMatcher } = require('./advanced-ai-system');

// إعداد النظام
const intentClassifier = new AdvancedIntentClassifier();
const aiProcessor = new ModernAITextProcessor();
const messageAnalyzer = new ModernMessageAnalyzer();
const aiMatcher = new ModernAIMatcher();

// بيانات اختبار شاملة
const testMessages = [
    // رسائل نصية عربية
    { message: "مرحبا كيف الحال", type: "text", expected: "greeting" },
    { message: "السلام عليكم ورحمة الله", type: "text", expected: "greeting" },
    { message: "كم سعر الجوال", type: "text", expected: "price_inquiry" },
    { message: "بكم اللابتوب", type: "text", expected: "price_inquiry" },
    { message: "أريد أشتري تابلت", type: "text", expected: "purchase_request" },
    { message: "أبغى أطلب منتج", type: "text", expected: "purchase_request" },
    { message: "شكراً لك", type: "text", expected: "thanks" },
    { message: "جزاك الله خير", type: "text", expected: "thanks" },
    
    // رسائل نصية إنجليزية
    { message: "Hello how are you", type: "text", expected: "greeting" },
    { message: "What is the price", type: "text", expected: "price_inquiry" },
    { message: "I want to buy", type: "text", expected: "purchase_request" },
    { message: "Thank you", type: "text", expected: "thanks" },
    
    // أرقام وإيموجي (يجب البحث في عمود CODE)
    { message: "123", type: "number", expected: "code_match" },
    { message: "456", type: "number", expected: "code_match" },
    { message: "😊", type: "emoji", expected: "code_match" },
    { message: "👍", type: "emoji", expected: "code_match" },
    
    // أرقام عربية (يجب تحويلها)
    { message: "١٢٣", type: "arabic_number", expected: "code_match" },
    { message: "٤٥٦", type: "arabic_number", expected: "code_match" },
    
    // رسائل مختلطة
    { message: "مرحبا ١٢٣", type: "mixed", expected: "text" },
    { message: "السعر ٥٠٠ ريال", type: "mixed", expected: "text" }
];

// بيانات تدريب وهمية للاختبار
const mockAutoReplyRules = [
    {
        code: "123",
        enabled: true,
        messages: {
            text: "مرحباً بك في خدمة العملاء",
            image: null,
            file: null,
            folder: null
        }
    },
    {
        code: "456",
        enabled: true,
        messages: {
            text: "شكراً لتواصلك معنا",
            image: null,
            file: null,
            folder: null
        }
    },
    {
        code: "😊",
        enabled: true,
        messages: {
            text: "سعداء بخدمتك",
            image: null,
            file: null,
            folder: null
        }
    },
    {
        code: "👍",
        enabled: true,
        messages: {
            text: "ممتاز! شكراً لك",
            image: null,
            file: null,
            folder: null
        }
    }
];

// دالة محاكاة processAdvancedAutoReply
async function simulateProcessAdvancedAutoReply(message, availableRules) {
    try {
        console.log(`\n🔍 معالجة الرسالة: "${message}"`);
        
        // تحديد نوع الرسالة (أرقام إنجليزية، أرقام عربية، أو إيموجي)
        const isNumberOrEmoji = /^[\d\u0660-\u0669\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F1E0}-\u{1F1FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]+$/u.test(message.trim());
        
        if (isNumberOrEmoji) {
            console.log("📊 نوع الرسالة: رقم أو إيموجي - البحث في عمود CODE");
            
            // تحويل الأرقام العربية إلى إنجليزية
            const convertedMessage = aiProcessor.convertArabicNumbers(message);
            console.log(`🔄 الرسالة بعد التحويل: "${convertedMessage}"`);
            
            // البحث عن تطابق 100% في عمود CODE
            for (const rule of availableRules) {
                if (rule.code === convertedMessage && rule.enabled) {
                    console.log(`✅ تطابق 100% في عمود CODE: "${rule.code}"`);
                    return {
                        success: true,
                        type: "code_match",
                        rule: rule,
                        score: 1.0,
                        message: convertedMessage
                    };
                }
            }
            
            console.log("❌ لا يوجد تطابق في عمود CODE");
            return { success: false, type: "code_no_match", message: convertedMessage };
            
        } else {
            console.log("📝 نوع الرسالة: نص - استخدام نظام NLP");
            
            // تحليل الرسالة باستخدام NLP
            const analysisResult = await intentClassifier.analyzeMessage(message);
            console.log(`🧠 تحليل NLP:`, {
                intent: analysisResult.intent,
                confidence: analysisResult.confidence,
                entities: analysisResult.entities.length
            });
            
            // البحث عن أفضل رد باستخدام NLP
            const nlpMatch = intentClassifier.findBestResponse(analysisResult, availableRules);
            
            if (nlpMatch && nlpMatch.score > 0) {
                console.log(`✅ تطابق NLP بنسبة: ${(nlpMatch.score * 100).toFixed(2)}%`);
                return {
                    success: true,
                    type: "nlp_match",
                    rule: nlpMatch.rule,
                    score: nlpMatch.score,
                    analysis: analysisResult,
                    message: message
                };
            }
            
            console.log("🔄 لا يوجد تطابق NLP مناسب - استخدام النظام التقليدي");
            
            // النظام التقليدي كبديل
            const processedMessage = aiProcessor.processText(message);
            const analysis = messageAnalyzer.analyzeMessage(processedMessage);
            const traditionalMatch = aiMatcher.findBestMatch(analysis, availableRules);
            
            if (traditionalMatch && traditionalMatch.similarity > 0) {
                console.log(`✅ تطابق تقليدي بنسبة: ${(traditionalMatch.similarity * 100).toFixed(2)}%`);
                return {
                    success: true,
                    type: "traditional_match",
                    rule: traditionalMatch.rule,
                    score: traditionalMatch.similarity,
                    analysis: analysis,
                    message: message
                };
            }
            
            console.log("❌ لا يوجد تطابق في أي نظام");
            return { success: false, type: "no_match", message: message };
        }
        
    } catch (error) {
        console.error('❌ خطأ في معالجة الرسالة:', error);
        return { success: false, type: "error", error: error.message, message: message };
    }
}

// دالة الاختبار الرئيسية
async function runComprehensiveTest() {
    console.log("🚀 بدء الاختبار الشامل للنظام المدمج");
    console.log("=" .repeat(60));
    
    const results = {
        total: testMessages.length,
        passed: 0,
        failed: 0,
        details: []
    };
    
    for (let i = 0; i < testMessages.length; i++) {
        const testCase = testMessages[i];
        console.log(`\n📋 اختبار ${i + 1}/${testMessages.length}`);
        
        const result = await simulateProcessAdvancedAutoReply(testCase.message, mockAutoReplyRules);
        
        const testResult = {
            message: testCase.message,
            type: testCase.type,
            expected: testCase.expected,
            actual: result.type,
            success: result.success,
            score: result.score || 0,
            passed: false
        };
        
        // تحديد نجاح الاختبار
        if (testCase.expected === "code_match" && result.type === "code_match") {
            testResult.passed = true;
        } else if (testCase.expected !== "code_match" && result.success && result.type !== "code_match") {
            testResult.passed = true;
        }
        
        if (testResult.passed) {
            results.passed++;
            console.log(`✅ نجح الاختبار`);
        } else {
            results.failed++;
            console.log(`❌ فشل الاختبار - متوقع: ${testCase.expected}, فعلي: ${result.type}`);
        }
        
        results.details.push(testResult);
    }
    
    // عرض النتائج النهائية
    console.log("\n" + "=" .repeat(60));
    console.log("📊 نتائج الاختبار الشامل");
    console.log("=" .repeat(60));
    console.log(`إجمالي الاختبارات: ${results.total}`);
    console.log(`نجح: ${results.passed} (${((results.passed / results.total) * 100).toFixed(1)}%)`);
    console.log(`فشل: ${results.failed} (${((results.failed / results.total) * 100).toFixed(1)}%)`);
    
    // عرض تفاصيل الاختبارات الفاشلة
    const failedTests = results.details.filter(test => !test.passed);
    if (failedTests.length > 0) {
        console.log("\n❌ الاختبارات الفاشلة:");
        failedTests.forEach((test, index) => {
            console.log(`${index + 1}. "${test.message}" - متوقع: ${test.expected}, فعلي: ${test.actual}`);
        });
    }
    
    return results;
}

// تشغيل الاختبار
if (require.main === module) {
    runComprehensiveTest()
        .then(results => {
            console.log("\n🎉 انتهى الاختبار الشامل");
            process.exit(results.failed === 0 ? 0 : 1);
        })
        .catch(error => {
            console.error("💥 خطأ في تشغيل الاختبار:", error);
            process.exit(1);
        });
}

module.exports = {
    runComprehensiveTest,
    simulateProcessAdvancedAutoReply,
    testMessages,
    mockAutoReplyRules
};
