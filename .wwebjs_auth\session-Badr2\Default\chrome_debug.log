[0626/213334.526:INFO:CONSOLE(163)] "Event handler of 'x-storagemutated-1' event must be added on the initial evaluation of worker script.", source: https://static.whatsapp.net/rsrc.php/v4/y4/r/Ghn4SaCyUkO.js (163)
[0626/213334.576:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[0626/213334.576:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[0626/213334.577:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[0626/213334.577:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[0626/213334.577:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[0626/213334.577:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[0626/213334.577:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[0626/213334.577:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[0626/213334.577:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'unload'.", source:  (0)
[0626/213334.577:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[0626/213334.577:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://web.whatsapp.com/ (0)
[0626/213344.974:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0626/213344.978:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0626/213345.645:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0626/213345.823:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0626/213345.824:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0626/213345.825:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0626/213345.826:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0626/213345.827:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0626/213345.828:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0626/213345.829:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0626/213405.243:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0626/213608.207:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
