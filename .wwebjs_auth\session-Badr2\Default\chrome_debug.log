[0627/233457.038:INFO:CONSOLE(163)] "Event handler of 'x-storagemutated-1' event must be added on the initial evaluation of worker script.", source: https://static.whatsapp.net/rsrc.php/v4/yK/r/3n1WXGy7Khu.js (163)
[0627/233457.044:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[0627/233457.044:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[0627/233457.044:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[0627/233457.044:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[0627/233457.045:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[0627/233457.045:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[0627/233457.045:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[0627/233457.045:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[0627/233457.045:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'unload'.", source:  (0)
[0627/233457.045:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[0627/233457.045:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://web.whatsapp.com/ (0)
[0627/233459.527:INFO:CONSOLE(163)] "Event handler of 'x-storagemutated-1' event must be added on the initial evaluation of worker script.", source: https://static.whatsapp.net/rsrc.php/v4/yQ/r/XjJbdBZVNWX.js (163)
[0627/233559.341:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0627/233600.730:INFO:CONSOLE(163)] "Event handler of 'x-storagemutated-1' event must be added on the initial evaluation of worker script.", source: https://static.whatsapp.net/rsrc.php/v4/yQ/r/XjJbdBZVNWX.js (163)
[0627/233609.815:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0627/233731.465:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0627/233748.031:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0627/233801.339:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0627/233802.322:INFO:CONSOLE(163)] "Event handler of 'x-storagemutated-1' event must be added on the initial evaluation of worker script.", source: https://static.whatsapp.net/rsrc.php/v4/yQ/r/XjJbdBZVNWX.js (163)
[0627/233814.224:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0627/233832.536:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0627/233850.189:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0627/233953.357:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0627/234024.702:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
