document.addEventListener('DOMContentLoaded', function() {
    // العناصر الأساسية
    const backBtn = document.getElementById('backBtn');
    const advancedModeBtn = document.getElementById('advancedModeBtn');
    const globalAutoReplyToggle = document.getElementById('globalAutoReplyToggle');
    const statusMessage = document.getElementById('statusMessage');
    const saveBtn = document.getElementById('saveBtn');
    const testBtn = document.getElementById('testBtn');

    // عناصر النص
    const messageText = document.getElementById('messageText');
    const insertNameBtn = document.getElementById('insertNameBtn');
    const emojiBtn = document.getElementById('emojiBtn');
    const emojiPicker = document.getElementById('emojiPicker');

    // عناصر الملفات
    const imageInput = document.getElementById('imageInput');
    const fileInput = document.getElementById('fileInput');
    const folderInput = document.getElementById('folderInput');

    // حاويات المعاينة
    const imagePreviewContainer = document.getElementById('imagePreviewContainer');
    const filePreviewContainer = document.getElementById('filePreviewContainer');
    const folderPreviewContainer = document.getElementById('folderPreviewContainer');
    const fullPreview = document.getElementById('fullPreview');

    // متغيرات الحالة
    let selectedFiles = {
        image: null,
        file: null,
        folder: []
    };

    let emojiPickerElement = null;

    // تحميل الإعدادات الحالية
    loadCurrentSettings();

    // مستمعي الأحداث الأساسية
    backBtn.addEventListener('click', () => {
        window.location.href = 'dashboard.html';
    });

    advancedModeBtn.addEventListener('click', () => {
        window.location.href = 'auto-reply-advanced.html?mode=global';
    });

    globalAutoReplyToggle.addEventListener('change', function() {
        updateToggleStatus();
        if (this.checked) {
            saveCurrentSettings();
        }
    });

    saveBtn.addEventListener('click', saveSettings);
    testBtn.addEventListener('click', testMessage);

    // مستمعي أحداث النص
    messageText.addEventListener('input', updatePreview);
    insertNameBtn.addEventListener('click', insertName);
    emojiBtn.addEventListener('click', toggleEmojiPicker);

    // مستمعي أحداث الملفات
    imageInput.addEventListener('change', handleImageSelect);
    fileInput.addEventListener('change', handleFileSelect);
    folderInput.addEventListener('change', handleFolderSelect);

    // وظائف التبديل
    function toggleSection(type) {
        const section = document.getElementById(`${type}Section`);
        const content = document.getElementById(`${type}Content`);
        const enableCheckbox = document.getElementById(`enable${type.charAt(0).toUpperCase() + type.slice(1)}`);
        
        if (enableCheckbox.checked) {
            section.classList.add('active');
            content.classList.add('active');
        } else {
            section.classList.remove('active');
            content.classList.remove('active');
            // مسح البيانات عند التعطيل
            if (type === 'text') {
                messageText.value = '';
            } else if (type === 'image') {
                selectedFiles.image = null;
                imagePreviewContainer.innerHTML = '';
                imageInput.value = '';
            } else if (type === 'file') {
                selectedFiles.file = null;
                filePreviewContainer.innerHTML = '';
                fileInput.value = '';
            } else if (type === 'folder') {
                selectedFiles.folder = [];
                folderPreviewContainer.innerHTML = '';
                folderInput.value = '';
            }
        }
        updatePreview();
    }

    // تحديث حالة التبديل
    function updateToggleStatus() {
        if (globalAutoReplyToggle.checked) {
            statusMessage.className = 'alert alert-success';
            statusMessage.innerHTML = '<i class="fas fa-check-circle me-2"></i>الرد التلقائي العام مفعل';
        } else {
            statusMessage.className = 'alert alert-info';
            statusMessage.innerHTML = '<i class="fas fa-info-circle me-2"></i>الرد التلقائي العام غير مفعل حالياً';
        }
    }

    // إدراج الاسم
    function insertName() {
        const cursorPos = messageText.selectionStart;
        const textBefore = messageText.value.substring(0, cursorPos);
        const textAfter = messageText.value.substring(cursorPos);
        messageText.value = textBefore + '{name}' + textAfter;
        messageText.focus();
        messageText.setSelectionRange(cursorPos + 6, cursorPos + 6);
        updatePreview();
    }

    // تبديل منتقي الرموز التعبيرية
    function toggleEmojiPicker() {
        if (!emojiPickerElement) {
            emojiPickerElement = document.createElement('emoji-picker');
            emojiPickerElement.addEventListener('emoji-click', event => {
                const cursorPos = messageText.selectionStart;
                const textBefore = messageText.value.substring(0, cursorPos);
                const textAfter = messageText.value.substring(cursorPos);
                messageText.value = textBefore + event.detail.unicode + textAfter;
                messageText.focus();
                messageText.setSelectionRange(cursorPos + event.detail.unicode.length, cursorPos + event.detail.unicode.length);
                updatePreview();
                emojiPicker.style.display = 'none';
            });
            emojiPicker.appendChild(emojiPickerElement);
        }
        
        emojiPicker.style.display = emojiPicker.style.display === 'block' ? 'none' : 'block';
    }

    // معالجة اختيار الصورة
    function handleImageSelect(event) {
        const file = event.target.files[0];
        if (file) {
            selectedFiles.image = file;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreviewContainer.innerHTML = `
                    <div class="mt-3">
                        <img src="${e.target.result}" class="image-preview" alt="معاينة الصورة">
                        <div class="mt-2">
                            <small class="text-muted">${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)</small>
                            <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="removeImage()">
                                <i class="fas fa-times"></i> إزالة
                            </button>
                        </div>
                    </div>
                `;
            };
            reader.readAsDataURL(file);
        }
        updatePreview();
    }

    // معالجة اختيار الملف
    function handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            selectedFiles.file = file;
            
            filePreviewContainer.innerHTML = `
                <div class="file-preview mt-3">
                    <i class="fas fa-file"></i>
                    <div class="flex-grow-1">
                        <div class="fw-bold">${file.name}</div>
                        <small class="text-muted">${(file.size / 1024 / 1024).toFixed(2)} MB</small>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        }
        updatePreview();
    }

    // معالجة اختيار ملفات متعددة
    function handleFolderSelect(event) {
        const files = Array.from(event.target.files);
        selectedFiles.folder = files;
        
        folderPreviewContainer.innerHTML = '';
        files.forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <i class="fas fa-times remove-file" onclick="removeFolderFile(${index})"></i>
                <i class="fas fa-file me-2"></i>
                <span>${file.name}</span>
                <small class="text-muted ms-2">(${(file.size / 1024 / 1024).toFixed(2)} MB)</small>
            `;
            folderPreviewContainer.appendChild(fileItem);
        });
        
        updatePreview();
    }

    // وظائف الإزالة
    window.removeImage = function() {
        selectedFiles.image = null;
        imagePreviewContainer.innerHTML = '';
        imageInput.value = '';
        updatePreview();
    };

    window.removeFile = function() {
        selectedFiles.file = null;
        filePreviewContainer.innerHTML = '';
        fileInput.value = '';
        updatePreview();
    };

    window.removeFolderFile = function(index) {
        selectedFiles.folder.splice(index, 1);
        handleFolderSelect({ target: { files: selectedFiles.folder } });
    };

    // تحديث المعاينة
    function updatePreview() {
        let previewHTML = '';
        let hasContent = false;

        // معاينة النص
        const enableText = document.getElementById('enableText').checked;
        if (enableText && messageText.value.trim()) {
            hasContent = true;
            const previewText = messageText.value.replace(/{name}/g, 'أحمد');
            previewHTML += `
                <div class="mb-3">
                    <strong><i class="fas fa-comment-alt me-2"></i>النص:</strong>
                    <div class="mt-2 p-2 bg-light rounded">${previewText}</div>
                </div>
            `;
        }

        // معاينة الصورة
        const enableImage = document.getElementById('enableImage').checked;
        if (enableImage && selectedFiles.image) {
            hasContent = true;
            previewHTML += `
                <div class="mb-3">
                    <strong><i class="fas fa-image me-2"></i>الصورة:</strong>
                    <div class="mt-2">${selectedFiles.image.name}</div>
                </div>
            `;
        }

        // معاينة الملف
        const enableFile = document.getElementById('enableFile').checked;
        if (enableFile && selectedFiles.file) {
            hasContent = true;
            previewHTML += `
                <div class="mb-3">
                    <strong><i class="fas fa-file me-2"></i>الملف:</strong>
                    <div class="mt-2">${selectedFiles.file.name}</div>
                </div>
            `;
        }

        // معاينة الملفات المتعددة
        const enableFolder = document.getElementById('enableFolder').checked;
        if (enableFolder && selectedFiles.folder.length > 0) {
            hasContent = true;
            previewHTML += `
                <div class="mb-3">
                    <strong><i class="fas fa-folder me-2"></i>الملفات المتعددة:</strong>
                    <div class="mt-2">
                        ${selectedFiles.folder.map(file => `<div>• ${file.name}</div>`).join('')}
                    </div>
                </div>
            `;
        }

        if (!hasContent) {
            previewHTML = `
                <div class="text-muted text-center py-4">
                    <i class="fas fa-comment-dots mb-2" style="font-size: 2rem;"></i>
                    <p>ستظهر معاينة الرسالة الكاملة هنا</p>
                </div>
            `;
        }

        fullPreview.innerHTML = previewHTML;
    }

    // تحميل الإعدادات الحالية
    function loadCurrentSettings() {
        fetch('/api/auto-reply/global/load')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.settings) {
                    const settings = data.settings;
                    
                    // تحديث حالة التبديل
                    globalAutoReplyToggle.checked = settings.enabled || false;
                    updateToggleStatus();
                    
                    if (settings.message) {
                        // تحميل النص
                        if (settings.message.text) {
                            document.getElementById('enableText').checked = true;
                            toggleSection('text');
                            messageText.value = settings.message.text;
                        }
                        
                        // تحميل معلومات الصورة
                        if (settings.message.imagePath) {
                            document.getElementById('enableImage').checked = true;
                            toggleSection('image');
                            displayExistingImage(settings.message.imagePath);
                        }
                        
                        // تحميل معلومات الملف
                        if (settings.message.filePath) {
                            document.getElementById('enableFile').checked = true;
                            toggleSection('file');
                            displayExistingFile(settings.message.filePath);
                        }
                        
                        // تحميل معلومات الملفات المتعددة
                        if (settings.message.folderFiles && settings.message.folderFiles.length > 0) {
                            document.getElementById('enableFolder').checked = true;
                            toggleSection('folder');
                            displayExistingFolderFiles(settings.message.folderFiles);
                        }
                    }
                    
                    updatePreview();
                }
            })
            .catch(error => {
                console.error('Error loading settings:', error);
            });
    }

    // عرض الصورة الموجودة
    function displayExistingImage(imagePath) {
        const fileName = imagePath.split('\\').pop().split('/').pop();
        imagePreviewContainer.innerHTML = `
            <div class="mt-3">
                <img src="/auto-reply-files/${fileName}" class="image-preview" alt="معاينة الصورة" 
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <div style="display: none;" class="mt-2 p-3 bg-light rounded text-center">
                    <i class="fas fa-image fa-2x text-muted mb-2"></i>
                    <div>صورة محفوظة</div>
                    <small class="text-muted">${fileName}</small>
                </div>
                <div class="mt-2">
                    <small class="text-muted">${fileName}</small>
                    <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="removeImage()">
                        <i class="fas fa-times"></i> إزالة
                    </button>
                </div>
            </div>
        `;
    }

    // عرض الملف الموجود
    function displayExistingFile(filePath) {
        const fileName = filePath.split('\\').pop().split('/').pop();
        filePreviewContainer.innerHTML = `
            <div class="file-preview mt-3">
                <i class="fas fa-file"></i>
                <div class="flex-grow-1">
                    <div class="fw-bold">${fileName}</div>
                    <small class="text-muted">ملف محفوظ</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    }

    // عرض الملفات المتعددة الموجودة
    function displayExistingFolderFiles(folderFiles) {
        folderPreviewContainer.innerHTML = '';
        folderFiles.forEach((filePath, index) => {
            const fileName = filePath.split('\\').pop().split('/').pop();
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <i class="fas fa-times remove-file" onclick="removeFolderFile(${index})"></i>
                <i class="fas fa-file me-2"></i>
                <span>${fileName}</span>
                <small class="text-muted ms-2">(ملف محفوظ)</small>
            `;
            folderPreviewContainer.appendChild(fileItem);
        });
    }

    // حفظ الإعدادات
    function saveSettings() {
        const formData = new FormData();
        
        const settings = {
            enabled: globalAutoReplyToggle.checked,
            message: {}
        };

        // إضافة النص
        if (document.getElementById('enableText').checked && messageText.value.trim()) {
            settings.message.text = messageText.value.trim();
        }

        formData.append('settings', JSON.stringify(settings));

        // إضافة الملفات
        if (document.getElementById('enableImage').checked && selectedFiles.image) {
            formData.append('image', selectedFiles.image);
        }

        if (document.getElementById('enableFile').checked && selectedFiles.file) {
            formData.append('file', selectedFiles.file);
        }

        if (document.getElementById('enableFolder').checked && selectedFiles.folder.length > 0) {
            selectedFiles.folder.forEach((file, index) => {
                formData.append(`folder_${index}`, file);
            });
        }

        // إرسال البيانات
        fetch('/api/auto-reply/global/save', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    title: 'تم الحفظ بنجاح',
                    text: 'تم حفظ إعدادات الرد التلقائي العام',
                    icon: 'success',
                    confirmButtonText: 'موافق'
                }).then(() => {
                    // إعادة تحميل الإعدادات لعرض الملفات المحفوظة
                    loadCurrentSettings();
                });
            } else {
                Swal.fire({
                    title: 'خطأ',
                    text: data.error || 'حدث خطأ في حفظ الإعدادات',
                    icon: 'error'
                });
            }
        })
        .catch(error => {
            console.error('Error saving settings:', error);
            Swal.fire({
                title: 'خطأ',
                text: 'حدث خطأ في حفظ الإعدادات',
                icon: 'error'
            });
        });
    }

    // اختبار الرسالة
    function testMessage() {
        const enableText = document.getElementById('enableText').checked;
        const enableImage = document.getElementById('enableImage').checked;
        const enableFile = document.getElementById('enableFile').checked;
        const enableFolder = document.getElementById('enableFolder').checked;

        if (!enableText && !enableImage && !enableFile && !enableFolder) {
            Swal.fire({
                title: 'تنبيه',
                text: 'يرجى تفعيل نوع واحد على الأقل من الرسائل للاختبار',
                icon: 'warning'
            });
            return;
        }

        let testContent = '<div class="text-start">';
        
        if (enableText && messageText.value.trim()) {
            testContent += `<p><strong>النص:</strong> ${messageText.value.replace(/{name}/g, 'أحمد')}</p>`;
        }
        
        if (enableImage && selectedFiles.image) {
            testContent += `<p><strong>الصورة:</strong> ${selectedFiles.image.name}</p>`;
        }
        
        if (enableFile && selectedFiles.file) {
            testContent += `<p><strong>الملف:</strong> ${selectedFiles.file.name}</p>`;
        }
        
        if (enableFolder && selectedFiles.folder.length > 0) {
            testContent += `<p><strong>الملفات المتعددة:</strong> ${selectedFiles.folder.length} ملف</p>`;
        }
        
        testContent += '<p class="text-muted">هذه معاينة لكيفية ظهور الرسالة للمستلمين</p></div>';

        Swal.fire({
            title: 'اختبار الرسالة',
            html: testContent,
            icon: 'info',
            confirmButtonText: 'موافق'
        });
    }

    // حذف جميع الإعدادات
    window.deleteAllSettings = function() {
        Swal.fire({
            title: 'تأكيد الحذف',
            text: 'هل أنت متأكد من حذف جميع إعدادات الرد التلقائي العام؟',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، احذف',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch('/api/auto-reply/global/delete', {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            title: 'تم الحذف',
                            text: 'تم حذف جميع إعدادات الرد التلقائي العام',
                            icon: 'success'
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire({
                            title: 'خطأ',
                            text: 'حدث خطأ في حذف الإعدادات',
                            icon: 'error'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error deleting settings:', error);
                    Swal.fire({
                        title: 'خطأ',
                        text: 'حدث خطأ في حذف الإعدادات',
                        icon: 'error'
                    });
                });
            }
        });
    };

    // جعل وظائف التبديل متاحة عالمياً
    window.toggleSection = toggleSection;
});
