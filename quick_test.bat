@echo off
chcp 65001 >nul
title WhatsApp Node - اختبار سريع للمتصفح المحسن

echo.
echo ========================================
echo   🚀 WhatsApp Node - اختبار سريع
echo   📅 الإصدار 2.0 المحسن
echo ========================================
echo.

echo 🔍 فحص المتطلبات...

:: فحص Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo 💡 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)
echo ✅ Python متوفر

:: فحص Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت أو غير موجود في PATH
    echo 💡 يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

:: فحص PyQt5
python -c "import PyQt5" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PyQt5 غير مثبت
    echo 🔄 جاري تثبيت PyQt5...
    pip install PyQt5 PyQtWebEngine
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت PyQt5
        pause
        exit /b 1
    )
)
echo ✅ PyQt5 متوفر

echo.
echo 🧪 تشغيل اختبار التحسينات...
echo.

:: تشغيل ملف الاختبار
python test_browser.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في تشغيل الاختبار
    echo 💡 تحقق من رسائل الخطأ أعلاه
    pause
    exit /b 1
)

echo.
echo ✅ تم الانتهاء من الاختبار
pause
