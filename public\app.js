document.addEventListener('DOMContentLoaded', function() {
    // تعريف المتغيرات العامة
    const createAccountBtn = document.getElementById('createAccountBtn');
    const accountsContainer = document.getElementById('accountsContainer');
    const noAccountsMessage = document.getElementById('noAccountsMessage');
    const connectBtn = document.getElementById('connectBtn');
    const accountForm = document.getElementById('accountForm');

    // تعريف النوافذ المنبثقة
    const createAccountModal = new bootstrap.Modal(document.getElementById('createAccountModal'));
    const qrModal = new bootstrap.Modal(document.getElementById('qrModal'));
    const pairingModal = new bootstrap.Modal(document.getElementById('pairingModal'));

    // متغيرات لتخزين البيانات الحالية
    let currentAccountName = '';
    let qrCheckInterval = null;
    let infoCheckInterval = null;
    let accountsUpdateInterval = null;
    let connectionSuccessMessageShown = false;
    let currentAutoReplyMode = 'global'; // متغير لتتبع وضع الرد التلقائي الحالي

    // تحميل الحسابات عند بدء التطبيق
    // Ensure app functionality is dependent on successful login
    if (localStorage.getItem('loggedIn') === 'true') {
        // تحميل إعدادات وضع الرد التلقائي أولاً، ثم تحميل الحسابات
        loadAutoReplyModeSettings().then(() => {
            // تحميل الحسابات بعد تحديث الوضع
            loadAccounts();
            // بدء التحديث الدوري لحالة الحسابات
            startAccountsStatusUpdate();
            // تحديث حالة زر Excel عند بدء التطبيق
            updateExcelSendButtonState();
        });
    }

    // إضافة مستمعي الأحداث
    createAccountBtn.addEventListener('click', () => {
        resetForm();
        createAccountModal.show();
    });

    // إضافة مستمع الحدث لزر إرسال من Excel
    document.getElementById('excelSendBtn').addEventListener('click', (e) => {
        const excelSendBtn = e.target;
        
        // التحقق من حالة تفعيل الزر
        if (excelSendBtn.disabled) {
            e.preventDefault();
            showAlert('يجب أن يكون هناك حساب واحد متصل على الأقل لاستخدام هذه الميزة', 'warning');
            return;
        }
        
        window.location.href = '/excel-send.html';
    });

    // وظيفة بدء التحديث الدوري لحالة الحسابات
    function startAccountsStatusUpdate() {
        // إيقاف أي فاصل زمني سابق
        if (accountsUpdateInterval) clearInterval(accountsUpdateInterval);

        // تحديث حالة الحسابات فورًا
        updateAccountsStatus();

        // تحديث حالة الحسابات كل 5 ثوانٍ
        accountsUpdateInterval = setInterval(() => {
            // التحقق من وجود حساب حالي في طور الاتصال
            if (currentAccountName) {
                // التحقق من حالة الاتصال للحساب الحالي
                fetch(`/api/connection-status/${currentAccountName}`)
                    .then(response => response.json())
                    .then(data => {
                        // إذا كان الحساب متصلاً، قم بتحديث قائمة الحسابات
                        if (data.connected) {
                            loadAccounts();
                        } else {
                            // استخدام وظيفة تحديث الحسابات بدلاً من إعادة تحميلها بالكامل
                            updateAccountsStatus();
                        }
                    })
                    .catch(error => {
                        console.error('Error checking account status:', error);
                        // في حالة الخطأ، استخدم وظيفة تحديث الحسابات
                        updateAccountsStatus();
                    });
            } else {
                // استخدام وظيفة تحديث الحسابات بدلاً من إعادة تحميلها بالكامل
                updateAccountsStatus();
            }
        }, 5000); // كل 5 ثوانٍ
    }

    // وظيفة تحديث حالة الحسابات بدون إعادة رسم البطاقات بالكامل
    function updateAccountsStatus() {
        fetch('/api/available-accounts')
            .then(response => response.json())
            .then(data => {
                if (data.accounts && data.accounts.length > 0) {
                    // تحديث كل حساب موجود في الصفحة
                    data.accounts.forEach(account => {
                        // البحث عن بطاقة الحساب في الصفحة
                        const accountCard = document.querySelector(`[data-account-id="${account.accountName}"]`);

                        // إذا لم تكن البطاقة موجودة، تخطي هذا الحساب
                        if (!accountCard) return;

                        // الحصول على عناصر البطاقة
                        const accountCardDiv = accountCard.querySelector('.account-card');
                        const accountHeader = accountCard.querySelector('.account-header');
                        const statusBadge = accountCard.querySelector('.status-badge');

                        // تحديث حالة الاتصال
                        if (statusBadge) {
                            let statusIcon = '';
                            let statusText = '';
                            let statusClass = '';
                            let headerClass = '';
                            let cardClass = '';

                            if (account.connected && account.status === 'connected') {
                                statusIcon = '<i class="fas fa-circle text-success me-2"></i>';
                                statusText = 'متصل';
                                statusClass = 'connected';
                                headerClass = 'bg-success';
                                cardClass = 'connected';
                            } else if (account.status === 'connecting' || account.status === 'qr_ready' || account.status === 'pairing_code_ready' || account.status === 'reconnecting') {
                                statusIcon = '<i class="fas fa-circle text-warning me-2"></i>';
                                statusText = account.status === 'qr_ready' ? 'بانتظار مسح QR' :
                                             account.status === 'pairing_code_ready' ? 'بانتظار إدخال رمز الاقتران' :
                                             account.status === 'reconnecting' ? 'جاري إعادة الاتصال...' : 'جاري الاتصال...';
                                statusClass = 'connecting';
                                headerClass = 'bg-warning text-dark';
                                cardClass = 'connecting';
                            } else {
                                statusIcon = '<i class="fas fa-circle text-danger me-2"></i>';
                                statusText = 'غير متصل';
                                statusClass = 'disconnected';
                                headerClass = 'bg-secondary';
                                cardClass = 'disconnected';
                            }

                            // تحديث نص وفئة الحالة
                            statusBadge.innerHTML = `${statusIcon}${statusText}`;
                            statusBadge.className = `status-badge ${statusClass}`;

                            // تحديث فئة البطاقة والعنوان
                            if (accountCardDiv) {
                                accountCardDiv.className = `account-card ${cardClass}`;
                            }

                            if (accountHeader) {
                                // حفظ النص الحالي للعنوان
                                const headerText = accountHeader.innerHTML;
                                // إزالة جميع الفئات وإضافة الفئة الجديدة
                                accountHeader.className = `account-header ${headerClass}`;
                                // إعادة تعيين النص
                                accountHeader.innerHTML = headerText;
                            }
                        }

                        // تحديث صورة الملف الشخصي إذا تغيرت
                        if (account.info && account.info.profilePictureUrl) {
                            const profileImage = accountCard.querySelector('.profile-image');
                            if (profileImage && profileImage.src !== account.info.profilePictureUrl) {
                                profileImage.src = account.info.profilePictureUrl;
                            }
                        }

                        // تحديث معلومات الحساب إذا كانت متوفرة
                        if (account.info) {
                            const accountInfo = accountCard.querySelector('.account-info');
                            if (accountInfo) {
                                // تحديث الاسم
                                const nameElement = accountInfo.querySelector('p:first-child');
                                if (nameElement) {
                                    nameElement.innerHTML = `<strong>الاسم:</strong> ${account.info.name || 'غير معروف'}`;
                                }

                                // تحديث الاسم الموثق إذا كان متوفرًا
                                const verifiedNameElement = accountInfo.querySelector('p:nth-child(2)');
                                if (verifiedNameElement && account.info.verifiedName) {
                                    verifiedNameElement.innerHTML = `<strong>الاسم الموثق:</strong> ${account.info.verifiedName}`;
                                }

                                // تحديث رقم الهاتف
                                const phoneElement = accountInfo.querySelector('p:nth-child(3)');
                                if (phoneElement && account.phoneNumber) {
                                    phoneElement.innerHTML = `<strong>رقم الهاتف:</strong> ${account.phoneNumber}`;
                                }
                            }
                        }
                    });

                    // التحقق من وجود حسابات جديدة غير موجودة في الصفحة
                    const existingAccountIds = Array.from(document.querySelectorAll('[data-account-id]')).map(el => el.getAttribute('data-account-id'));
                    const newAccounts = data.accounts.filter(account => !existingAccountIds.includes(account.accountName));

                    // إذا كانت هناك حسابات جديدة، أعد تحميل الصفحة بالكامل
                    if (newAccounts.length > 0) {
                        loadAccounts();
                    }
                } else if (document.querySelectorAll('[data-account-id]').length > 0) {
                    // إذا لم تكن هناك حسابات ولكن توجد بطاقات في الصفحة، أعد تحميل الصفحة
                    loadAccounts();
                }
                // تحديث حالة زر Excel بعد تحديث حالة الحسابات
                updateExcelSendButtonState();
            })
            .catch(error => {
                console.error('Error updating accounts status:', error);
                // تحديث حالة زر Excel حتى في حالة الخطأ
                updateExcelSendButtonState();
            });
    }

    connectBtn.addEventListener('click', createConnection);

    // وظيفة تحميل الحسابات المتصلة
    function loadAccounts() {
        fetch('/api/accounts')
            .then(response => response.json())
            .then(data => {
                if (data.accounts && data.accounts.length > 0) {
                    noAccountsMessage.style.display = 'none';
                    renderAccounts(data.accounts);
                    // تحديث حالة زر Excel بعد تحميل الحسابات المتصلة
                    updateExcelSendButtonState();
                } else {
                    // إذا لم تكن هناك حسابات متصلة، تحقق من الحسابات المتاحة
                    loadAvailableAccounts();
                }
            })
            .catch(error => {
                console.error('Error loading accounts:', error);
                showAlert('خطأ في تحميل الحسابات', 'error');
                // في حالة الخطأ، تحقق من الحسابات المتاحة
                loadAvailableAccounts();
            });
    }

    // وظيفة تحميل الحسابات المتاحة (المخزنة في مجلدات الجلسات)
    function loadAvailableAccounts() {
        fetch('/api/available-accounts')
            .then(response => response.json())
            .then(data => {
                if (data.accounts && data.accounts.length > 0) {
                    noAccountsMessage.style.display = 'none';
                    renderAvailableAccounts(data.accounts);
                    // تحديث حالة زر Excel بعد تحميل الحسابات المتاحة
                    updateExcelSendButtonState();
                } else {
                    noAccountsMessage.style.display = 'block';
                    noAccountsMessage.innerHTML = '<p class="text-muted">لا توجد حسابات متاحة. قم بإنشاء حساب جديد للبدء.</p>';
                    accountsContainer.innerHTML = '';
                    // تحديث حالة زر Excel عند عدم وجود حسابات
                    updateExcelSendButtonState();
                }
            })
            .catch(error => {
                console.error('Error loading available accounts:', error);
                showAlert('خطأ في تحميل الحسابات المتاحة', 'error');
                noAccountsMessage.style.display = 'block';
                noAccountsMessage.innerHTML = '<p class="text-muted">حدث خطأ أثناء تحميل الحسابات. يرجى المحاولة مرة أخرى.</p>';
            });
    }

    // وظيفة إنشاء اتصال جديد
    function createConnection() {
        const accountName = document.getElementById('accountName').value.trim();
        const phoneNumber = document.getElementById('phoneNumber').value.trim();
        const connectionType = document.querySelector('input[name="connectionType"]:checked').value;

        if (!accountName || !phoneNumber) {
            showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
            return;
        }

        // التحقق من صحة رقم الهاتف
        if (!/^\d+$/.test(phoneNumber)) {
            showAlert('يرجى إدخال رقم هاتف صحيح (أرقام فقط)', 'warning');
            return;
        }

        // حفظ الاسم الحالي للحساب
        currentAccountName = accountName;

        // إظهار مؤشر التحميل
        Swal.fire({
            title: 'جاري إنشاء الاتصال...',
            text: 'يرجى الانتظار',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // إرسال طلب إنشاء اتصال
        fetch('/api/create-connection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                accountName,
                phoneNumber,
                connectionType
            })
        })
        .then(response => response.json())
        .then(data => {
            Swal.close();

            if (data.success) {
                createAccountModal.hide();

                // تحديث قائمة الحسابات المتاحة لإظهار الحساب الجديد
                loadAvailableAccounts();

                if (connectionType === 'qr') {
                    // عرض نافذة QR
                    qrModal.show();
                    startQrCheck();
                } else if (connectionType === 'pairing') {
                    // عرض نافذة رمز الاقتران
                    pairingModal.show();
                    startInfoCheck();
                }

                showAlert('تم إنشاء الاتصال بنجاح', 'success');
            } else {
                showAlert(data.error || 'حدث خطأ أثناء إنشاء الاتصال', 'error');
            }
        })
        .catch(error => {
            console.error('Error creating connection:', error);
            Swal.close();
            showAlert('حدث خطأ أثناء إنشاء الاتصال', 'error');
        });
    }

    // وظيفة بدء التحقق من QR
    function startQrCheck() {
        // إيقاف أي فاصل زمني سابق
        if (qrCheckInterval) clearInterval(qrCheckInterval);
        if (infoCheckInterval) clearInterval(infoCheckInterval);

        // إظهار مؤشر التحميل في حاوية QR
        document.getElementById('qrContainer').innerHTML = `
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-3">جاري إنشاء رمز QR...</p>
        `;

        // إعادة تعيين علامة عرض رسالة النجاح
        connectionSuccessMessageShown = false;

        // بدء تحديث حالة الاتصال للحساب الحالي كل ثانية
        infoCheckInterval = setInterval(() => {
            updateCurrentAccountStatus();
        }, 1000);

        // التحقق من QR كل ثانيتين
        qrCheckInterval = setInterval(() => {
            fetch(`/api/get-qr/${currentAccountName}`)
                .then(response => response.json())
                .then(data => {
                    if (data.qr) {
                        // عرض QR الرسمي من واتساب
                        document.getElementById('qrContainer').innerHTML = `<img src="${data.qr}" alt="QR Code" class="img-fluid pulse">`;
                    }

                    // إذا كان هناك رمز اقتران وكان حالة الاتصال جاهزة للاقتران
                    if (data.pairingCode && data.status === 'pairing_code_ready') {
                        // عرض رمز الاقتران في نافذة الاقتران
                        const pairingCodeElement = document.getElementById('pairingCode');
                        if (pairingCodeElement) {
                            pairingCodeElement.textContent = data.pairingCode;
                        }

                        // إظهار نافذة رمز الاقتران
                        qrModal.hide();
                        pairingModal.show();
                    }

                    // التحقق من معلومات الحساب فقط إذا كان الحساب في حالة متصل فعلياً
                    if (data.status === 'connected') {
                        checkAccountInfo();
                    }
                })
                .catch(error => {
                    console.error('Error fetching QR:', error);
                });
        }, 2000);
    }

    // وظيفة بدء التحقق من معلومات الحساب
    function startInfoCheck() {
        // إيقاف أي فاصل زمني سابق
        if (infoCheckInterval) clearInterval(infoCheckInterval);
        if (qrCheckInterval) clearInterval(qrCheckInterval);

        // إظهار مؤشر التحميل في حاوية رمز الاقتران
        const pairingCodeElement = document.getElementById('pairingCode');
        if (pairingCodeElement) {
            pairingCodeElement.innerHTML = `
                <div class="spinner-border text-success" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-3">جاري إنشاء رمز الاقتران...</p>
            `;
        }

        // إعادة تعيين علامة عرض رسالة النجاح
        connectionSuccessMessageShown = false;

        // بدء تحديث حالة الاتصال للحساب الحالي كل ثانية
        infoCheckInterval = setInterval(() => {
            updateCurrentAccountStatus();
        }, 1000);

        // التحقق من معلومات الحساب ورمز الاقتران كل ثانيتين
        qrCheckInterval = setInterval(() => {
            // التحقق من رمز الاقتران أولاً
            fetch(`/api/get-qr/${currentAccountName}`)
                .then(response => response.json())
                .then(data => {
                    // إذا كان هناك رمز اقتران وكان حالة الاتصال جاهزة للاقتران
                    if (data.pairingCode && data.status === 'pairing_code_ready') {
                        // عرض رمز الاقتران في نافذة الاقتران
                        const pairingCodeElement = document.getElementById('pairingCode');
                        if (pairingCodeElement) {
                            pairingCodeElement.textContent = data.pairingCode;
                            console.log('تم تحديث رمز الاقتران:', data.pairingCode);
                        }
                    }

                    // التحقق من معلومات الحساب فقط إذا كان الحساب متصلاً فعلياً
                    if (data.status === 'connected') {
                        checkAccountInfo();
                    }
                })
                .catch(error => {
                    console.error('Error fetching pairing code:', error);
                });
        }, 2000);
    }

    // وظيفة التحقق من معلومات الحساب
    function checkAccountInfo() {
        fetch(`/api/account-info/${currentAccountName}`)
            .then(response => response.json())
            .then(data => {
                // فقط إذا كانت معلومات الحساب متوفرة وكان الحساب متصلاً بالفعل
                if (data.info && data.connected === true && data.status === 'connected') {
                    console.log('تم التحقق من معلومات الحساب ونجاح الاتصال:', data.info);

                    // إغلاق النوافذ المنبثقة فقط عند نجاح الاتصال
                    qrModal.hide();
                    pairingModal.hide();

                    // إيقاف الفواصل الزمنية عند نجاح الاتصال
                    if (qrCheckInterval) clearInterval(qrCheckInterval);
                    if (infoCheckInterval) clearInterval(infoCheckInterval);

                    // تحديث قائمة الحسابات
                    loadAccounts();

                    // عرض رسالة نجاح مرة واحدة فقط
                    if (!connectionSuccessMessageShown) {
                        showAlert('تم الاتصال بنجاح!', 'success');
                        connectionSuccessMessageShown = true;
                    }
                }
            })
            .catch(error => {
                console.error('Error checking account info:', error);
            });
    }

    // وظيفة عرض الحسابات المتصلة
    function renderAccounts(accounts) {
        accountsContainer.innerHTML = '';

        accounts.forEach(account => {
            if (account.info) {
                // تحديد صورة الملف الشخصي مع صورة افتراضية
                const profilePic = account.info.profilePictureUrl || 'https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png';

                // إضافة الاسم المُوثق إذا كان متوفرًا
                const verifiedNameInfo = account.info.verifiedName ?
                    `<p><strong>الاسم الموثق:</strong> ${account.info.verifiedName}</p>` : '';

                const accountCard = document.createElement('div');
                accountCard.className = 'col-md-6 col-lg-4';

                // تخزين معرف الحساب كسمة بيانات لتسهيل تحديثه لاحقًا
                accountCard.setAttribute('data-account-id', account.accountName);

                accountCard.innerHTML = `
                    <div class="account-card connected">
                        <div class="account-header bg-success">
                            <h5>${account.accountName}</h5>
                        </div>
                        <div class="account-body text-center">
                            <img src="${profilePic}" alt="صورة الملف الشخصي" class="profile-image" onerror="this.src='https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png'">
                            <div class="status-badge connected">
                                <i class="fas fa-circle text-success me-2"></i>متصل
                            </div>
                            <div class="account-info mt-3">
                                <p><strong>الاسم:</strong> ${account.info.name || 'غير معروف'}</p>
                                ${verifiedNameInfo}
                                <p><strong>رقم الهاتف:</strong> ${account.phoneNumber}</p>
                                <div class="mt-3">
                                    <button class="btn btn-info account-details-btn mb-2" data-account="${account.accountName}">
                                        <i class="fas fa-info-circle me-2"></i>تفاصيل الحساب
                                    </button>
                                    <button class="btn btn-warning auto-reply-btn mb-2" data-account="${account.accountName}" style="display: none;">
                                        <i class="fas fa-robot me-2"></i>الرد التلقائي
                                    </button>
                                    <button class="btn btn-danger delete-account-btn" data-account="${account.accountName}">
                                        <i class="fas fa-trash-alt me-2"></i>حذف الحساب
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                accountsContainer.appendChild(accountCard);
            }
        });

        // إضافة مستمعي الأحداث لأزرار حذف الحساب
        document.querySelectorAll('.delete-account-btn').forEach(button => {
            button.addEventListener('click', handleDeleteAccount);
        });

        // إضافة مستمعي الأحداث لأزرار تفاصيل الحساب
        document.querySelectorAll('.account-details-btn').forEach(button => {
            button.addEventListener('click', handleAccountDetails);
        });

        // إضافة مستمعي الأحداث لأزرار الرد التلقائي
        document.querySelectorAll('.auto-reply-btn').forEach(button => {
            button.addEventListener('click', handleAutoReply);
        });

        // تحديث إظهار/إخفاء أزرار الرد التلقائي بناءً على الوضع الحالي
        updateAutoReplyButtonsVisibility();
    }

    // وظيفة عرض الحسابات المتاحة (غير المتصلة)
    function renderAvailableAccounts(accounts) {
        accountsContainer.innerHTML = '';

        accounts.forEach(account => {
            const accountCard = document.createElement('div');
            accountCard.className = 'col-md-6 col-lg-4';

            // إضافة معلومات رقم الهاتف إذا كانت متوفرة
            const phoneNumberInfo = account.phoneNumber ?
                `<p><strong>رقم الهاتف:</strong> ${account.phoneNumber}</p>` : '';

            // تحديد حالة الاتصال وأيقونة الحالة
            let statusIcon = '';
            let statusText = '';
            let headerClass = '';
            let cardClass = '';
            let statusBadgeClass = '';
            let actionButtons = '';
            let userInfo = '';
            let verifiedNameInfo = '';

            if (account.connected && account.info) {
                // حساب متصل بنجاح
                statusIcon = '<i class="fas fa-circle text-success me-2"></i>';
                statusText = 'متصل';
                headerClass = 'bg-success';
                cardClass = 'connected';
                statusBadgeClass = 'connected';

                // إضافة الاسم المُوثق إذا كان متوفرًا
                verifiedNameInfo = account.info.verifiedName ?
                    `<p><strong>الاسم الموثق:</strong> ${account.info.verifiedName}</p>` : '';

                // إضافة معلومات المستخدم المتصل
                userInfo = `
                    <p><strong>الاسم:</strong> ${account.info.name || 'غير معروف'}</p>
                    ${verifiedNameInfo}
                    ${phoneNumberInfo}
                `;

                // إنشاء أزرار الحساب - سنضيف زر الرد التلقائي دائماً ونتحكم في إظهاره بـ CSS
                let autoReplyButton = `
                    <button class="btn btn-warning auto-reply-btn mb-2" data-account="${account.accountName}" style="display: none;">
                        <i class="fas fa-robot me-2"></i>الرد التلقائي
                    </button>
                `;

                actionButtons = `
                    <button class="btn btn-info account-details-btn mb-2" data-account="${account.accountName}">
                        <i class="fas fa-info-circle me-2"></i>تفاصيل الحساب
                    </button>
                    ${autoReplyButton}
                    <button class="btn btn-danger delete-account-btn" data-account="${account.accountName}">
                        <i class="fas fa-trash-alt me-2"></i>حذف الحساب
                    </button>
                `;
            } else if (account.status === 'connecting' || account.status === 'qr_ready' || account.status === 'pairing_code_ready' || account.status === 'reconnecting') {
                // حساب في طور الاتصال
                statusIcon = '<i class="fas fa-circle text-warning me-2"></i>';
                statusText = account.status === 'qr_ready' ? 'بانتظار مسح QR' :
                             account.status === 'pairing_code_ready' ? 'بانتظار إدخال رمز الاقتران' :
                             account.status === 'reconnecting' ? 'جاري إعادة الاتصال...' : 'جاري الاتصال...';
                headerClass = 'bg-warning text-dark';
                cardClass = 'connecting';
                statusBadgeClass = 'connecting';

                // إضافة معلومات الاتصال الجاري
                userInfo = phoneNumberInfo;

                // إضافة أزرار إعادة المحاولة وحذف الحساب
                actionButtons = `
                    <button class="btn btn-primary reconnect-btn mb-2" data-account="${account.accountName}" data-phone="${account.phoneNumber}">
                        <i class="fas fa-sync-alt me-2"></i>إعادة المحاولة
                    </button>
                    <button class="btn btn-danger delete-account-btn" data-account="${account.accountName}">
                        <i class="fas fa-trash-alt me-2"></i>حذف الحساب
                    </button>
                `;
            } else {
                // حساب غير متصل
                statusIcon = '<i class="fas fa-circle text-danger me-2"></i>';
                statusText = 'غير متصل';
                headerClass = 'bg-secondary';
                cardClass = 'disconnected';
                statusBadgeClass = 'disconnected';

                // إضافة معلومات الحساب غير المتصل
                userInfo = phoneNumberInfo;

                // إضافة زر إعادة الاتصال إذا كان رقم الهاتف متوفرًا
                const reconnectButton = account.phoneNumber ?
                    `<button class="btn btn-success reconnect-btn mb-2" data-account="${account.accountName}" data-phone="${account.phoneNumber}">
                        <i class="fas fa-sync-alt me-2"></i>إعادة الاتصال
                    </button>` : '';

                actionButtons = `
                    ${reconnectButton}
                    <button class="btn btn-danger delete-account-btn" data-account="${account.accountName}">
                        <i class="fas fa-trash-alt me-2"></i>حذف الحساب
                    </button>
                `;
            }

            // تخزين معرف الحساب كسمة بيانات لتسهيل تحديثه لاحقًا
            accountCard.setAttribute('data-account-id', account.accountName);

            // تحديد صورة الملف الشخصي مع صورة افتراضية
            const profilePicture = account.info && account.info.profilePictureUrl ?
                account.info.profilePictureUrl : 'https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png';

            accountCard.innerHTML = `
                <div class="account-card ${cardClass}">
                    <div class="account-header ${headerClass}">
                        <h5>${account.accountName}</h5>
                    </div>
                    <div class="account-body text-center">
                        <img src="${profilePicture}" alt="صورة الملف الشخصي" class="profile-image" onerror="this.src='https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png'">
                        <div class="status-badge ${statusBadgeClass}">
                            ${statusIcon}${statusText}
                        </div>
                        <div class="account-info mt-3">
                            ${userInfo}
                            <div class="mt-3">
                                ${actionButtons}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            accountsContainer.appendChild(accountCard);
        });

        // إضافة مستمعي الأحداث لأزرار حذف الحساب
        document.querySelectorAll('.delete-account-btn').forEach(button => {
            button.addEventListener('click', handleDeleteAccount);
        });

        // إضافة مستمعي الأحداث لأزرار تفاصيل الحساب
        document.querySelectorAll('.account-details-btn').forEach(button => {
            button.addEventListener('click', handleAccountDetails);
        });

        // إضافة مستمعي الأحداث لأزرار الرد التلقائي
        document.querySelectorAll('.auto-reply-btn').forEach(button => {
            button.addEventListener('click', handleAutoReply);
        });

        // تحديث إظهار/إخفاء أزرار الرد التلقائي بناءً على الوضع الحالي
        updateAutoReplyButtonsVisibility();

        // إضافة مستمعي الأحداث لأزرار إعادة الاتصال
        document.querySelectorAll('.reconnect-btn').forEach(button => {
            button.addEventListener('click', function() {
                const accountName = this.getAttribute('data-account');
                const phoneNumber = this.getAttribute('data-phone');

                // عرض نافذة اختيار طريقة الاتصال
                Swal.fire({
                    title: 'اختر طريقة الاتصال',
                    html: `
                        <div class="form-check text-start mb-3">
                            <input class="form-check-input" type="radio" name="reconnectType" id="reconnectQr" value="qr" checked>
                            <label class="form-check-label" for="reconnectQr">
                                الاتصال عن طريق مسح رمز QR
                            </label>
                        </div>
                        <div class="form-check text-start">
                            <input class="form-check-input" type="radio" name="reconnectType" id="reconnectPairing" value="pairing">
                            <label class="form-check-label" for="reconnectPairing">
                                الاتصال عن طريق طلب رمز الاقتران
                            </label>
                        </div>
                    `,
                    showCancelButton: true,
                    confirmButtonText: 'اتصال',
                    cancelButtonText: 'إلغاء',
                    confirmButtonColor: '#25D366',
                    focusConfirm: false,
                    preConfirm: () => {
                        return document.querySelector('input[name="reconnectType"]:checked').value;
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        reconnectAccount(accountName, phoneNumber, result.value);
                    }
                });
            });
        });
    }

    // وظيفة معالجة حذف الحساب
    function handleDeleteAccount(event) {
        const accountName = event.currentTarget.getAttribute('data-account');

        // عرض نافذة تأكيد الحذف
        Swal.fire({
            title: 'تأكيد الحذف',
            text: `هل أنت متأكد من رغبتك في حذف الحساب "${accountName}"؟ سيتم حذف جميع البيانات المرتبطة به نهائيًا.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'نعم، حذف الحساب',
            cancelButtonText: 'إلغاء',
            confirmButtonColor: '#d33'
        }).then((result) => {
            if (result.isConfirmed) {
                deleteAccount(accountName);
            }
        });
    }

    // وظيفة حذف الحساب
    function deleteAccount(accountName) {
        // إظهار مؤشر التحميل
        Swal.fire({
            title: 'جاري حذف الحساب...',
            text: 'يرجى الانتظار',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // إرسال طلب حذف الحساب
        fetch(`/api/delete-account/${accountName}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            Swal.close();

            if (data.success) {
                showAlert('تم حذف الحساب بنجاح', 'success');
                // تحديث قائمة الحسابات
                loadAccounts();
            } else {
                showAlert(data.error || 'حدث خطأ أثناء حذف الحساب', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting account:', error);
            Swal.close();
            showAlert('حدث خطأ أثناء حذف الحساب', 'error');
        });
    }

    // وظيفة إعادة الاتصال بالحساب
    function reconnectAccount(accountName, phoneNumber, connectionType) {
        // حفظ الاسم الحالي للحساب
        currentAccountName = accountName;

        // إظهار مؤشر التحميل
        Swal.fire({
            title: 'جاري إعادة الاتصال...',
            text: 'يرجى الانتظار',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // إرسال طلب إعادة الاتصال
        fetch(`/api/reconnect/${accountName}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                phoneNumber,
                connectionType
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.close();

                if (connectionType === 'qr') {
                    // عرض نافذة QR
                    qrModal.show();
                    startQrCheck();
                } else if (connectionType === 'pairing') {
                    // عرض نافذة رمز الاقتران
                    pairingModal.show();
                    startInfoCheck();
                }

                showAlert('تم بدء إعادة الاتصال بنجاح', 'success');
            } else {
                Swal.close();
                showAlert(data.error || 'حدث خطأ أثناء إعادة الاتصال', 'error');
            }
        })
        .catch(error => {
            console.error('Error reconnecting:', error);
            Swal.close();
            showAlert('حدث خطأ أثناء إعادة الاتصال', 'error');
        });
    }

    // وظيفة إعادة تعيين النموذج
    function resetForm() {
        accountForm.reset();
        document.getElementById('qrType').checked = true;
    }

    // وظيفة عرض التنبيهات
    function showAlert(message, type = 'success') {
        Swal.fire({
            title: type === 'success' ? 'نجاح' : type === 'warning' ? 'تنبيه' : 'خطأ',
            text: message,
            icon: type,
            confirmButtonText: 'حسنًا',
            confirmButtonColor: '#25D366'
        });
    }

    // وظيفة معالجة النقر على زر تفاصيل الحساب
    function handleAccountDetails(event) {
        const accountName = event.currentTarget.getAttribute('data-account');
        // الانتقال إلى صفحة تفاصيل الحساب
        window.location.href = `/account-details.html?account=${encodeURIComponent(accountName)}`;
    }

    // وظيفة تحديث حالة الاتصال للحساب الحالي
    function updateCurrentAccountStatus() {
        if (!currentAccountName) return;

        // التحقق من حالة الاتصال للحساب الحالي
        fetch(`/api/connection-status/${currentAccountName}`)
            .then(response => response.json())
            .then(data => {
                // تحديث حالة الاتصال في واجهة المستخدم
                const accountCard = document.querySelector(`[data-account-id="${currentAccountName}"]`);
                if (accountCard) {
                    // الحصول على عناصر البطاقة
                    const accountCardDiv = accountCard.querySelector('.account-card');
                    const accountHeader = accountCard.querySelector('.account-header');
                    const statusBadge = accountCard.querySelector('.status-badge');

                    // تحديث حالة الاتصال
                    if (statusBadge) {
                        let statusIcon = '';
                        let statusText = '';
                        let statusClass = '';
                        let headerClass = '';
                        let cardClass = '';

                        if (data.connected && data.status === 'connected') {
                            statusIcon = '<i class="fas fa-circle text-success me-2"></i>';
                            statusText = 'متصل';
                            statusClass = 'connected';
                            headerClass = 'bg-success';
                            cardClass = 'connected';

                            // إيقاف الفواصل الزمنية لأنه أصبح متصلاً بالفعل
                            if (qrCheckInterval) clearInterval(qrCheckInterval);
                            if (infoCheckInterval) clearInterval(infoCheckInterval);

                            // إغلاق النوافذ المنبثقة فقط إذا كان الاتصال ناجحاً بالفعل
                            qrModal.hide();
                            pairingModal.hide();

                            // تحديث قائمة الحسابات
                            loadAccounts();

                            // عرض رسالة نجاح مرة واحدة فقط
                            if (!connectionSuccessMessageShown) {
                                showAlert('تم الاتصال بنجاح!', 'success');
                                connectionSuccessMessageShown = true;
                            }
                        } else if (data.status === 'connecting' || data.status === 'qr_ready' || data.status === 'pairing_code_ready' || data.status === 'reconnecting' || data.status === 'authenticated' || data.status === 'initializing') {
                            statusIcon = '<i class="fas fa-circle text-warning me-2"></i>';
                            statusText = data.status === 'qr_ready' ? 'بانتظار مسح QR' :
                                         data.status === 'pairing_code_ready' ? 'بانتظار إدخال رمز الاقتران' :
                                         data.status === 'authenticated' ? 'تم المصادقة، جاري الاتصال...' :
                                         data.status === 'reconnecting' ? 'جاري إعادة الاتصال...' : 'جاري الاتصال...';
                            statusClass = 'connecting';
                            headerClass = 'bg-warning text-dark';
                            cardClass = 'connecting';

                            // إعادة تعيين علامة عرض رسالة النجاح
                            connectionSuccessMessageShown = false;
                        } else {
                            statusIcon = '<i class="fas fa-circle text-danger me-2"></i>';
                            statusText = 'غير متصل';
                            statusClass = 'disconnected';
                            headerClass = 'bg-secondary';
                            cardClass = 'disconnected';

                            // إعادة تعيين علامة عرض رسالة النجاح
                            connectionSuccessMessageShown = false;
                        }

                        // تحديث نص وفئة الحالة
                        statusBadge.innerHTML = `${statusIcon}${statusText}`;
                        statusBadge.className = `status-badge ${statusClass}`;

                        // تحديث فئة البطاقة والعنوان
                        if (accountCardDiv) {
                            accountCardDiv.className = `account-card ${cardClass}`;
                        }

                        if (accountHeader) {
                            // حفظ النص الحالي للعنوان
                            const headerText = accountHeader.innerHTML;
                            // إزالة جميع الفئات وإضافة الفئة الجديدة
                            accountHeader.className = `account-header ${headerClass}`;
                            // إعادة تعيين النص
                            accountHeader.innerHTML = headerText;
                        }
                    }

                    // تحديث معلومات الحساب إذا كانت متوفرة
                    if (data.info) {
                        // تحديث صورة الملف الشخصي
                        if (data.info.profilePictureUrl) {
                            const profileImage = accountCard.querySelector('.profile-image');
                            if (profileImage) {
                                profileImage.src = data.info.profilePictureUrl;
                            }
                        }

                        // تحديث معلومات الحساب
                        const accountInfo = accountCard.querySelector('.account-info');
                        if (accountInfo) {
                            // تحديث الاسم
                            const nameElement = accountInfo.querySelector('p:first-child');
                            if (nameElement && data.info.name) {
                                nameElement.innerHTML = `<strong>الاسم:</strong> ${data.info.name || 'غير معروف'}`;
                            }

                            // تحديث الاسم الموثق
                            const verifiedNameElement = accountInfo.querySelector('p:nth-child(2)');
                            if (verifiedNameElement && data.info.verifiedName) {
                                verifiedNameElement.innerHTML = `<strong>الاسم الموثق:</strong> ${data.info.verifiedName}`;
                            }

                            // تحديث رقم الهاتف
                            const phoneElement = accountInfo.querySelector('p:nth-child(3)');
                            if (phoneElement && data.info.number) {
                                phoneElement.innerHTML = `<strong>رقم الهاتف:</strong> ${data.info.number}`;
                            }
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Error updating current account status:', error);
            });
    }
    // وظيفة تحديث حالة زر إرسال Excel
    function updateExcelSendButtonState() {
        const excelSendBtn = document.getElementById('excelSendBtn');
        
        // التحقق من وجود حساب متصل واحد على الأقل
        fetch('/api/available-accounts') // تم التغيير إلى المسار الصحيح
            .then(response => response.json())
            .then(data => {
                // تصفية الحسابات للتأكد من أنها متصلة بالفعل وجاهزة
                const connectedAccounts = data.accounts ? data.accounts.filter(acc => acc.connected && (acc.status === 'connected' || acc.status === 'authenticated')) : [];
                const hasConnectedAccount = connectedAccounts.length > 0;
                
                if (hasConnectedAccount) {
                    // تفعيل الزر إذا كان هناك حساب متصل
                    excelSendBtn.disabled = false;
                    excelSendBtn.classList.remove('btn-secondary');
                    excelSendBtn.classList.add('btn-primary');
                    excelSendBtn.title = 'إرسال رسائل من ملف Excel';
                } else {
                    // تعطيل الزر إذا لم يكن هناك حساب متصل
                    excelSendBtn.disabled = true;
                    excelSendBtn.classList.remove('btn-primary');
                    excelSendBtn.classList.add('btn-secondary');
                    excelSendBtn.title = 'يجب أن يكون هناك حساب واحد متصل على الأقل لاستخدام هذه الميزة';
                }
            })
            .catch(error => {
                console.error('Error checking connected accounts:', error);
                // في حالة الخطأ، تعطيل الزر احتياطياً
                excelSendBtn.disabled = true;
                excelSendBtn.classList.remove('btn-primary');
                excelSendBtn.classList.add('btn-secondary');
                excelSendBtn.title = 'خطأ في التحقق من الحسابات المتصلة';
            });
    }

    // وظيفة للتنقل إلى صفحة الرد التلقائي للحساب
    function handleAutoReply(event) {
        const accountName = event.target.getAttribute('data-account');
        if (accountName) {
            window.location.href = `auto-reply-account-new.html?account=${encodeURIComponent(accountName)}`;
        }
    }

    // إضافة مستمع الأحداث لزر الرد التلقائي العام
    const globalAutoReplyBtn = document.getElementById('globalAutoReplyBtn');
    if (globalAutoReplyBtn) {
        globalAutoReplyBtn.addEventListener('click', function() {
            window.location.href = 'auto-reply-global-new.html';
        });
    }

    // إدارة خيارات الرد التلقائي
    const globalAutoReplyMode = document.getElementById('globalAutoReplyMode');
    const accountAutoReplyMode = document.getElementById('accountAutoReplyMode');
    const autoReplyModeDescription = document.getElementById('autoReplyModeDescription');
    const saveAutoReplyModeBtn = document.getElementById('saveAutoReplyModeBtn');

    // مستمعي أحداث تغيير وضع الرد التلقائي
    if (globalAutoReplyMode) {
        globalAutoReplyMode.addEventListener('change', updateAutoReplyModeDescription);
    }
    if (accountAutoReplyMode) {
        accountAutoReplyMode.addEventListener('change', updateAutoReplyModeDescription);
    }

    // مستمع حفظ إعدادات وضع الرد التلقائي
    if (saveAutoReplyModeBtn) {
        saveAutoReplyModeBtn.addEventListener('click', saveAutoReplyModeSettings);
    }

    function loadAutoReplyModeSettings(skipAccountsReload = false) {
        return fetch('/api/auto-reply/mode/settings')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const mode = data.mode || 'global';
                    currentAutoReplyMode = mode; // تحديث المتغير العام

                    if (mode === 'global' && globalAutoReplyMode) {
                        globalAutoReplyMode.checked = true;
                    } else if (mode === 'account' && accountAutoReplyMode) {
                        accountAutoReplyMode.checked = true;
                    }
                    updateAutoReplyModeDescription(skipAccountsReload);

                    // إعادة تحميل الحسابات لتحديث الأزرار (إلا إذا طُلب تجاهل ذلك)
                    if (!skipAccountsReload) {
                        loadAccounts();
                    }
                }
            })
            .catch(error => {
                console.error('Error loading auto-reply mode settings:', error);
            });
    }

    function updateAutoReplyModeDescription(skipAccountsReload = false) {
        if (!autoReplyModeDescription) return;

        if (globalAutoReplyMode && globalAutoReplyMode.checked) {
            currentAutoReplyMode = 'global'; // تحديث المتغير العام
            autoReplyModeDescription.innerHTML = `
                <i class="fas fa-info-circle me-2"></i>
                <strong>الوضع الحالي: الرد التلقائي العام</strong>
                <br>
                في هذا الوضع، سيتم تطبيق نفس إعدادات الرد التلقائي على جميع الحسابات المتصلة. يمكنك تعديل الإعدادات من خلال زر "الرد التلقائي العام" أعلاه.
            `;
            autoReplyModeDescription.className = 'alert alert-info';
        } else if (accountAutoReplyMode && accountAutoReplyMode.checked) {
            currentAutoReplyMode = 'account'; // تحديث المتغير العام
            autoReplyModeDescription.innerHTML = `
                <i class="fas fa-info-circle me-2"></i>
                <strong>الوضع الحالي: الرد التلقائي الخاص</strong>
                <br>
                في هذا الوضع، يمكنك إعداد رد تلقائي مختلف لكل حساب. استخدم أزرار "الرد التلقائي" في بطاقات الحسابات لتخصيص الإعدادات.
            `;
            autoReplyModeDescription.className = 'alert alert-warning';
        }

        // تحديث إظهار/إخفاء أزرار الرد التلقائي
        updateAutoReplyButtonsVisibility();

        // إعادة تحميل الحسابات لتحديث الأزرار (إلا إذا طُلب تجاهل ذلك)
        if (!skipAccountsReload) {
            loadAccounts();
        }
    }

    // دالة محسنة لتحديث إظهار/إخفاء أزرار الرد التلقائي مع التحقق من التكامل
    function updateAutoReplyButtonsVisibility() {
        console.log(`🔄 Updating auto-reply buttons visibility for mode: ${currentAutoReplyMode}`);

        const autoReplyButtons = document.querySelectorAll('.auto-reply-btn');
        const advancedAutoReplyButtons = document.querySelectorAll('.advanced-auto-reply-btn');

        // تحديث أزرار الرد التلقائي العادي
        autoReplyButtons.forEach(button => {
            if (currentAutoReplyMode === 'account') {
                button.style.display = 'block';
                button.title = 'إعداد الرد التلقائي الخاص بهذا الحساب';
            } else {
                button.style.display = 'none';
                button.title = 'الرد التلقائي الخاص متاح فقط في الوضع الخاص';
            }
        });

        // تحديث أزرار الرد التلقائي المتقدم
        advancedAutoReplyButtons.forEach(button => {
            if (currentAutoReplyMode === 'account') {
                button.style.display = 'block';
                button.title = 'إعداد الرد التلقائي المتقدم الخاص بهذا الحساب';
            } else {
                button.style.display = 'none';
                button.title = 'الرد التلقائي المتقدم الخاص متاح فقط في الوضع الخاص';
            }
        });

        // تحديث أزرار الرد التلقائي العام
        const globalAutoReplyBtn = document.getElementById('globalAutoReplyBtn');
        const globalAdvancedAutoReplyBtn = document.getElementById('globalAdvancedAutoReplyBtn');

        if (globalAutoReplyBtn) {
            if (currentAutoReplyMode === 'global') {
                globalAutoReplyBtn.style.display = 'block';
                globalAutoReplyBtn.title = 'إعداد الرد التلقائي العام لجميع الحسابات';
            } else {
                globalAutoReplyBtn.style.display = 'none';
                globalAutoReplyBtn.title = 'الرد التلقائي العام متاح فقط في الوضع العام';
            }
        }

        if (globalAdvancedAutoReplyBtn) {
            if (currentAutoReplyMode === 'global') {
                globalAdvancedAutoReplyBtn.style.display = 'block';
                globalAdvancedAutoReplyBtn.title = 'إعداد الرد التلقائي المتقدم العام لجميع الحسابات';
            } else {
                globalAdvancedAutoReplyBtn.style.display = 'none';
                globalAdvancedAutoReplyBtn.title = 'الرد التلقائي المتقدم العام متاح فقط في الوضع العام';
            }
        }

        console.log(`✅ Auto-reply buttons visibility updated successfully`);
    }

    // وظيفة لاختبار تكامل أنظمة الرد التلقائي
    function testAutoReplyIntegration() {
        const mode = currentAutoReplyMode;
        const accountName = mode === 'account' ? 'test-account' : null;

        console.log(`🧪 Testing auto-reply integration for mode: ${mode}`);

        fetch('/api/test-auto-reply-integration', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ mode, accountName })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('🎉 Integration test results:', data);

                // عرض النتائج في نافذة منبثقة
                let resultHtml = `
                    <div class="text-end" dir="rtl">
                        <h5>نتائج اختبار التكامل</h5>
                        <p><strong>الوضع الحالي:</strong> ${mode === 'global' ? 'عام' : 'خاص'}</p>
                        <hr>
                        <h6>حالة الأنظمة:</h6>
                `;

                if (data.systems_status.regular_global) {
                    const status = data.systems_status.regular_global;
                    resultHtml += `
                        <p><strong>الرد التلقائي العام:</strong>
                        <span class="badge ${status.enabled ? 'bg-success' : 'bg-danger'}">${status.enabled ? 'مفعل' : 'معطل'}</span>
                        ${status.has_content ? '<span class="badge bg-info">يحتوي على محتوى</span>' : '<span class="badge bg-warning">لا يحتوي على محتوى</span>'}
                        </p>
                    `;
                }

                if (data.systems_status.regular_account) {
                    const status = data.systems_status.regular_account;
                    resultHtml += `
                        <p><strong>الرد التلقائي الخاص:</strong>
                        <span class="badge ${status.enabled ? 'bg-success' : 'bg-danger'}">${status.enabled ? 'مفعل' : 'معطل'}</span>
                        ${status.has_content ? '<span class="badge bg-info">يحتوي على محتوى</span>' : '<span class="badge bg-warning">لا يحتوي على محتوى</span>'}
                        </p>
                    `;
                }

                if (data.systems_status.advanced_global) {
                    const status = data.systems_status.advanced_global;
                    resultHtml += `
                        <p><strong>الرد التلقائي المتقدم العام:</strong>
                        <span class="badge ${status.enabled ? 'bg-success' : 'bg-danger'}">${status.enabled ? 'مفعل' : 'معطل'}</span>
                        <span class="badge bg-info">${status.enabled_rules_count} قاعدة مفعلة</span>
                        ${status.ai_fallback_enabled ? '<span class="badge bg-primary">الذكاء الاصطناعي مفعل</span>' : '<span class="badge bg-secondary">الذكاء الاصطناعي معطل</span>'}
                        </p>
                    `;
                }

                if (data.systems_status.advanced_account) {
                    const status = data.systems_status.advanced_account;
                    resultHtml += `
                        <p><strong>الرد التلقائي المتقدم الخاص:</strong>
                        <span class="badge ${status.enabled ? 'bg-success' : 'bg-danger'}">${status.enabled ? 'مفعل' : 'معطل'}</span>
                        <span class="badge bg-info">${status.enabled_rules_count} قاعدة مفعلة</span>
                        ${status.ai_fallback_enabled ? '<span class="badge bg-primary">الذكاء الاصطناعي مفعل</span>' : '<span class="badge bg-secondary">الذكاء الاصطناعي معطل</span>'}
                        </p>
                    `;
                }

                resultHtml += `
                        <hr>
                        <h6>تحليل التكامل:</h6>
                        <p><strong>التوصية:</strong> ${data.integration_analysis.recommendation}</p>
                    </div>
                `;

                Swal.fire({
                    title: 'نتائج اختبار التكامل',
                    html: resultHtml,
                    icon: data.integration_analysis.any_system_enabled ? 'success' : 'warning',
                    confirmButtonText: 'موافق',
                    width: '600px'
                });
            } else {
                throw new Error(data.error || 'حدث خطأ في الاختبار');
            }
        })
        .catch(error => {
            console.error('❌ Error testing integration:', error);
            Swal.fire({
                title: 'خطأ في الاختبار',
                text: 'حدث خطأ أثناء اختبار التكامل: ' + error.message,
                icon: 'error'
            });
        });
    }

    // إضافة زر اختبار التكامل إلى لوحة التحكم (إذا لم يكن موجوداً)
    function addIntegrationTestButton() {
        const saveBtn = document.getElementById('saveAutoReplyModeBtn');
        if (saveBtn && !document.getElementById('testIntegrationBtn')) {
            const testBtn = document.createElement('button');
            testBtn.id = 'testIntegrationBtn';
            testBtn.className = 'btn btn-info me-2';
            testBtn.innerHTML = '<i class="fas fa-flask me-2"></i>اختبار التكامل';
            testBtn.addEventListener('click', testAutoReplyIntegration);

            saveBtn.parentNode.insertBefore(testBtn, saveBtn);
        }
    }

    // إضافة زر الاختبار عند تحميل الصفحة
    setTimeout(addIntegrationTestButton, 1000);

    function saveAutoReplyModeSettings() {
        const mode = globalAutoReplyMode && globalAutoReplyMode.checked ? 'global' : 'account';
        currentAutoReplyMode = mode; // تحديث المتغير العام

        fetch('/api/auto-reply/mode/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ mode: mode })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إعادة تحميل الحسابات لتحديث الأزرار
                loadAccounts();

                Swal.fire({
                    title: 'تم الحفظ!',
                    text: 'تم حفظ إعدادات وضع الرد التلقائي بنجاح',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
            } else {
                throw new Error(data.error || 'حدث خطأ في الحفظ');
            }
        })
        .catch(error => {
            console.error('Error saving auto-reply mode settings:', error);
            Swal.fire({
                title: 'خطأ',
                text: 'حدث خطأ في حفظ إعدادات الوضع: ' + error.message,
                icon: 'error'
            });
        });
    }
});