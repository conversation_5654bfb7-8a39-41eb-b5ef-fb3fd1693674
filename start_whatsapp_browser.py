#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WhatsApp Node - نظام تشغيل المتصفح المدمج
يقوم هذا الملف بتشغيل السيرفر والمتصفح معاً كنظام واحد متكامل
"""

import sys
import os
import subprocess
import time
import signal
import threading
import requests
import json
from PyQt5.QtWidgets import QApplication, QSplashScreen, QLabel
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont
from LocalBrowser import WhatsAppBrowser

class WhatsAppLauncher:
    """مُشغل التطبيق المتكامل"""
    
    def __init__(self):
        self.project_path = os.path.dirname(os.path.abspath(__file__))
        self.server_process = None
        self.server_port = 3000
        self.server_url = f"http://localhost:{self.server_port}"
        
    def check_dependencies(self):
        """فحص المتطلبات والتبعيات"""
        print("🔍 فحص المتطلبات...")
        
        # فحص Node.js
        try:
            result = subprocess.run(["node", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Node.js متوفر: {result.stdout.strip()}")
            else:
                print("❌ Node.js غير مثبت")
                return False
        except FileNotFoundError:
            print("❌ Node.js غير موجود في PATH")
            return False
        
        # فحص ملف server.js
        server_file = os.path.join(self.project_path, "server.js")
        if os.path.exists(server_file):
            print("✅ ملف server.js موجود")
        else:
            print("❌ ملف server.js غير موجود")
            return False
        
        # فحص package.json
        package_file = os.path.join(self.project_path, "package.json")
        if os.path.exists(package_file):
            print("✅ ملف package.json موجود")
        else:
            print("❌ ملف package.json غير موجود")
            return False
        
        # فحص node_modules
        modules_dir = os.path.join(self.project_path, "node_modules")
        if os.path.exists(modules_dir):
            print("✅ مجلد node_modules موجود")
        else:
            print("⚠️ مجلد node_modules غير موجود - جاري تثبيت التبعيات...")
            return self.install_dependencies()
        
        return True
    
    def install_dependencies(self):
        """تثبيت تبعيات Node.js"""
        try:
            print("📦 جاري تثبيت التبعيات...")
            result = subprocess.run(
                ["npm", "install"],
                cwd=self.project_path,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print("✅ تم تثبيت التبعيات بنجاح")
                return True
            else:
                print(f"❌ فشل في تثبيت التبعيات: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تثبيت التبعيات: {str(e)}")
            return False
    
    def start_server(self):
        """بدء سيرفر Node.js"""
        try:
            print("🚀 بدء سيرفر Node.js...")
            
            # إنشاء عملية السيرفر
            self.server_process = subprocess.Popen(
                ["node", "server.js"],
                cwd=self.project_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            # انتظار بدء السيرفر
            print("⏳ انتظار بدء السيرفر...")
            for i in range(30):  # انتظار حتى 30 ثانية
                try:
                    response = requests.get(self.server_url, timeout=2)
                    if response.status_code == 200:
                        print(f"✅ السيرفر يعمل على {self.server_url}")
                        return True
                except requests.exceptions.RequestException:
                    pass
                
                time.sleep(1)
                print(f"⏳ انتظار... ({i+1}/30)")
            
            print("❌ فشل في بدء السيرفر")
            return False
            
        except Exception as e:
            print(f"❌ خطأ في بدء السيرفر: {str(e)}")
            return False
    
    def stop_server(self):
        """إيقاف السيرفر"""
        if self.server_process:
            try:
                print("🛑 إيقاف السيرفر...")
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
                print("✅ تم إيقاف السيرفر")
            except subprocess.TimeoutExpired:
                print("⚠️ إجبار إيقاف السيرفر...")
                if os.name == 'nt':
                    subprocess.run(["taskkill", "/F", "/PID", str(self.server_process.pid)], 
                                 capture_output=True)
                else:
                    os.kill(self.server_process.pid, signal.SIGKILL)
                print("✅ تم إجبار إيقاف السيرفر")
            except Exception as e:
                print(f"❌ خطأ في إيقاف السيرفر: {str(e)}")
    
    def create_splash_screen(self, app):
        """إنشاء شاشة البداية"""
        # إنشاء صورة شاشة البداية
        splash_pixmap = QPixmap(400, 300)
        splash_pixmap.fill(Qt.white)
        
        # إنشاء شاشة البداية
        splash = QSplashScreen(splash_pixmap)
        splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.SplashScreen)
        
        # إضافة نص
        splash.showMessage(
            "🚀 WhatsApp Node\n\nجاري تحميل التطبيق...\nيرجى الانتظار",
            Qt.AlignCenter,
            Qt.black
        )
        
        splash.show()
        app.processEvents()
        
        return splash
    
    def run(self):
        """تشغيل التطبيق الكامل"""
        print("=" * 50)
        print("🚀 WhatsApp Node - المتصفح المدمج")
        print("=" * 50)
        
        # فحص المتطلبات
        if not self.check_dependencies():
            print("❌ فشل في فحص المتطلبات")
            input("اضغط Enter للخروج...")
            return False
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        app.setApplicationName("WhatsApp Node Browser")
        app.setApplicationVersion("1.0")
        
        # عرض شاشة البداية
        splash = self.create_splash_screen(app)
        
        try:
            # بدء السيرفر
            splash.showMessage(
                "🚀 WhatsApp Node\n\nجاري بدء السيرفر...\nيرجى الانتظار",
                Qt.AlignCenter,
                Qt.black
            )
            app.processEvents()
            
            if not self.start_server():
                splash.close()
                print("❌ فشل في بدء السيرفر")
                input("اضغط Enter للخروج...")
                return False
            
            # بدء المتصفح
            splash.showMessage(
                "🚀 WhatsApp Node\n\nجاري تحميل المتصفح...\nيرجى الانتظار",
                Qt.AlignCenter,
                Qt.black
            )
            app.processEvents()
            
            # إنشاء المتصفح
            browser = WhatsAppBrowser()
            
            # إغلاق شاشة البداية
            splash.close()
            
            # عرض المتصفح
            browser.show()
            
            print("✅ تم تشغيل التطبيق بنجاح!")
            print(f"🌐 التطبيق متاح على: {self.server_url}")
            print("🔒 المتصفح محمي ومقيد للاستخدام المحلي فقط")
            
            # تشغيل التطبيق
            result = app.exec_()
            
            # إيقاف السيرفر عند الإغلاق
            self.stop_server()
            
            return result == 0
            
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف التطبيق بواسطة المستخدم")
            splash.close()
            self.stop_server()
            return False
        except Exception as e:
            print(f"❌ خطأ في تشغيل التطبيق: {str(e)}")
            splash.close()
            self.stop_server()
            return False

def main():
    """الدالة الرئيسية"""
    launcher = WhatsAppLauncher()
    success = launcher.run()
    
    if not success:
        print("\n❌ فشل في تشغيل التطبيق")
        input("اضغط Enter للخروج...")
        sys.exit(1)

if __name__ == "__main__":
    main()
