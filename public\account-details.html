<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل حساب واتساب</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.12/dist/sweetalert2.min.css">
    <!-- Google Fonts - Tajawal -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            background-color: #f0f2f5;
            font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .header {
            background-color: #128C7E;
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .profile-section {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .profile-image {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin-left: 20px;
        }

        .account-info {
            background-color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .stats-card {
            background-color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
            cursor: pointer;
        }

        .stats-icon {
            font-size: 2rem;
            color: #128C7E;
            margin-bottom: 10px;
        }

        .stats-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #333;
        }

        .stats-label {
            font-size: 1rem;
            color: #666;
        }

        .contacts-table {
            background-color: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .contacts-table th {
            background-color: #f0f2f5;
            border: none;
        }

        .contacts-table td {
            vertical-align: middle;
        }

        .contact-image {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .btn-whatsapp {
            background-color: #25D366;
            color: white;
        }

        .btn-whatsapp:hover {
            background-color: #128C7E;
            color: white;
        }

        .back-btn {
            margin-bottom: 20px;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-right: 10px;
        }

        .status-badge.connected {
            background-color: #d4edda;
            color: #155724;
        }

        .status-badge.disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }

        /* تنسيقات لمؤشر التحميل */
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 250px;
            padding: 20px;
        }

        .loading-info {
            text-align: center;
            width: 100%;
            max-width: 500px;
        }

        .progress {
            height: 15px !important;
            border-radius: 10px;
            background-color: #f0f0f0;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
        }

        .progress-bar {
            border-radius: 10px;
            font-size: 10px;
            line-height: 15px;
            font-weight: bold;
        }

        /* تنسيقات للجدول وأزرار الترتيب */
        .sortable {
            cursor: pointer;
            position: relative;
            user-select: none;
        }

        .sortable:hover {
            background-color: #e9ecef;
        }

        .pagination .page-link {
            color: #128C7E;
        }

        .pagination .page-item.active .page-link {
            background-color: #128C7E;
            border-color: #128C7E;
            color: white;
        }

        .badge {
            font-size: 0.8rem;
            padding: 0.35em 0.65em;
        }

        /* تنسيقات لعرض الاسم بشكل عصري */
        .contact-name {
            display: flex;
            flex-direction: column;
            gap: 4px;
            padding: 2px 0;
        }

        .whatsapp-name {
            color: #075E54;
            font-size: 1rem;
            display: flex;
            align-items: center;
            font-weight: bold;
        }

        .saved-name {
            font-size: 0.85rem;
            color: #666;
            display: flex;
            align-items: center;
            margin-top: 2px;
        }

        .default-name {
            color: #333;
            font-size: 1rem;
            display: flex;
            align-items: center;
        }

        .verified-name {
            font-size: 0.8rem;
            color: #128C7E;
            display: flex;
            align-items: center;
            margin-top: 2px;
        }

        /* تنسيقات للأيقونات */
        .fa-whatsapp {
            font-size: 1rem;
            color: #25D366;
            margin-right: 5px;
        }

        .fa-user-edit {
            font-size: 0.8rem;
            color: #666;
            margin-right: 5px;
        }

        .fa-check-circle {
            font-size: 0.8rem;
            color: #128C7E;
            margin-right: 5px;
        }

        /* تنسيقات للجدول المختار */
        .selected-items-table {
            background-color: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            border: 2px solid #25D366;
        }

        .selected-items-table th {
            background-color: #e7f8ef;
            border: none;
        }

        .selected-item-row {
            transition: all 0.2s ease;
        }

        .selected-item-row:hover {
            background-color: #f5f5f5;
        }

        .remove-item-btn {
            color: #dc3545;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .remove-item-btn:hover {
            color: #bd2130;
            transform: scale(1.2);
        }

        .selectable-row {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .selectable-row:hover {
            background-color: #e7f8ef;
        }

        .empty-selection-message {
            padding: 20px;
            text-align: center;
            color: #6c757d;
        }

        /* تنسيقات للبطاقات المتحركة */
        .card-hover-effect {
            transition: all 0.3s ease;
        }

        .card-hover-effect:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="profile-section">
                <img id="profileImage" src="https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png" alt="صورة الملف الشخصي" class="profile-image">
                <div>
                    <h1 id="accountName">جاري تحميل معلومات الحساب...</h1>
                    <div id="statusBadge" class="status-badge">
                        <i class="fas fa-circle text-secondary me-2"></i>جاري التحميل...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <button id="backBtn" class="btn btn-secondary back-btn">
            <i class="fas fa-arrow-right me-2"></i>العودة للرئيسية
        </button>

        <div class="row">
            <!-- معلومات الحساب -->
            <div class="col-md-4">
                <div class="account-info card-hover-effect">
                    <h3>معلومات الحساب</h3>
                    <hr>
                    <div id="accountInfo">
                        <div class="loading-spinner">
                            <div class="spinner-border text-success" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات الحساب -->
                <div class="stats-card text-center card-hover-effect">
                    <div class="stats-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-value" id="groupsCount">0</div>
                    <div class="stats-label">المجموعات</div>
                    <div class="row mt-3">
                        <div class="col-6">
                            <div class="stats-value" id="adminGroupsCount">0</div>
                            <div class="stats-label">أنت مدير</div>
                        </div>
                        <div class="col-6">
                            <div class="stats-value" id="memberGroupsCount">0</div>
                            <div class="stats-label">أنت عضو</div>
                        </div>
                    </div>
                </div>

                <div class="stats-card text-center card-hover-effect">
                    <div class="stats-icon">
                        <i class="fas fa-address-book"></i>
                    </div>
                    <div class="stats-value" id="contactsCount">0</div>
                    <div class="stats-label">جهات الاتصال</div>
                </div>

                <button id="loadContactsBtn" class="btn btn-whatsapp w-100 mb-3">
                    <i class="fas fa-address-book me-2"></i>عرض جهات الاتصال
                </button>

                <button id="autoReplyBtn" class="btn btn-outline-primary w-100 mb-3">
                    <i class="fas fa-robot me-2"></i>إعدادات الرد التلقائي
                </button>

                <!-- الجدول المختار -->
                <div class="selected-items-table">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3 class="mb-0">العناصر المختارة</h3>
                        <button id="clearSelectedBtn" class="btn btn-sm btn-outline-danger">
                            <i class="fas fa-trash-alt me-1"></i>مسح الكل
                        </button>
                    </div>
                    <hr>
                    <div id="selectedItemsContainer">
                        <div class="empty-selection-message">
                            <i class="fas fa-info-circle mb-2" style="font-size: 2rem; color: #6c757d;"></i>
                            <p>اضغط على أي عنصر من جدول جهات الاتصال أو المجموعات لإضافته هنا</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جهات الاتصال -->
            <div class="col-md-8">
        <div class="contacts-table">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3>جهات الاتصال</h3>
                <button id="exportContactsBtn" class="btn btn-success"><i class="fas fa-file-excel me-2"></i>تصدير إلى Excel</button>
            </div>
            <div id="contactsContainer" style="display: none;">
                <div class="input-group mb-3">
                </div>
            </div>
        </div>
            </div>
        </div>
    </div>

    <!-- Modal for Image Popup -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalLabel">صورة جهة الاتصال</h5>
                    <div>
                        <button id="zoomInBtn" class="btn btn-sm btn-light me-1" title="تكبير"><i class="fas fa-search-plus"></i></button>
                        <button id="zoomOutBtn" class="btn btn-sm btn-light me-1" title="تصغير"><i class="fas fa-search-minus"></i></button>
                        <button id="resetZoomBtn" class="btn btn-sm btn-light me-1" title="إعادة ضبط"><i class="fas fa-redo"></i></button>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                </div>
                <div class="modal-body text-center p-0 position-relative">
                    <div class="image-container" style="background-color: #f8f9fa; padding: 20px; min-height: 400px; display: flex; align-items: center; justify-content: center; overflow: auto; max-height: 80vh;">
                        <img id="modalImage" src="" alt="صورة جهة الاتصال" class="rounded" style="box-shadow: 0 4px 15px rgba(0,0,0,0.1); max-width: 100%; max-height: 70vh; object-fit: contain;">
                    </div>
                </div>
                <div class="modal-footer">
                    <span class="text-muted me-auto" id="imageInfo">جاري التحميل...</span>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script>
        if (localStorage.getItem('loggedIn') !== 'true') {
            window.location.href = 'index.html';
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.12/dist/sweetalert2.all.min.js"></script>
    <!-- تفاصيل الحساب JS -->
    <script src="account-details.js"></script>
    <!-- سكريبت العناصر المختارة -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // مصفوفة لتخزين العناصر المختارة - جعلها متاحة عالمياً
            window.selectedItems = [];

            // تهيئة زر مسح الكل
            document.getElementById('clearSelectedBtn').addEventListener('click', function() {
                window.selectedItems = [];
                window.updateSelectedItemsTable();
            });

            // وظيفة لتحديث جدول العناصر المختارة - جعلها متاحة عالمياً
            window.updateSelectedItemsTable = function() {
                const container = document.getElementById('selectedItemsContainer');

                if (selectedItems.length === 0) {
                    container.innerHTML = `
                        <div class="empty-selection-message">
                            <i class="fas fa-info-circle mb-2" style="font-size: 2rem; color: #6c757d;"></i>
                            <p>اضغط على أي عنصر من جدول جهات الاتصال أو المجموعات لإضافته هنا</p>
                        </div>
                    `;
                    return;
                }

                let tableHTML = `
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th scope="col" style="width: 50px;">#</th>
                                    <th scope="col">الاسم</th>
                                    <th scope="col">النوع</th>
                                    <th scope="col" style="width: 50px;">حذف</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                selectedItems.forEach((item, index) => {
                    tableHTML += `
                        <tr class="selected-item-row">
                            <td>${index + 1}</td>
                            <td>
                                ${item.image ? `<img src="${item.image}" alt="${item.name}" class="contact-image me-2">` :
                                `<div class="contact-image me-2 d-flex align-items-center justify-content-center bg-light">
                                    <i class="${item.type === 'contact' ? 'fas fa-user' : 'fas fa-users'}"></i>
                                </div>`}
                                ${item.name}
                            </td>
                            <td>
                                <span class="badge ${item.type === 'contact' ? 'bg-info' : 'bg-success'}">
                                    ${item.type === 'contact' ? 'جهة اتصال' : 'مجموعة'}
                                </span>
                            </td>
                            <td>
                                <i class="fas fa-times-circle remove-item-btn" data-index="${index}"></i>
                            </td>
                        </tr>
                    `;
                });

                tableHTML += `
                            </tbody>
                        </table>
                    </div>
                `;

                container.innerHTML = tableHTML;

                // إضافة مستمعي الأحداث لأزرار الحذف
                document.querySelectorAll('.remove-item-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const index = parseInt(this.getAttribute('data-index'));
                        selectedItems.splice(index, 1);
                        updateSelectedItemsTable();
                    });
                });

                // إضافة زر إرسال الرسائل إذا كان هناك عناصر مختارة
                const sendMessageBtn = document.getElementById('sendMessageBtn');
                if (selectedItems.length > 0) {
                    if (!sendMessageBtn) {
                        const btnContainer = document.createElement('div');
                        btnContainer.className = 'text-center mt-3';
                        btnContainer.innerHTML = `
                            <div class="d-grid gap-2">
                                <button id="sendMessageBtn" class="btn btn-success">
                                    <i class="fab fa-whatsapp me-2"></i>إرسال رسالة للمحددين
                                </button>
                                <button id="cleanupOperationsBtn" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-trash-alt me-2"></i>تنظيف ملفات العمليات
                                </button>
                            </div>
                        `;
                        container.appendChild(btnContainer);

                        // إضافة مستمع الحدث لزر إرسال الرسائل
                        document.getElementById('sendMessageBtn').addEventListener('click', function() {
                            // تخزين العناصر المختارة في localStorage بدلاً من تمريرها في URL
                            localStorage.setItem('selectedRecipients', JSON.stringify(window.selectedItems));

                            // --- تصحيح: الحصول على اسم الحساب كنص صريح --- START
                            const accountNameElement = document.getElementById('accountName');
                            let accountNameString = '';
                            if (accountNameElement) {
                                accountNameString = accountNameElement.textContent.trim();
                            } else {
                                // كحل بديل، حاول قراءته من معلمات URL للصفحة الحالية مرة أخرى
                                const currentUrlParams = new URLSearchParams(window.location.search);
                                accountNameString = currentUrlParams.get('account') || '';
                                console.warn('لم يتم العثور على عنصر accountName H1، تم استخدام قيمة من URL.');
                            }

                            if (!accountNameString || accountNameString === '[object HTMLHeadingElement]') {
                                console.error('اسم الحساب غير صالح قبل الانتقال:', accountNameString);
                                Swal.fire({
                                    title: 'خطأ فادح',
                                    text: 'اسم الحساب غير صالح. لا يمكن إرسال الرسائل. القيمة الحالية: ' + accountNameString,
                                    icon: 'error',
                                    confirmButtonText: 'موافق'
                                });
                                return;
                            }
                            // --- تصحيح: الحصول على اسم الحساب كنص صريح --- END

                            // الانتقال إلى صفحة إرسال الرسائل مع تمرير اسم الحساب فقط
                            console.log(`الانتقال إلى send-message.html بالمعلمات: account=${accountNameString}`);
                            window.location.href = `/send-message.html?account=${encodeURIComponent(accountNameString)}`;
                        });

                        // إضافة مستمع الحدث لتنظيف ملفات العمليات
                        document.getElementById('cleanupOperationsBtn').addEventListener('click', async function() {
                            const btn = this;
                            const originalText = btn.innerHTML;

                            // تأكيد من المستخدم
                            const result = await Swal.fire({
                                title: 'تأكيد التنظيف',
                                text: 'هل أنت متأكد من حذف جميع ملفات العمليات؟ هذا الإجراء لا يمكن التراجع عنه.',
                                icon: 'warning',
                                showCancelButton: true,
                                confirmButtonColor: '#ffc107',
                                cancelButtonColor: '#6c757d',
                                confirmButtonText: 'نعم، احذف',
                                cancelButtonText: 'إلغاء'
                            });

                            if (result.isConfirmed) {
                                try {
                                    // تغيير نص الزر أثناء التحميل
                                    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التنظيف...';
                                    btn.disabled = true;

                                    const response = await fetch('/api/cleanup-operations', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json'
                                        }
                                    });

                                    if (response.ok) {
                                        const data = await response.json();

                                        Swal.fire({
                                            title: 'تم التنظيف بنجاح',
                                            text: `تم حذف ${data.deletedCount || 0} ملف عملية`,
                                            icon: 'success',
                                            confirmButtonText: 'حسناً',
                                            confirmButtonColor: '#25D366'
                                        });

                                        console.log(`✅ تم حذف ${data.deletedCount || 0} ملف عملية`);
                                    } else {
                                        throw new Error('فشل في تنظيف ملفات العمليات');
                                    }
                                } catch (error) {
                                    console.error('خطأ في تنظيف ملفات العمليات:', error);

                                    Swal.fire({
                                        title: 'خطأ',
                                        text: 'فشل في تنظيف ملفات العمليات',
                                        icon: 'error',
                                        confirmButtonText: 'حسناً',
                                        confirmButtonColor: '#d33'
                                    });
                                } finally {
                                    // إعادة تعيين نص الزر
                                    btn.innerHTML = originalText;
                                    btn.disabled = false;
                                }
                            }
                        });
                    }
                } else {
                    // إزالة زر إرسال الرسائل إذا لم تكن هناك عناصر مختارة
                    if (sendMessageBtn) {
                        sendMessageBtn.parentElement.remove();
                    }
                }
            }

            // وظيفة لإضافة مستمعي الأحداث للصفوف القابلة للنقر
            function addClickListenersToRows() {
                // إضافة مستمعي الأحداث لصفوف جهات الاتصال
                document.querySelectorAll('.contact-row').forEach(row => {
                    if (!row.hasAttribute('data-has-click-listener')) {
                        row.setAttribute('data-has-click-listener', 'true');
                        row.classList.add('selectable-row');

                        row.addEventListener('click', function() {
                            const contactId = this.getAttribute('data-contact-id');
                            const contactName = this.getAttribute('data-contact-name');
                            const contactImage = this.querySelector('.contact-image')?.src || '';

                            // التحقق من عدم وجود العنصر مسبقًا
                            const exists = window.selectedItems.some(item =>
                                item.type === 'contact' && item.id === contactId
                            );

                            if (!exists) {
                                window.selectedItems.push({
                                    id: contactId,
                                    name: contactName,
                                    image: contactImage,
                                    type: 'contact'
                                });

                                window.updateSelectedItemsTable();

                                // إظهار إشعار
                                Swal.fire({
                                    title: 'تمت الإضافة',
                                    text: `تمت إضافة ${contactName} إلى القائمة المختارة`,
                                    icon: 'success',
                                    toast: true,
                                    position: 'top-end',
                                    showConfirmButton: false,
                                    timer: 3000
                                });
                            }
                        });
                    }
                });

                // إضافة مستمعي الأحداث لصفوف المجموعات
                document.querySelectorAll('.group-row').forEach(row => {
                    if (!row.hasAttribute('data-has-click-listener')) {
                        row.setAttribute('data-has-click-listener', 'true');
                        row.classList.add('selectable-row');

                        row.addEventListener('click', function() {
                            const groupId = this.getAttribute('data-group-id');
                            const groupName = this.getAttribute('data-group-name');

                            // التحقق من عدم وجود العنصر مسبقًا
                            const exists = window.selectedItems.some(item =>
                                item.type === 'group' && item.id === groupId
                            );

                            if (!exists) {
                                window.selectedItems.push({
                                    id: groupId,
                                    name: groupName,
                                    image: '',
                                    type: 'group'
                                });

                                window.updateSelectedItemsTable();

                                // إظهار إشعار
                                Swal.fire({
                                    title: 'تمت الإضافة',
                                    text: `تمت إضافة مجموعة ${groupName} إلى القائمة المختارة`,
                                    icon: 'success',
                                    toast: true,
                                    position: 'top-end',
                                    showConfirmButton: false,
                                    timer: 3000
                                });
                            }
                        });
                    }
                });
            }

            // مراقبة التغييرات في DOM لإضافة مستمعي الأحداث للصفوف الجديدة
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        addClickListenersToRows();
                    }
                });
            });

            // بدء مراقبة التغييرات في حاويات الجداول
            observer.observe(document.getElementById('contactsContainer'), { childList: true, subtree: true });

            // إذا كان هناك حاوية لجهات الاتصال في المجموعات
            const groupContactsContainer = document.getElementById('groupContactsContainer');
            if (groupContactsContainer) {
                observer.observe(groupContactsContainer, { childList: true, subtree: true });
            }

            // إضافة مستمعي الأحداث لأزرار اختيار الكل
            document.addEventListener('click', function(event) {
                // زر اختيار كل جهات الاتصال
                if (event.target.closest('#selectAllContactsBtn')) {
                    // الحصول على جميع الصفوف المرئية في جدول جهات الاتصال
                    const visibleRows = Array.from(document.querySelectorAll('#contactsContainer tbody tr'))
                        .filter(row => row.style.display !== 'none');

                    if (visibleRows.length === 0) {
                        Swal.fire({
                            title: 'تنبيه',
                            text: 'لا توجد جهات اتصال لإضافتها',
                            icon: 'warning',
                            confirmButtonText: 'حسنًا',
                            confirmButtonColor: '#25D366'
                        });
                        return;
                    }

                    let addedCount = 0;

                    // إضافة كل صف مرئي إلى العناصر المختارة
                    visibleRows.forEach(row => {
                        const contactId = row.getAttribute('data-id');
                        const contactNumber = row.getAttribute('data-number');

                        // استخراج اسم جهة الاتصال من الخلية الثانية
                        const nameCell = row.querySelector('td:nth-child(2)');
                        const nameElement = nameCell.querySelector('.contact-name') || nameCell.querySelector('div > div');
                        const contactName = nameElement ? nameElement.textContent.trim() :
                                          (nameCell.textContent.trim() || 'جهة اتصال');

                        const contactImage = row.querySelector('.contact-image')?.src || '';

                        // التحقق من عدم وجود العنصر مسبقًا
                        const exists = window.selectedItems.some(item =>
                            item.type === 'contact' && (item.id === contactId || item.number === contactNumber)
                        );

                        if (!exists) {
                            window.selectedItems.push({
                                id: contactId || '',
                                name: contactName,
                                number: contactNumber,
                                image: contactImage,
                                type: 'contact'
                            });
                            addedCount++;

                            // إضافة صنف "selected-row" لتمييز الصف المحدد
                            row.classList.add('selected-row');
                        }
                    });

                    // تحديث جدول العناصر المختارة
                    window.updateSelectedItemsTable();

                    // إظهار إشعار
                    if (addedCount > 0) {
                        Swal.fire({
                            title: 'تمت الإضافة',
                            text: `تمت إضافة ${addedCount} جهة اتصال إلى القائمة المختارة`,
                            icon: 'success',
                            confirmButtonText: 'حسنًا',
                            confirmButtonColor: '#25D366'
                        });
                    } else {
                        Swal.fire({
                            title: 'تنبيه',
                            text: 'جميع جهات الاتصال المعروضة مضافة بالفعل',
                            icon: 'info',
                            confirmButtonText: 'حسنًا',
                            confirmButtonColor: '#25D366'
                        });
                    }
                }

                // زر اختيار كل المجموعات
                if (event.target.closest('#selectAllGroupsBtn')) {
                    // الحصول على جميع الصفوف المرئية في جدول المجموعات
                    const visibleRows = Array.from(document.querySelectorAll('#groupContactsContainer tbody tr'))
                        .filter(row => row.style.display !== 'none');

                    if (visibleRows.length === 0) {
                        Swal.fire({
                            title: 'تنبيه',
                            text: 'لا توجد مجموعات لإضافتها',
                            icon: 'warning',
                            confirmButtonText: 'حسنًا',
                            confirmButtonColor: '#25D366'
                        });
                        return;
                    }

                    let addedCount = 0;

                    // إضافة كل صف مرئي إلى العناصر المختارة
                    visibleRows.forEach(row => {
                        const groupId = row.getAttribute('data-group-id');
                        const groupName = row.getAttribute('data-group-name');

                        // التحقق من عدم وجود العنصر مسبقًا
                        const exists = window.selectedItems.some(item =>
                            item.type === 'group' && item.id === groupId
                        );

                        if (!exists) {
                            window.selectedItems.push({
                                id: groupId,
                                name: groupName,
                                image: '',
                                type: 'group'
                            });
                            addedCount++;
                        }
                    });

                    // تحديث جدول العناصر المختارة
                    window.updateSelectedItemsTable();

                    // إظهار إشعار
                    if (addedCount > 0) {
                        Swal.fire({
                            title: 'تمت الإضافة',
                            text: `تمت إضافة ${addedCount} مجموعة إلى القائمة المختارة`,
                            icon: 'success',
                            confirmButtonText: 'حسنًا',
                            confirmButtonColor: '#25D366'
                        });
                    } else {
                        Swal.fire({
                            title: 'تنبيه',
                            text: 'جميع المجموعات المعروضة مضافة بالفعل',
                            icon: 'info',
                            confirmButtonText: 'حسنًا',
                            confirmButtonColor: '#25D366'
                        });
                    }
                }
            });

            // إضافة مستمع الحدث لزر الرد التلقائي
            document.getElementById('autoReplyBtn').addEventListener('click', function() {
                const urlParams = new URLSearchParams(window.location.search);
                const accountName = urlParams.get('account');
                if (accountName) {
                    window.location.href = `auto-reply-account-new.html?account=${encodeURIComponent(accountName)}`;
                } else {
                    Swal.fire({
                        title: 'خطأ',
                        text: 'لم يتم العثور على اسم الحساب',
                        icon: 'error'
                    });
                }
            });

            // تهيئة جدول العناصر المختارة
            window.updateSelectedItemsTable();
        });
    </script>
</body>
</html>
