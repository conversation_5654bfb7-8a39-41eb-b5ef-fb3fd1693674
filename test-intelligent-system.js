// اختبار النظام الدلالي المتقدم باستخدام Vector Embeddings
const AdvancedSemanticAI = require('./advanced-semantic-ai');

// محاكاة بيانات الرد التلقائي مع أمثلة واقعية
const mockAdvancedSettings = {
    enabled: true,
    rules: [
        // قواعد العضوية والاشتراكات
        {
            id: 'membership-info',
            code: 'عضوية',
            enabled: true,
            messages: {
                text: 'معلومات العضوية: نوفر عضويات شهرية وسنوية مع خصومات مميزة. العضوية الشهرية 100 ريال والسنوية 1000 ريال.',
                image: '',
                file: '',
                folder: []
            }
        },
        {
            id: 'membership-schedule',
            code: 'مواعيد_عضوية',
            enabled: true,
            messages: {
                text: 'مواعيد استحقاق العضوية: يتم تجديد العضوية الشهرية كل 30 يوم، والسنوية كل 365 يوم. سنرسل تذكير قبل انتهاء العضوية بـ 7 أيام.',
                image: '',
                file: '',
                folder: []
            }
        },
        
        // قواعد التدريب والرياضة
        {
            id: 'training-schedule',
            code: 'تدريب',
            enabled: true,
            messages: {
                text: 'جدول التدريبات: الصباح من 6-10، المساء من 4-10. تدريبات القوة: السبت والثلاثاء والخميس. الكارديو: الأحد والاثنين والأربعاء.',
                image: '',
                file: '',
                folder: []
            }
        },
        {
            id: 'training-booking',
            code: 'حجز_تدريب',
            enabled: true,
            messages: {
                text: 'حجز التدريبات: يمكنك حجز جلسة تدريب شخصية مع المدرب. الجلسة الواحدة 150 ريال لمدة ساعة. احجز مسبقاً بـ 24 ساعة.',
                image: '',
                file: '',
                folder: []
            }
        },
        
        // قواعد الأسعار والخدمات
        {
            id: 'pricing-info',
            code: 'أسعار',
            enabled: true,
            messages: {
                text: 'قائمة الأسعار: العضوية الشهرية 100 ريال، السنوية 1000 ريال، التدريب الشخصي 150 ريال/ساعة، استشارة التغذية 200 ريال.',
                image: '',
                file: '',
                folder: []
            }
        },
        {
            id: 'services-info',
            code: 'خدمات',
            enabled: true,
            messages: {
                text: 'خدماتنا تشمل: صالة ألعاب رياضية مجهزة، تدريب شخصي، استشارات تغذية، برامج لياقة متخصصة، ساونا وجاكوزي.',
                image: '',
                file: '',
                folder: []
            }
        },
        
        // قواعد الحجز والإلغاء
        {
            id: 'booking-info',
            code: 'حجز',
            enabled: true,
            messages: {
                text: 'نظام الحجز: احجز موعدك عبر التطبيق أو الهاتف. يمكن الحجز حتى 7 أيام مقدماً. تأكيد الحجز خلال ساعة من الطلب.',
                image: '',
                file: '',
                folder: []
            }
        },
        {
            id: 'cancellation-info',
            code: 'إلغاء',
            enabled: true,
            messages: {
                text: 'سياسة الإلغاء: يمكن إلغاء الحجز مجاناً قبل 24 ساعة. الإلغاء المتأخر يخضع لرسوم 50% من قيمة الخدمة.',
                image: '',
                file: '',
                folder: []
            }
        }
    ]
};

// إنشاء معالج النظام الدلالي المتقدم
const semanticAI = new AdvancedSemanticAI();

// وظيفة تحديد نوع الرسالة
function isMessageNumberOrEmoji(message) {
    const isNumber = /^[\d٠-٩]+$/.test(message.trim());
    const emojiRegex = /^[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]+$/u;
    const isEmoji = emojiRegex.test(message.trim());
    return isNumber || isEmoji;
}

// وظيفة التحليل الدلالي المتقدم
function calculateAdvancedSemanticSimilarity(messageText, responseText) {
    try {
        // استخدام النظام الدلالي المتقدم
        const similarity = semanticAI.calculateSemanticSimilarity(messageText, responseText);

        return similarity;
    } catch (error) {
        console.error('❌ خطأ في التحليل الدلالي:', error);
        return 0;
    }
}

// محاكاة وظيفة معالجة الرد التلقائي المتقدم
async function processIntelligentAutoReply(messageBody, advancedSettings) {
    try {
        console.log(`🔍 معالجة الرد التلقائي الذكي للرسالة: "${messageBody}"`);

        const isNumberOrEmoji = isMessageNumberOrEmoji(messageBody);

        if (isNumberOrEmoji) {
            console.log(`🔢 رسالة رقم أو إيموجي - البحث في عمود الكود بتطابق 100%`);
            const normalizedMessage = modernTextProcessor.convertArabicNumbers(messageBody);
            console.log(`🔄 الأصلي: "${messageBody}" → المحول: "${normalizedMessage}"`);

            const exactCodeMatch = advancedSettings.rules.find(rule => {
                if (!rule.enabled || !rule.code) return false;
                return rule.code === messageBody || rule.code === normalizedMessage;
            });

            if (exactCodeMatch) {
                console.log(`🎯 تم العثور على تطابق دقيق في عمود الكود: "${exactCodeMatch.code}"`);
                return { success: true, rule: exactCodeMatch, type: 'exact_code' };
            }

            console.log(`❌ لم يتم العثور على تطابق في عمود الكود للرسالة: "${messageBody}"`);
            return { success: false, reason: 'no_code_match' };

        } else {
            console.log(`💬 رسالة نصية - البحث في عمود الرسالة النصية باستخدام النظام الذكي`);

            const textRules = advancedSettings.rules.filter(rule =>
                rule.enabled && rule.messages && rule.messages.text && rule.messages.text.trim()
            );

            if (textRules.length === 0) {
                console.log(`❌ لا توجد قواعد نصية متاحة للمطابقة`);
                return { success: false, reason: 'no_text_rules' };
            }

            console.log(`🧠 استخدام النظام الذكي لفهم المعنى...`);
            console.log(`🔍 فحص ${textRules.length} قاعدة نصية للعثور على أفضل تطابق...`);

            let bestMatch = null;
            let bestScore = 0;

            for (const rule of textRules) {
                console.log(`\n🔍 فحص القاعدة "${rule.code}"`);
                const similarity = calculateAdvancedSemanticSimilarity(messageBody, rule.messages.text);

                console.log(`📊 القاعدة "${rule.code}": ${(similarity * 100).toFixed(2)}% - "${rule.messages.text.substring(0, 50)}..."`);

                if (similarity > bestScore) {
                    bestScore = similarity;
                    bestMatch = rule;
                    console.log(`🆕 مرشح جديد للفوز: "${rule.code}" بنسبة ${(similarity * 100).toFixed(2)}%`);
                }
            }

            if (bestMatch && bestScore > 0) {
                console.log(`🏆 الفائز: "${bestMatch.code}" بنسبة ${(bestScore * 100).toFixed(2)}%`);
                return { success: true, rule: bestMatch, type: 'intelligent_match', score: bestScore };
            }

            console.log(`❌ لم يتم العثور على تطابق مناسب`);
            return { success: false, reason: 'no_intelligent_match' };
        }

    } catch (error) {
        console.error('خطأ في معالجة الرد التلقائي الذكي:', error);
        return { success: false, reason: 'error', error: error.message };
    }
}

// اختبار النظام الذكي
async function testIntelligentSystem() {
    console.log('🧪 اختبار النظام الذكي المتقدم لفهم المعنى الحقيقي\n');

    const testMessages = [
        // اختبارات العضوية والاشتراكات
        { message: 'متى تنتهي عضويتي؟', expected: 'membership-schedule', description: 'سؤال عن انتهاء العضوية' },
        { message: 'أريد معلومات عن الاشتراك', expected: 'membership-info', description: 'طلب معلومات العضوية' },
        { message: 'كم باقي على استحقاق العضوية؟', expected: 'membership-schedule', description: 'سؤال عن موعد استحقاق العضوية' },
        
        // اختبارات التدريب والرياضة
        { message: 'ما هي مواعيد التدريب؟', expected: 'training-schedule', description: 'سؤال عن مواعيد التدريب' },
        { message: 'أريد حجز جلسة تدريب شخصية', expected: 'training-booking', description: 'طلب حجز تدريب' },
        { message: 'متى تفتح الصالة الرياضية؟', expected: 'training-schedule', description: 'سؤال عن أوقات الصالة' },
        
        // اختبارات الأسعار والخدمات
        { message: 'كم سعر العضوية الشهرية؟', expected: 'pricing-info', description: 'سؤال عن أسعار العضوية' },
        { message: 'ما هي الخدمات المتوفرة؟', expected: 'services-info', description: 'سؤال عن الخدمات' },
        { message: 'كم تكلفة التدريب الشخصي؟', expected: 'pricing-info', description: 'سؤال عن سعر التدريب' },
        
        // اختبارات الحجز والإلغاء
        { message: 'كيف أحجز موعد؟', expected: 'booking-info', description: 'سؤال عن طريقة الحجز' },
        { message: 'أريد إلغاء حجزي', expected: 'cancellation-info', description: 'طلب إلغاء حجز' },
        { message: 'هل يمكن تغيير موعد الحجز؟', expected: 'cancellation-info', description: 'سؤال عن تغيير الحجز' },
        
        // اختبارات خاطئة (يجب أن تفشل)
        { message: 'أريد طلب بيتزا', expected: 'no_match', description: 'طلب غير متعلق بالنادي' },
        { message: 'ما حالة الطقس اليوم؟', expected: 'no_match', description: 'سؤال غير متعلق' }
    ];

    let totalTests = testMessages.length;
    let passedTests = 0;

    for (let i = 0; i < testMessages.length; i++) {
        const test = testMessages[i];
        console.log(`\n📨 اختبار ${i + 1}/${totalTests}: ${test.description}`);
        console.log(`   الرسالة: "${test.message}"`);
        console.log(`   المتوقع: ${test.expected}`);

        const result = await processIntelligentAutoReply(test.message, mockAdvancedSettings);

        console.log(`   النتيجة: ${result.success ? 'نجح' : 'فشل'}`);
        
        if (result.success) {
            console.log(`   القاعدة المطابقة: "${result.rule.id}"`);
            console.log(`   نوع التطابق: ${result.type}`);
            if (result.score) {
                console.log(`   نسبة التطابق: ${(result.score * 100).toFixed(2)}%`);
            }
            console.log(`   الرد: "${result.rule.messages.text.substring(0, 100)}..."`);
        } else {
            console.log(`   سبب الفشل: ${result.reason}`);
        }

        // تقييم النتيجة
        let testPassed = false;
        if (test.expected === 'no_match' && !result.success) {
            testPassed = true;
        } else if (result.success && result.rule.id === test.expected) {
            testPassed = true;
        }

        if (testPassed) {
            console.log(`   ✅ الاختبار نجح`);
            passedTests++;
        } else {
            console.log(`   ❌ الاختبار فشل`);
        }
    }

    console.log(`\n📊 ملخص النتائج:`);
    console.log(`   إجمالي الاختبارات: ${totalTests}`);
    console.log(`   الاختبارات الناجحة: ${passedTests}`);
    console.log(`   الاختبارات الفاشلة: ${totalTests - passedTests}`);
    console.log(`   نسبة النجاح: ${((passedTests / totalTests) * 100).toFixed(2)}%`);

    if (passedTests === totalTests) {
        console.log(`\n🎉 جميع الاختبارات نجحت! النظام الذكي جاهز للاستخدام.`);
    } else {
        console.log(`\n⚠️ بعض الاختبارات فشلت. النظام يحتاج إلى تحسينات.`);
    }
}

// تشغيل الاختبار
testIntelligentSystem().catch(console.error);
