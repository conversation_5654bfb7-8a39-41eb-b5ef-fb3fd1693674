document.addEventListener('DOMContentLoaded', function() {
    // الحصول على اسم الحساب من URL
    const urlParams = new URLSearchParams(window.location.search);
    const accountName = urlParams.get('account');

    if (!accountName) {
        window.location.href = 'dashboard.html';
        return;
    }

    // العناصر الأساسية
    const backBtn = document.getElementById('backBtn');
    const accountNameElement = document.getElementById('accountName');
    const accountPhone = document.getElementById('accountPhone');
    const accountStatus = document.getElementById('accountStatus');
    const accountAvatar = document.getElementById('accountAvatar');
    const accountAutoReplyToggle = document.getElementById('accountAutoReplyToggle');
    const statusMessage = document.getElementById('statusMessage');
    const saveBtn = document.getElementById('saveBtn');
    const testBtn = document.getElementById('testBtn');

    // عناصر الرسائل
    const enableText = document.getElementById('enableText');
    const enableImage = document.getElementById('enableImage');
    const enableFile = document.getElementById('enableFile');
    const enableFolder = document.getElementById('enableFolder');

    const textSection = document.getElementById('textSection');
    const imageSection = document.getElementById('imageSection');
    const fileSection = document.getElementById('fileSection');
    const folderSection = document.getElementById('folderSection');

    const messageText = document.getElementById('messageText');
    const imageInput = document.getElementById('imageInput');
    const fileInput = document.getElementById('fileInput');
    const folderInput = document.getElementById('folderInput');

    const imagePreview = document.getElementById('imagePreview');
    const filePreview = document.getElementById('filePreview');
    const folderPreview = document.getElementById('folderPreview');
    const fullPreview = document.getElementById('fullPreview');
    const previewContent = document.getElementById('previewContent');

    // أدوات النص
    const insertNameBtn = document.getElementById('insertNameBtn');
    const emojiBtn = document.getElementById('emojiBtn');
    const emojiPicker = document.getElementById('emojiPicker');

    // بيانات الملفات المحددة
    let selectedFiles = {
        image: null,
        file: null,
        folder: []
    };

    // تحميل معلومات الحساب والإعدادات
    loadAccountInfo();
    loadCurrentSettings();

    // مستمعي الأحداث الأساسية
    backBtn.addEventListener('click', () => {
        window.location.href = `account-details.html?account=${encodeURIComponent(accountName)}`;
    });

    accountAutoReplyToggle.addEventListener('change', updateToggleStatus);
    saveBtn.addEventListener('click', saveSettings);
    testBtn.addEventListener('click', testMessage);

    // مستمعي أحداث التفعيل/الإلغاء
    enableText.addEventListener('change', () => toggleSection('text'));
    enableImage.addEventListener('change', () => toggleSection('image'));
    enableFile.addEventListener('change', () => toggleSection('file'));
    enableFolder.addEventListener('change', () => toggleSection('folder'));

    // مستمعي أحداث الملفات
    imageInput.addEventListener('change', handleImageSelect);
    fileInput.addEventListener('change', handleFileSelect);
    folderInput.addEventListener('change', handleFolderSelect);

    // مستمعي أحداث النص
    messageText.addEventListener('input', updatePreview);
    insertNameBtn.addEventListener('click', insertName);
    emojiBtn.addEventListener('click', toggleEmojiPicker);

    // تحميل معلومات الحساب
    function loadAccountInfo() {
        fetch(`/api/account-details/${encodeURIComponent(accountName)}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    console.error('Error loading account info:', data.error);
                    return;
                }
                
                accountNameElement.textContent = data.name || accountName;
                accountPhone.textContent = data.number || 'غير متوفر';
                
                const profilePicture = data.profilePictureUrl || 
                    'https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png';
                accountAvatar.src = profilePicture;
                
                if (data.connected) {
                    accountStatus.textContent = 'متصل';
                    accountStatus.className = 'account-status status-connected';
                } else {
                    accountStatus.textContent = 'غير متصل';
                    accountStatus.className = 'account-status status-disconnected';
                }
            })
            .catch(error => {
                console.error('Error loading account info:', error);
                accountNameElement.textContent = accountName;
                accountPhone.textContent = 'غير متوفر';
                accountStatus.textContent = 'غير متصل';
                accountStatus.className = 'account-status status-disconnected';
            });
    }

    // تحميل الإعدادات الحالية
    function loadCurrentSettings() {
        fetch(`/api/auto-reply/account/${encodeURIComponent(accountName)}/settings`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.settings) {
                    const settings = data.settings;
                    accountAutoReplyToggle.checked = settings.enabled || false;
                    
                    // تحميل النص
                    if (settings.message && settings.message.text) {
                        enableText.checked = true;
                        messageText.value = settings.message.text;
                    }
                    
                    // تحميل الصورة
                    if (settings.message && settings.message.imagePath) {
                        enableImage.checked = true;
                        loadImagePreview(settings.message.imagePath);
                    }
                    
                    // تحميل الملف
                    if (settings.message && settings.message.filePath) {
                        enableFile.checked = true;
                        loadFilePreview(settings.message.filePath);
                    }
                    
                    // تحميل ملفات المجلد
                    if (settings.message && settings.message.folderFiles && settings.message.folderFiles.length > 0) {
                        enableFolder.checked = true;
                        loadFolderPreview(settings.message.folderFiles);
                    }
                    
                    // تحديث الواجهة
                    updateAllSections();
                    updateToggleStatus();
                    updatePreview();
                }
            })
            .catch(error => {
                console.error('Error loading settings:', error);
            });
    }

    // تبديل حالة القسم
    function toggleSection(type) {
        const section = document.getElementById(`${type}Section`);
        const enableCheckbox = document.getElementById(`enable${type.charAt(0).toUpperCase() + type.slice(1)}`);
        
        if (enableCheckbox.checked) {
            section.classList.add('active');
        } else {
            section.classList.remove('active');
            // مسح البيانات عند الإلغاء
            if (type === 'text') {
                messageText.value = '';
            } else if (type === 'image') {
                selectedFiles.image = null;
                imagePreview.innerHTML = '';
                imagePreview.style.display = 'none';
                imageInput.value = '';
            } else if (type === 'file') {
                selectedFiles.file = null;
                filePreview.innerHTML = '';
                filePreview.style.display = 'none';
                fileInput.value = '';
            } else if (type === 'folder') {
                selectedFiles.folder = [];
                folderPreview.innerHTML = '';
                folderPreview.style.display = 'none';
                folderInput.value = '';
            }
        }
        updatePreview();
    }

    // تحديث جميع الأقسام
    function updateAllSections() {
        toggleSection('text');
        toggleSection('image');
        toggleSection('file');
        toggleSection('folder');
    }

    // تحديث حالة التبديل
    function updateToggleStatus() {
        if (accountAutoReplyToggle.checked) {
            statusMessage.className = 'alert alert-success';
            statusMessage.innerHTML = '<i class="fas fa-check-circle me-2"></i>الرد التلقائي للحساب مفعل حالياً';
        } else {
            statusMessage.className = 'alert alert-info';
            statusMessage.innerHTML = '<i class="fas fa-info-circle me-2"></i>الرد التلقائي للحساب غير مفعل حالياً';
        }
    }

    // معالجة اختيار الصورة
    function handleImageSelect(event) {
        const file = event.target.files[0];
        if (file) {
            selectedFiles.image = file;
            const reader = new FileReader();
            reader.onload = function(e) {
                showImagePreview(e.target.result, file.name);
            };
            reader.readAsDataURL(file);
        }
        updatePreview();
    }

    // معالجة اختيار الملف
    function handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            selectedFiles.file = file;
            showFilePreview(file.name);
        }
        updatePreview();
    }

    // معالجة اختيار ملفات المجلد
    function handleFolderSelect(event) {
        const files = Array.from(event.target.files);
        if (files.length > 0) {
            selectedFiles.folder = files;
            showFolderPreview(files);
        }
        updatePreview();
    }

    // عرض معاينة الصورة
    function showImagePreview(src, name) {
        imagePreview.innerHTML = `
            <div class="preview-item">
                <img src="${src}" alt="${name}" class="preview-image">
                <div class="file-info">
                    <span>${name}</span>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeImage()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        imagePreview.style.display = 'block';
    }

    // عرض معاينة الملف
    function showFilePreview(name) {
        filePreview.innerHTML = `
            <div class="preview-item">
                <div class="file-info">
                    <i class="fas fa-file"></i>
                    <span>${name}</span>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        filePreview.style.display = 'block';
    }

    // عرض معاينة ملفات المجلد
    function showFolderPreview(files) {
        let html = '<div class="folder-files-list">';
        files.forEach((file, index) => {
            html += `
                <div class="file-item">
                    <div class="file-info">
                        <i class="fas fa-file"></i>
                        <span>${file.name}</span>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFolderFile(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        });
        html += `
            <div class="text-center mt-2">
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearFolder()">
                    <i class="fas fa-trash"></i> حذف جميع الملفات
                </button>
            </div>
        </div>`;
        folderPreview.innerHTML = html;
        folderPreview.style.display = 'block';
    }

    // تحميل معاينة الصورة المحفوظة
    function loadImagePreview(imagePath) {
        if (imagePath) {
            selectedFiles.image = { path: imagePath };
            const fileName = imagePath.split('/').pop();
            const imageUrl = `/auto-reply-files/${fileName}`;
            imagePreview.innerHTML = `
                <div class="preview-item">
                    <img src="${imageUrl}" alt="${fileName}" class="preview-image"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="file-info" style="display: none; flex-direction: column; align-items: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                        <i class="fas fa-image fa-2x text-muted mb-2"></i>
                        <span>صورة محفوظة</span>
                        <small class="text-muted">${fileName}</small>
                    </div>
                    <div class="file-info">
                        <span>${fileName}</span>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeImage()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            imagePreview.style.display = 'block';
        }
    }

    // تحميل معاينة الملف المحفوظ
    function loadFilePreview(filePath) {
        if (filePath) {
            const fileName = filePath.split('/').pop();
            selectedFiles.file = { path: filePath, name: fileName };
            filePreview.innerHTML = `
                <div class="preview-item">
                    <div class="file-info">
                        <i class="fas fa-file"></i>
                        <span>${fileName}</span>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            filePreview.style.display = 'block';
        }
    }

    // تحميل معاينة ملفات المجلد المحفوظة
    function loadFolderPreview(folderFiles) {
        if (folderFiles && folderFiles.length > 0) {
            selectedFiles.folder = folderFiles;
            let html = '<div class="folder-files-list">';
            folderFiles.forEach((file, index) => {
                const fileName = file.split('/').pop();
                html += `
                    <div class="file-item">
                        <div class="file-info">
                            <i class="fas fa-file"></i>
                            <span>${fileName}</span>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFolderFile(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
            });
            html += `
                <div class="text-center mt-2">
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearFolder()">
                        <i class="fas fa-trash"></i> حذف جميع الملفات
                    </button>
                </div>
            </div>`;
            folderPreview.innerHTML = html;
            folderPreview.style.display = 'block';
        }
    }

    // تحديث المعاينة الكاملة
    function updatePreview() {
        let hasContent = false;
        let previewHTML = '';

        // معاينة النص
        if (enableText.checked && messageText.value.trim()) {
            hasContent = true;
            previewHTML += `
                <div class="mb-3">
                    <h6><i class="fas fa-font me-2"></i>النص:</h6>
                    <div class="p-3 bg-light rounded">${messageText.value}</div>
                </div>
            `;
        }

        // معاينة الصورة
        if (enableImage.checked && selectedFiles.image) {
            hasContent = true;
            const imageSrc = selectedFiles.image.path || (selectedFiles.image instanceof File ? URL.createObjectURL(selectedFiles.image) : '');
            previewHTML += `
                <div class="mb-3">
                    <h6><i class="fas fa-image me-2"></i>الصورة:</h6>
                    <img src="${imageSrc}" alt="صورة الرد" style="max-width: 200px; max-height: 200px; border-radius: 8px;">
                </div>
            `;
        }

        // معاينة الملف
        if (enableFile.checked && selectedFiles.file) {
            hasContent = true;
            const fileName = selectedFiles.file.name || selectedFiles.file.path?.split('/').pop() || 'ملف';
            previewHTML += `
                <div class="mb-3">
                    <h6><i class="fas fa-file me-2"></i>الملف:</h6>
                    <div class="p-2 bg-light rounded d-inline-block">
                        <i class="fas fa-file me-2"></i>${fileName}
                    </div>
                </div>
            `;
        }

        // معاينة ملفات المجلد
        if (enableFolder.checked && selectedFiles.folder && selectedFiles.folder.length > 0) {
            hasContent = true;
            previewHTML += `
                <div class="mb-3">
                    <h6><i class="fas fa-folder me-2"></i>الملفات المتعددة (${selectedFiles.folder.length} ملف):</h6>
                    <div class="p-2 bg-light rounded">
            `;
            selectedFiles.folder.forEach(file => {
                const fileName = file.name || file.split('/').pop();
                previewHTML += `<div><i class="fas fa-file me-2"></i>${fileName}</div>`;
            });
            previewHTML += '</div></div>';
        }

        if (hasContent) {
            // إضافة تنبيه حالة الرد التلقائي
            const statusAlert = accountAutoReplyToggle.checked ?
                '<div class="alert alert-success mt-3"><i class="fas fa-check-circle me-2"></i>الرد التلقائي مفعل - سيتم إرسال هذه الرسالة تلقائياً</div>' :
                '<div class="alert alert-warning mt-3"><i class="fas fa-exclamation-triangle me-2"></i>الرد التلقائي غير مفعل - لن يتم إرسال هذه الرسالة</div>';

            previewContent.innerHTML = previewHTML + statusAlert;
            fullPreview.style.display = 'block';
        } else {
            fullPreview.style.display = 'none';
        }
    }

    // إدراج اسم المرسل
    function insertName() {
        const cursorPos = messageText.selectionStart;
        const textBefore = messageText.value.substring(0, cursorPos);
        const textAfter = messageText.value.substring(cursorPos);
        messageText.value = textBefore + '{name}' + textAfter;
        messageText.focus();
        messageText.setSelectionRange(cursorPos + 6, cursorPos + 6);
        updatePreview();
    }

    // تبديل منتقي الرموز التعبيرية
    function toggleEmojiPicker() {
        if (emojiPicker.classList.contains('show')) {
            emojiPicker.classList.remove('show');
        } else {
            if (!emojiPicker.innerHTML) {
                emojiPicker.innerHTML = '<emoji-picker></emoji-picker>';
                const picker = emojiPicker.querySelector('emoji-picker');
                picker.addEventListener('emoji-click', (event) => {
                    const emoji = event.detail.emoji.unicode;
                    const cursorPos = messageText.selectionStart;
                    const textBefore = messageText.value.substring(0, cursorPos);
                    const textAfter = messageText.value.substring(cursorPos);
                    messageText.value = textBefore + emoji + textAfter;
                    messageText.focus();
                    messageText.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);
                    updatePreview();
                });
            }
            emojiPicker.classList.add('show');
        }
    }

    // حفظ الإعدادات
    function saveSettings() {
        // إعداد البيانات الأساسية
        const settings = {
            enabled: accountAutoReplyToggle.checked,
            message: {}
        };

        // إذا كان الرد التلقائي مفعلاً، يجب التحقق من وجود محتوى
        if (accountAutoReplyToggle.checked) {
            const hasText = enableText.checked && messageText.value.trim();
            const hasImage = enableImage.checked && selectedFiles.image;
            const hasFile = enableFile.checked && selectedFiles.file;
            const hasFolder = enableFolder.checked && selectedFiles.folder && selectedFiles.folder.length > 0;

            if (!hasText && !hasImage && !hasFile && !hasFolder) {
                Swal.fire({
                    title: 'تنبيه',
                    text: 'عند تفعيل الرد التلقائي، يجب إضافة نوع واحد على الأقل من الرسائل',
                    icon: 'warning'
                });
                return;
            }
        }

        // حفظ الرسائل المفعلة (سواء كان الرد مفعل أم لا)
        // إضافة النص
        if (enableText.checked && messageText.value.trim()) {
            settings.message.text = messageText.value.trim();
        }

        // إعداد FormData للملفات
        const formData = new FormData();

        // إضافة الصورة
        if (enableImage.checked && selectedFiles.image instanceof File) {
            formData.append('image', selectedFiles.image);
        }

        // إضافة الملف
        if (enableFile.checked && selectedFiles.file instanceof File) {
            formData.append('file', selectedFiles.file);
        }

        // إضافة ملفات المجلد
        if (enableFolder.checked && selectedFiles.folder.length > 0) {
            selectedFiles.folder.forEach((file, index) => {
                if (file instanceof File) {
                    formData.append(`folder_${index}`, file);
                }
            });
            settings.message.folderCount = selectedFiles.folder.filter(f => f instanceof File).length;
        }

        formData.append('settings', JSON.stringify(settings));

        // إرسال البيانات
        Swal.fire({
            title: 'جاري الحفظ...',
            text: 'يتم حفظ إعدادات الرد التلقائي',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        fetch(`/api/auto-reply/account/${encodeURIComponent(accountName)}/save`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const statusText = accountAutoReplyToggle.checked ?
                    'تم حفظ إعدادات الرد التلقائي وتم تفعيل الرد للحساب' :
                    'تم حفظ إعدادات الرد التلقائي (الرد غير مفعل حالياً)';

                Swal.fire({
                    title: 'تم الحفظ',
                    text: statusText,
                    icon: 'success'
                });
                // إعادة تحميل الإعدادات لعرض المسارات المحفوظة
                setTimeout(() => {
                    loadCurrentSettings();
                }, 1000);
            } else {
                Swal.fire({
                    title: 'خطأ',
                    text: data.error || 'حدث خطأ أثناء حفظ الإعدادات',
                    icon: 'error'
                });
            }
        })
        .catch(error => {
            console.error('Error saving settings:', error);
            Swal.fire({
                title: 'خطأ',
                text: 'حدث خطأ أثناء حفظ الإعدادات',
                icon: 'error'
            });
        });
    }

    // اختبار الرسالة
    function testMessage() {
        updatePreview();
        if (fullPreview.style.display === 'none') {
            Swal.fire({
                title: 'لا يوجد محتوى',
                text: 'لا يوجد محتوى لاختباره',
                icon: 'info'
            });
            return;
        }

        const statusNote = accountAutoReplyToggle.checked ?
            '<div class="alert alert-success mt-3"><i class="fas fa-check-circle me-2"></i>الرد التلقائي مفعل - سيتم إرسال هذه الرسالة تلقائياً</div>' :
            '<div class="alert alert-warning mt-3"><i class="fas fa-exclamation-triangle me-2"></i>الرد التلقائي غير مفعل - لن يتم إرسال هذه الرسالة</div>';

        Swal.fire({
            title: 'معاينة الرسالة',
            html: previewContent.innerHTML + statusNote,
            icon: 'info',
            confirmButtonText: 'موافق',
            width: '600px'
        });
    }

    // وظائف الحذف - يجب أن تكون متاحة عالمياً
    window.removeImage = function() {
        selectedFiles.image = null;
        imagePreview.innerHTML = '';
        imagePreview.style.display = 'none';
        imageInput.value = '';
        updatePreview();
    };

    window.removeFile = function() {
        selectedFiles.file = null;
        filePreview.innerHTML = '';
        filePreview.style.display = 'none';
        fileInput.value = '';
        updatePreview();
    };

    window.removeFolderFile = function(index) {
        if (selectedFiles.folder && selectedFiles.folder.length > index) {
            selectedFiles.folder.splice(index, 1);
            if (selectedFiles.folder.length === 0) {
                clearFolder();
            } else {
                showFolderPreview(selectedFiles.folder);
                updatePreview();
            }
        }
    };

    window.clearFolder = function() {
        selectedFiles.folder = [];
        folderPreview.innerHTML = '';
        folderPreview.style.display = 'none';
        folderInput.value = '';
        updatePreview();
    };

    // وظيفة حذف جميع الإعدادات
    window.deleteAllSettings = function() {
        Swal.fire({
            title: 'تأكيد الحذف',
            text: 'هل أنت متأكد من حذف جميع إعدادات الرد التلقائي لهذا الحساب؟',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، احذف',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(`/api/auto-reply/account/${encodeURIComponent(accountName)}/delete`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إعادة تعيين جميع القيم
                        accountAutoReplyToggle.checked = false;
                        enableText.checked = true;
                        enableImage.checked = false;
                        enableFile.checked = false;
                        enableFolder.checked = false;

                        messageText.value = '';
                        selectedFiles = { image: null, file: null, folder: [] };

                        imagePreview.innerHTML = '';
                        filePreview.innerHTML = '';
                        folderPreview.innerHTML = '';
                        imagePreview.style.display = 'none';
                        filePreview.style.display = 'none';
                        folderPreview.style.display = 'none';
                        fullPreview.style.display = 'none';

                        imageInput.value = '';
                        fileInput.value = '';
                        folderInput.value = '';

                        updateAllSections();
                        updateToggleStatus();
                        updatePreview();

                        Swal.fire({
                            title: 'تم الحذف',
                            text: 'تم حذف جميع إعدادات الرد التلقائي بنجاح',
                            icon: 'success'
                        });
                    } else {
                        Swal.fire({
                            title: 'خطأ',
                            text: data.message || 'حدث خطأ أثناء حذف الإعدادات',
                            icon: 'error'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error deleting settings:', error);
                    Swal.fire({
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء حذف الإعدادات',
                        icon: 'error'
                    });
                });
            }
        });
    };

    // إخفاء منتقي الرموز التعبيرية عند النقر خارجه
    document.addEventListener('click', function(event) {
        if (!emojiBtn.contains(event.target) && !emojiPicker.contains(event.target)) {
            emojiPicker.classList.remove('show');
        }
    });
});
