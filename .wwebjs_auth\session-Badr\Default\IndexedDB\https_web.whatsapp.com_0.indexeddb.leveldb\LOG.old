2025/06/27-23:34:49.459 52c Reusing MANIFEST D:\Whatsappnode\.wwebjs_auth\session-Badr\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/06/27-23:34:49.459 52c Recovering log #158
2025/06/27-23:34:49.487 52c Reusing old log D:\Whatsappnode\.wwebjs_auth\session-Badr\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/000158.log 
2025/06/27-23:34:49.487 52c Delete type=2 #154
2025/06/27-23:34:49.487 52c Delete type=2 #155
2025/06/27-23:34:49.487 52c Delete type=2 #156
2025/06/27-23:34:49.487 52c Delete type=2 #159
2025/06/27-23:34:52.726 4028 Level-0 table #165: started
2025/06/27-23:34:52.740 4028 Level-0 table #165: 202821 bytes OK
2025/06/27-23:34:52.744 4028 Delete type=0 #158
2025/06/27-23:34:52.754 4028 Compacting 1@1 + 3@2 files
2025/06/27-23:34:52.918 4028 Generated table #166@1: 66365 keys, 2237259 bytes
2025/06/27-23:34:53.010 4028 Generated table #167@1: 37138 keys, 2189186 bytes
2025/06/27-23:34:53.021 4028 Generated table #168@1: 1436 keys, 26410 bytes
2025/06/27-23:34:53.021 4028 Compacted 1@1 + 3@2 files => 4452855 bytes
2025/06/27-23:34:53.026 4028 compacted to: files[ 0 0 3 0 0 0 0 ]
2025/06/27-23:34:53.026 4028 Delete type=2 #165
2025/06/27-23:34:53.027 1558 Manual compaction at level-0 from '\x000\x00\x00\x00' @ 72057594037927935 : 1 .. '\x001\x00\x00\x00' @ 0 : 0; will stop at (end)
