class IntelligentLearningAI {
    constructor() {
        // ذاكرة النظام للردود المتعلمة
        this.responseMemory = new Map();
        this.conceptDatabase = new Map();
        this.contextPatterns = new Map();
        this.semanticRelations = new Map();
        
        // إحصائيات التعلم
        this.learningStats = {
            totalResponses: 0,
            conceptsLearned: 0,
            patternsIdentified: 0,
            lastUpdate: null
        };
        
        console.log('🧠 تم تهيئة نظام التعلم الذكي');
    }
    
    // تنظيف النص العربي
    cleanArabicText(text) {
        if (!text) return '';
        return text
            .replace(/[ًٌٍَُِّْ]/g, '') // إزالة التشكيل
            .replace(/[؟!،.]/g, '') // إزالة علامات الترقيم
            .replace(/\s+/g, ' ') // توحيد المسافات
            .trim()
            .toLowerCase();
    }
    
    // تحويل الأرقام العربية إلى إنجليزية
    convertArabicNumbers(text) {
        const arabicNumbers = '٠١٢٣٤٥٦٧٨٩';
        const englishNumbers = '0123456789';
        
        for (let i = 0; i < arabicNumbers.length; i++) {
            text = text.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
        }
        return text;
    }
    
    // استخراج المفاهيم من النص
    extractConcepts(text) {
        const cleanText = this.cleanArabicText(text);
        const words = cleanText.split(' ').filter(word => word.length > 2);
        
        const concepts = {
            keywords: [],
            actions: [],
            entities: [],
            numbers: [],
            questions: []
        };
        
        // كلمات الأفعال
        const actionWords = ['احجز', 'أريد', 'أطلب', 'أحتاج', 'ألغي', 'أغير', 'أسأل'];
        // كلمات الاستفهام
        const questionWords = ['ما', 'ماذا', 'كيف', 'متى', 'أين', 'هل', 'كم'];
        // كلمات الكيانات
        const entityWords = ['عضوية', 'تدريب', 'حجز', 'سعر', 'موعد', 'خدمة', 'صالة'];
        
        words.forEach(word => {
            // استخراج الأرقام
            if (/\d+/.test(word)) {
                concepts.numbers.push(this.convertArabicNumbers(word));
            }
            
            // استخراج الأفعال
            if (actionWords.some(action => word.includes(action))) {
                concepts.actions.push(word);
            }
            
            // استخراج الاستفهامات
            if (questionWords.includes(word)) {
                concepts.questions.push(word);
            }
            
            // استخراج الكيانات
            if (entityWords.some(entity => word.includes(entity))) {
                concepts.entities.push(word);
            }
            
            // الكلمات المفتاحية العامة
            if (word.length > 3) {
                concepts.keywords.push(word);
            }
        });
        
        return concepts;
    }
    
    // تحليل نمط الرد
    analyzeResponsePattern(responseText) {
        const concepts = this.extractConcepts(responseText);
        const cleanText = this.cleanArabicText(responseText);

        // تحديد نوع الرد بدقة أكبر
        let responseType = 'informational';
        let typeScore = 0;

        // فحص أنواع الردود بالأولوية
        const typeChecks = [
            { type: 'pricing', keywords: ['سعر', 'ريال', 'تكلفة', 'مبلغ', 'قائمة الأسعار'], weight: 3 },
            { type: 'booking', keywords: ['احجز', 'حجز', 'موعد', 'جلسة'], weight: 3 },
            { type: 'cancellation', keywords: ['ألغ', 'إلغاء', 'تراجع'], weight: 3 },
            { type: 'training', keywords: ['تدريب', 'جدول', 'تمرين', 'لياقة'], weight: 2 },
            { type: 'services', keywords: ['خدمة', 'خدمات', 'نوفر', 'تشمل'], weight: 2 },
            { type: 'membership', keywords: ['عضوية', 'عضو', 'اشتراك'], weight: 2 },
            { type: 'schedule', keywords: ['وقت', 'ساعة', 'مواعيد', 'يوم'], weight: 1 }
        ];

        typeChecks.forEach(check => {
            const matches = check.keywords.filter(keyword => cleanText.includes(keyword)).length;
            const score = matches * check.weight;
            if (score > typeScore) {
                typeScore = score;
                responseType = check.type;
            }
        });

        return {
            type: responseType,
            typeScore: typeScore,
            concepts: concepts,
            mainTopics: this.identifyMainTopics(cleanText),
            intentSignals: this.identifyIntentSignals(cleanText),
            contextClues: this.identifyContextClues(cleanText)
        };
    }
    
    // تحديد المواضيع الرئيسية
    identifyMainTopics(text) {
        const topics = [];
        
        const topicKeywords = {
            membership: ['عضوية', 'اشتراك', 'عضو'],
            training: ['تدريب', 'تمرين', 'لياقة', 'رياضة'],
            pricing: ['سعر', 'تكلفة', 'ريال', 'مبلغ', 'رسوم'],
            booking: ['حجز', 'موعد', 'جلسة'],
            schedule: ['وقت', 'ساعة', 'يوم', 'مواعيد'],
            services: ['خدمة', 'خدمات', 'نوفر', 'متوفر'],
            cancellation: ['إلغاء', 'ألغي', 'تراجع']
        };
        
        Object.keys(topicKeywords).forEach(topic => {
            const keywords = topicKeywords[topic];
            if (keywords.some(keyword => text.includes(keyword))) {
                topics.push(topic);
            }
        });
        
        return topics;
    }
    
    // تحديد إشارات النية
    identifyIntentSignals(text) {
        const signals = [];
        
        if (text.includes('أريد') || text.includes('أطلب')) {
            signals.push('request');
        }
        if (text.includes('ما') || text.includes('كيف') || text.includes('متى')) {
            signals.push('question');
        }
        if (text.includes('احجز') || text.includes('حجز')) {
            signals.push('booking_intent');
        }
        if (text.includes('ألغي') || text.includes('إلغاء')) {
            signals.push('cancellation_intent');
        }
        
        return signals;
    }
    
    // تحديد أدلة السياق
    identifyContextClues(text) {
        const clues = [];
        
        if (text.includes('شخصي') || text.includes('فردي')) {
            clues.push('personal_service');
        }
        if (text.includes('جماعي') || text.includes('مجموعة')) {
            clues.push('group_service');
        }
        if (text.includes('شهري') || text.includes('سنوي')) {
            clues.push('subscription_period');
        }
        if (text.includes('مجاني') || text.includes('مجاناً')) {
            clues.push('free_service');
        }
        
        return clues;
    }
    
    // تعلم من جميع الردود في قاعدة البيانات
    async learnFromResponses(responses) {
        console.log('🎓 بدء عملية التعلم من الردود...');
        
        this.responseMemory.clear();
        this.conceptDatabase.clear();
        this.contextPatterns.clear();
        this.semanticRelations.clear();
        
        let learnedCount = 0;
        
        responses.forEach((response, index) => {
            if (response.messages && response.messages.text) {
                const responseId = `response_${index}`;
                const analysis = this.analyzeResponsePattern(response.messages.text);
                
                // حفظ تحليل الرد في الذاكرة
                this.responseMemory.set(responseId, {
                    originalText: response.messages.text,
                    analysis: analysis,
                    code: response.code || null,
                    metadata: {
                        hasImages: !!(response.messages.images && response.messages.images.length > 0),
                        hasFiles: !!(response.messages.files && response.messages.files.length > 0),
                        hasFolders: !!(response.messages.folders && response.messages.folders.length > 0)
                    }
                });
                
                // بناء قاعدة بيانات المفاهيم
                analysis.concepts.keywords.forEach(keyword => {
                    if (!this.conceptDatabase.has(keyword)) {
                        this.conceptDatabase.set(keyword, []);
                    }
                    this.conceptDatabase.get(keyword).push(responseId);
                });
                
                // بناء أنماط السياق
                analysis.mainTopics.forEach(topic => {
                    if (!this.contextPatterns.has(topic)) {
                        this.contextPatterns.set(topic, []);
                    }
                    this.contextPatterns.get(topic).push(responseId);
                });
                
                // بناء العلاقات الدلالية
                const semanticKey = `${analysis.type}_${analysis.mainTopics.join('_')}`;
                if (!this.semanticRelations.has(semanticKey)) {
                    this.semanticRelations.set(semanticKey, []);
                }
                this.semanticRelations.get(semanticKey).push(responseId);
                
                learnedCount++;
            }
        });
        
        // تحديث إحصائيات التعلم
        this.learningStats = {
            totalResponses: learnedCount,
            conceptsLearned: this.conceptDatabase.size,
            patternsIdentified: this.contextPatterns.size,
            lastUpdate: new Date().toISOString()
        };
        
        console.log('✅ تم الانتهاء من التعلم:');
        console.log(`   📚 إجمالي الردود المتعلمة: ${learnedCount}`);
        console.log(`   🧩 المفاهيم المستخرجة: ${this.conceptDatabase.size}`);
        console.log(`   🔗 الأنماط المحددة: ${this.contextPatterns.size}`);
        console.log(`   🎯 العلاقات الدلالية: ${this.semanticRelations.size}`);
        
        return this.learningStats;
    }
    
    // تحليل الرسالة الواردة وإيجاد أفضل تطابق
    analyzeIncomingMessage(messageText) {
        console.log(`🔍 تحليل الرسالة الواردة: "${messageText}"`);
        
        const messageConcepts = this.extractConcepts(messageText);
        const messageAnalysis = this.analyzeResponsePattern(messageText);
        
        console.log('📊 تحليل الرسالة:');
        console.log(`   🎯 النوع المتوقع: ${messageAnalysis.type}`);
        console.log(`   📝 المواضيع: ${messageAnalysis.mainTopics.join(', ')}`);
        console.log(`   🎪 إشارات النية: ${messageAnalysis.intentSignals.join(', ')}`);
        
        // البحث عن التطابقات
        const matches = this.findBestMatches(messageConcepts, messageAnalysis);
        
        return {
            messageAnalysis: messageAnalysis,
            messageConcepts: messageConcepts,
            matches: matches
        };
    }
    
    // البحث عن أفضل التطابقات
    findBestMatches(messageConcepts, messageAnalysis) {
        const candidateScores = new Map();

        // البحث بالمفاهيم مع أوزان ذكية
        messageConcepts.keywords.forEach(keyword => {
            if (this.conceptDatabase.has(keyword)) {
                this.conceptDatabase.get(keyword).forEach(responseId => {
                    if (!candidateScores.has(responseId)) {
                        candidateScores.set(responseId, { score: 0, reasons: [], keywordMatches: 0, topicMatches: 0 });
                    }
                    candidateScores.get(responseId).score += 15;
                    candidateScores.get(responseId).keywordMatches++;
                    candidateScores.get(responseId).reasons.push(`keyword_match: ${keyword}`);
                });
            }
        });

        // البحث بالمواضيع مع أوزان أعلى
        messageAnalysis.mainTopics.forEach(topic => {
            if (this.contextPatterns.has(topic)) {
                this.contextPatterns.get(topic).forEach(responseId => {
                    if (!candidateScores.has(responseId)) {
                        candidateScores.set(responseId, { score: 0, reasons: [], keywordMatches: 0, topicMatches: 0 });
                    }
                    candidateScores.get(responseId).score += 25;
                    candidateScores.get(responseId).topicMatches++;
                    candidateScores.get(responseId).reasons.push(`topic_match: ${topic}`);
                });
            }
        });

        // البحث بالعلاقات الدلالية مع أوزان عالية
        const semanticKey = `${messageAnalysis.type}_${messageAnalysis.mainTopics.join('_')}`;
        if (this.semanticRelations.has(semanticKey)) {
            this.semanticRelations.get(semanticKey).forEach(responseId => {
                if (!candidateScores.has(responseId)) {
                    candidateScores.set(responseId, { score: 0, reasons: [], keywordMatches: 0, topicMatches: 0 });
                }
                candidateScores.get(responseId).score += 40;
                candidateScores.get(responseId).reasons.push(`semantic_match: ${semanticKey}`);
            });
        }

        // تطبيق مكافآت للتطابقات المتعددة
        candidateScores.forEach((data, responseId) => {
            // مكافأة للتطابقات المتعددة
            if (data.keywordMatches > 1) {
                data.score += data.keywordMatches * 5;
                data.reasons.push(`multi_keyword_bonus: +${data.keywordMatches * 5}`);
            }
            if (data.topicMatches > 1) {
                data.score += data.topicMatches * 10;
                data.reasons.push(`multi_topic_bonus: +${data.topicMatches * 10}`);
            }

            // تحليل التطابق مع نوع الرد
            const response = this.responseMemory.get(responseId);
            if (response && response.analysis) {
                if (response.analysis.type === messageAnalysis.type) {
                    data.score += 20;
                    data.reasons.push(`type_match: ${messageAnalysis.type}`);
                }

                // مكافأة للتطابق في إشارات النية
                const commonIntents = messageAnalysis.intentSignals.filter(intent =>
                    response.analysis.intentSignals.includes(intent)
                );
                if (commonIntents.length > 0) {
                    data.score += commonIntents.length * 15;
                    data.reasons.push(`intent_match: ${commonIntents.join(',')}`);
                }
            }
        });

        // ترتيب النتائج
        const sortedMatches = Array.from(candidateScores.entries())
            .map(([responseId, data]) => ({
                responseId,
                score: data.score,
                reasons: data.reasons,
                response: this.responseMemory.get(responseId),
                keywordMatches: data.keywordMatches,
                topicMatches: data.topicMatches
            }))
            .sort((a, b) => b.score - a.score);

        return sortedMatches;
    }
    
    // الحصول على أفضل رد
    getBestResponse(messageText) {
        const analysis = this.analyzeIncomingMessage(messageText);

        if (analysis.matches.length === 0) {
            console.log('❌ لم يتم العثور على تطابق مناسب');
            return null;
        }

        const bestMatch = analysis.matches[0];

        // حساب الثقة بطريقة أكثر ذكاءً
        let baseConfidence = Math.min(bestMatch.score / 80 * 100, 100);

        // تعديل الثقة بناءً على جودة التطابق
        if (bestMatch.keywordMatches >= 2 && bestMatch.topicMatches >= 1) {
            baseConfidence = Math.min(baseConfidence * 1.2, 100);
        }

        // تقليل الثقة للتطابقات الضعيفة
        if (bestMatch.score < 30) {
            baseConfidence = Math.max(baseConfidence * 0.7, 0);
        }

        // تعديل الثقة بناءً على نوع الرسالة
        if (analysis.messageAnalysis.mainTopics.length === 0) {
            baseConfidence = Math.max(baseConfidence * 0.5, 0);
        }

        const confidence = Math.round(baseConfidence * 100) / 100;

        console.log('🏆 أفضل تطابق:');
        console.log(`   📊 النقاط: ${bestMatch.score}`);
        console.log(`   🎯 الثقة: ${confidence.toFixed(2)}%`);
        console.log(`   🔑 تطابقات الكلمات: ${bestMatch.keywordMatches}`);
        console.log(`   📋 تطابقات المواضيع: ${bestMatch.topicMatches}`);
        console.log(`   📝 الأسباب: ${bestMatch.reasons.join(', ')}`);

        // إرجاع null إذا كانت الثقة منخفضة جداً
        if (confidence < 25) {
            console.log('⚠️ الثقة منخفضة جداً، لا يوجد تطابق مناسب');
            return null;
        }

        return {
            response: bestMatch.response,
            confidence: confidence,
            score: bestMatch.score,
            reasons: bestMatch.reasons,
            analysis: analysis,
            keywordMatches: bestMatch.keywordMatches,
            topicMatches: bestMatch.topicMatches
        };
    }
    
    // طباعة إحصائيات التعلم
    printLearningStats() {
        console.log('📈 إحصائيات نظام التعلم:');
        console.log(`   📚 إجمالي الردود: ${this.learningStats.totalResponses}`);
        console.log(`   🧩 المفاهيم المتعلمة: ${this.learningStats.conceptsLearned}`);
        console.log(`   🔗 الأنماط المحددة: ${this.learningStats.patternsIdentified}`);
        console.log(`   🕒 آخر تحديث: ${this.learningStats.lastUpdate}`);
    }
}

module.exports = IntelligentLearningAI;
