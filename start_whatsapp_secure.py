#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WhatsApp Node - المتصفح المدمج الحصري
تطبيق آمن ومبسط لإدارة WhatsApp Node
"""

import sys
import os
import subprocess
import time
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from LocalBrowser import WhatsAppBrowser

def check_dependencies():
    """فحص المتطلبات الأساسية"""
    try:
        import PyQt5
        import requests
        return True
    except ImportError as e:
        QMessageBox.critical(None, "خطأ في المتطلبات", 
                           f"مكتبة مفقودة: {str(e)}\n\nيرجى تثبيت المتطلبات باستخدام:\npip install PyQt5 requests")
        return False

def check_node_server():
    """فحص وجود ملفات السيرفر"""
    required_files = ['server.js', 'package.json']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        QMessageBox.critical(None, "ملفات مفقودة", 
                           f"الملفات التالية مفقودة:\n{', '.join(missing_files)}")
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    # إعداد التطبيق
    app = QApplication(sys.argv)
    app.setApplicationName("WhatsApp Node - المتصفح المدمج")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("WhatsApp Node")
    
    # تعيين الترميز
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # فحص المتطلبات
    if not check_dependencies():
        return 1
    
    if not check_node_server():
        return 1
    
    try:
        # إنشاء وتشغيل المتصفح
        browser = WhatsAppBrowser()
        browser.show()
        
        # رسالة ترحيب
        QMessageBox.information(None, "مرحباً", 
                              "🎉 مرحباً بك في WhatsApp Node!\n\n"
                              "✅ تم تبسيط النظام وإزالة الحماية المفرطة\n"
                              "🔒 المتصفح المدمج هو الطريقة الوحيدة للوصول\n"
                              "🚀 السيرفر سيبدأ تلقائياً\n\n"
                              "استمتع بالتجربة المحسنة!")
        
        # تشغيل التطبيق
        return app.exec_()
        
    except Exception as e:
        QMessageBox.critical(None, "خطأ في التشغيل", 
                           f"حدث خطأ أثناء تشغيل التطبيق:\n{str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
