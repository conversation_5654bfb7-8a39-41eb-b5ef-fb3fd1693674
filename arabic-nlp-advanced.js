// نظام التحليل اللغوي العربي المتقدم
// يدعم جميع اللهجات العربية واستخراج المشتقات

class ArabicNLPAdvanced {
    constructor() {
        // قاموس الجذور العربية الشائعة
        this.arabicRoots = {
            // جذور ثلاثية شائعة
            'كتب': ['كتب', 'كاتب', 'مكتوب', 'كتابة', 'مكتب', 'مكتبة', 'كتيب'],
            'قرأ': ['قرأ', 'قارئ', 'مقروء', 'قراءة', 'مقرأ', 'قرآن'],
            'علم': ['علم', 'عالم', 'معلوم', 'تعليم', 'معلم', 'علمي', 'أعلم'],
            'عمل': ['عمل', 'عامل', 'معمول', 'عمال', 'معمل', 'عملي', 'أعمال'],
            'درس': ['درس', 'دارس', 'مدروس', 'دراسة', 'مدرسة', 'مدرس'],
            'سأل': ['سأل', 'سائل', 'مسؤول', 'سؤال', 'مسألة', 'استفسار'],
            'حضر': ['حضر', 'حاضر', 'محضور', 'حضور', 'محضر', 'حضرة'],
            'ذهب': ['ذهب', 'ذاهب', 'مذهب', 'ذهاب', 'أذهب'],
            'جلس': ['جلس', 'جالس', 'مجلس', 'جلسة', 'أجلس'],
            'نام': ['نام', 'نائم', 'منام', 'نوم', 'أنام'],
            'أكل': ['أكل', 'آكل', 'مأكول', 'أكلة', 'مأكل'],
            'شرب': ['شرب', 'شارب', 'مشروب', 'شراب', 'مشرب'],
            'لعب': ['لعب', 'لاعب', 'ملعوب', 'لعبة', 'ملعب'],
            'سمع': ['سمع', 'سامع', 'مسموع', 'سماع', 'مسمع'],
            'رأى': ['رأى', 'رائي', 'مرئي', 'رؤية', 'مرآة'],
            'فهم': ['فهم', 'فاهم', 'مفهوم', 'فهامة', 'تفهيم'],
            'حب': ['حب', 'حبيب', 'محبوب', 'حبة', 'محبة'],
            'كره': ['كره', 'كاره', 'مكروه', 'كراهية', 'أكره'],
            'خرج': ['خرج', 'خارج', 'مخرج', 'خروج', 'أخرج'],
            'دخل': ['دخل', 'داخل', 'مدخل', 'دخول', 'أدخل'],
            'وقف': ['وقف', 'واقف', 'موقف', 'وقوف', 'أوقف'],
            'جرى': ['جرى', 'جاري', 'مجرى', 'جري', 'أجرى'],
            'مشى': ['مشى', 'ماشي', 'ممشى', 'مشي', 'أمشى'],
            'ركب': ['ركب', 'راكب', 'مركب', 'ركوب', 'أركب'],
            'نزل': ['نزل', 'نازل', 'منزل', 'نزول', 'أنزل'],
            'صعد': ['صعد', 'صاعد', 'مصعد', 'صعود', 'أصعد'],
            'فتح': ['فتح', 'فاتح', 'مفتوح', 'فتحة', 'مفتاح'],
            'غلق': ['غلق', 'غالق', 'مغلق', 'إغلاق', 'أغلق'],
            'بدأ': ['بدأ', 'بادئ', 'مبدوء', 'بداية', 'مبدأ'],
            'انتهى': ['انتهى', 'منته', 'منتهى', 'انتهاء', 'أنهى'],
            'طبخ': ['طبخ', 'طابخ', 'مطبوخ', 'طبخة', 'مطبخ'],
            'غسل': ['غسل', 'غاسل', 'مغسول', 'غسيل', 'مغسلة'],
            'لبس': ['لبس', 'لابس', 'ملبوس', 'لباس', 'ملبس'],
            'نظف': ['نظف', 'ناظف', 'منظف', 'نظافة', 'تنظيف'],
            'رتب': ['رتب', 'راتب', 'مرتب', 'ترتيب', 'أرتب'],
            'حمل': ['حمل', 'حامل', 'محمول', 'حمولة', 'أحمل'],
            'وضع': ['وضع', 'واضع', 'موضوع', 'وضعية', 'أوضع'],
            'أخذ': ['أخذ', 'آخذ', 'مأخوذ', 'أخذة', 'مأخذ'],
            'أعطى': ['أعطى', 'معطي', 'معطى', 'عطاء', 'إعطاء'],
            'باع': ['باع', 'بائع', 'مبيع', 'بيع', 'مبيعات'],
            'اشترى': ['اشترى', 'مشتري', 'مشترى', 'شراء', 'مشتريات'],
            'دفع': ['دفع', 'دافع', 'مدفوع', 'دفعة', 'مدفع'],
            'استلم': ['استلم', 'مستلم', 'مستلم', 'استلام', 'تسليم'],
            'أرسل': ['أرسل', 'مرسل', 'مرسل', 'إرسال', 'رسالة'],
            'وصل': ['وصل', 'واصل', 'موصول', 'وصول', 'توصيل'],
            'سافر': ['سافر', 'مسافر', 'مسافر', 'سفر', 'سفرة'],
            'عاد': ['عاد', 'عائد', 'معاد', 'عودة', 'إعادة'],
            'زار': ['زار', 'زائر', 'مزور', 'زيارة', 'مزار'],
            'التقى': ['التقى', 'ملتقي', 'ملتقى', 'لقاء', 'التقاء'],
            'تكلم': ['تكلم', 'متكلم', 'متكلم', 'كلام', 'تكليم'],
            'سكت': ['سكت', 'ساكت', 'مسكوت', 'سكوت', 'إسكات'],
            'ضحك': ['ضحك', 'ضاحك', 'مضحوك', 'ضحكة', 'إضحاك'],
            'بكى': ['بكى', 'باكي', 'مبكي', 'بكاء', 'إبكاء'],
            'فرح': ['فرح', 'فارح', 'مفروح', 'فرحة', 'إفراح'],
            'حزن': ['حزن', 'حازن', 'محزون', 'حزن', 'إحزان'],
            'غضب': ['غضب', 'غاضب', 'مغضوب', 'غضبة', 'إغضاب'],
            'هدأ': ['هدأ', 'هادئ', 'مهدوء', 'هدوء', 'تهدئة']
        };

        // أنماط اللهجات العربية المختلفة
        this.dialectPatterns = {
            // اللهجة المصرية
            egyptian: {
                'ايه': ['ما', 'ماذا', 'أي'],
                'ازيك': ['كيف حالك', 'كيفك'],
                'عامل ايه': ['كيف حالك', 'ماذا تفعل'],
                'خلاص': ['انتهى', 'كفى', 'حسناً'],
                'يلا': ['هيا', 'تعال', 'بسرعة'],
                'معلش': ['لا بأس', 'لا يهم'],
                'اهو': ['هذا', 'ها هو'],
                'كده': ['هكذا', 'بهذا الشكل'],
                'علشان': ['لأن', 'من أجل'],
                'عشان': ['لأن', 'من أجل']
            },
            // اللهجة الخليجية
            gulf: {
                'شلونك': ['كيف حالك', 'كيفك'],
                'وش': ['ما', 'ماذا', 'أي'],
                'ليش': ['لماذا', 'لم'],
                'زين': ['جيد', 'حسن'],
                'مب': ['ليس', 'لا'],
                'يا هلا': ['أهلاً', 'مرحباً'],
                'الله يعطيك العافية': ['شكراً', 'بارك الله فيك'],
                'ما شاء الله': ['رائع', 'جميل'],
                'إن شاء الله': ['نعم', 'موافق'],
                'يعطيك العافية': ['شكراً', 'أحسنت']
            },
            // اللهجة الشامية
            levantine: {
                'كيفك': ['كيف حالك', 'كيف أنت'],
                'شو': ['ما', 'ماذا', 'أي'],
                'ليش': ['لماذا', 'لم'],
                'هيك': ['هكذا', 'بهذا الشكل'],
                'يلا': ['هيا', 'تعال'],
                'خلص': ['انتهى', 'كفى'],
                'بدي': ['أريد', 'أود'],
                'مش': ['ليس', 'لا'],
                'هون': ['هنا', 'في هذا المكان'],
                'هناك': ['هناك', 'في ذلك المكان']
            },
            // اللهجة المغربية
            moroccan: {
                'كيداير': ['كيف حالك', 'كيفك'],
                'أش': ['ما', 'ماذا'],
                'علاش': ['لماذا', 'لم'],
                'واخا': ['حسناً', 'موافق'],
                'بزاف': ['كثير', 'جداً'],
                'شوية': ['قليل', 'بعض'],
                'دابا': ['الآن', 'حالياً'],
                'غدا': ['غداً', 'في المستقبل'],
                'هنا': ['هنا', 'في هذا المكان'],
                'تما': ['هناك', 'في ذلك المكان']
            }
        };

        // كلمات الربط والأدوات
        this.stopWords = new Set([
            'في', 'من', 'إلى', 'على', 'عن', 'مع', 'بعد', 'قبل', 'تحت', 'فوق',
            'أمام', 'خلف', 'بين', 'حول', 'ضد', 'نحو', 'خلال', 'عبر', 'بدون',
            'هذا', 'هذه', 'ذلك', 'تلك', 'التي', 'الذي', 'التي', 'اللذان',
            'اللتان', 'اللذين', 'اللتين', 'اللواتي', 'اللاتي',
            'أن', 'إن', 'كان', 'كانت', 'يكون', 'تكون', 'سوف', 'قد', 'لقد',
            'ما', 'لا', 'لم', 'لن', 'ليس', 'ليست', 'غير', 'سوى',
            'أو', 'أم', 'لكن', 'لكن', 'غير', 'إلا', 'بل', 'لعل', 'عسى',
            'كل', 'بعض', 'جميع', 'معظم', 'أكثر', 'أقل', 'نفس', 'ذات',
            'هو', 'هي', 'هم', 'هن', 'أنت', 'أنتم', 'أنتن', 'أنا', 'نحن',
            'إياه', 'إياها', 'إياهم', 'إياهن', 'إياك', 'إياكم', 'إياكن', 'إياي', 'إيانا'
        ]);

        // أوزان الأفعال العربية
        this.verbPatterns = [
            /^(ا|أ)(.{2,})$/,  // أفعال بادئة بألف
            /^ت(.{2,})$/,      // أفعال بادئة بتاء
            /^ي(.{2,})$/,      // أفعال بادئة بياء
            /^ن(.{2,})$/,      // أفعال بادئة بنون
            /^(.{3,})ة$/,      // أفعال منتهية بتاء مربوطة
            /^م(.{2,})$/       // أفعال بادئة بميم
        ];

        // أوزان الأسماء العربية
        this.nounPatterns = [
            /^م(.{2,})$/,      // أسماء بادئة بميم
            /^(.{2,})ة$/,      // أسماء منتهية بتاء مربوطة
            /^(.{2,})ان$/,     // أسماء منتهية بألف ونون
            /^(.{2,})ين$/,     // أسماء منتهية بياء ونون
            /^(.{2,})ات$/,     // جمع مؤنث سالم
            /^(.{2,})ون$/      // جمع مذكر سالم
        ];
    }

    // تطبيع النص العربي الشامل
    normalizeArabicText(text) {
        if (!text) return '';

        let normalized = text
            // تحويل إلى أحرف صغيرة
            .toLowerCase()
            // تحويل الأرقام العربية إلى إنجليزية
            .replace(/[٠-٩]/g, (d) => '٠١٢٣٤٥٦٧٨٩'.indexOf(d))
            // إزالة التشكيل والحركات
            .replace(/[\u064B-\u0652\u0670\u0640]/g, '')
            // توحيد الألف
            .replace(/[آأإ]/g, 'ا')
            // توحيد التاء المربوطة والهاء
            .replace(/[ة]/g, 'ه')
            // توحيد الياء
            .replace(/[ى]/g, 'ي')
            // توحيد الهمزة
            .replace(/[ؤئ]/g, 'ء')
            // إزالة علامات الترقيم
            .replace(/[.,;:!?()[\]{}"'`~@#$%^&*+=|\\<>]/g, ' ')
            // إزالة المسافات الزائدة
            .replace(/\s+/g, ' ')
            .trim();

        return normalized;
    }

    // تقسيم النص إلى كلمات مع معالجة اللهجات
    tokenizeWithDialects(text) {
        const normalizedText = this.normalizeArabicText(text);
        let words = normalizedText.split(/\s+/).filter(word => word.length > 0);

        // معالجة اللهجات - تحويل كلمات اللهجة إلى الفصحى
        const processedWords = [];
        
        for (let word of words) {
            let standardWord = word;
            
            // البحث في أنماط اللهجات
            for (const [dialect, patterns] of Object.entries(this.dialectPatterns)) {
                if (patterns[word]) {
                    // إضافة المعاني المختلفة للكلمة
                    processedWords.push(...patterns[word]);
                    standardWord = patterns[word][0]; // أخذ المعنى الأول كأساسي
                    break;
                }
            }
            
            processedWords.push(standardWord);
        }

        return [...new Set(processedWords)]; // إزالة التكرار
    }

    // استخراج الجذر والمشتقات
    extractRootAndDerivatives(word) {
        const derivatives = new Set();
        derivatives.add(word); // إضافة الكلمة الأصلية

        // البحث المباشر في قاموس الجذور
        for (const [root, rootDerivatives] of Object.entries(this.arabicRoots)) {
            if (rootDerivatives.includes(word)) {
                // إضافة جميع مشتقات هذا الجذر
                rootDerivatives.forEach(derivative => derivatives.add(derivative));
                break;
            }
        }

        // استخراج الجذر بناءً على الأوزان
        const extractedRoot = this.extractRootByPattern(word);
        if (extractedRoot && this.arabicRoots[extractedRoot]) {
            this.arabicRoots[extractedRoot].forEach(derivative => derivatives.add(derivative));
        }

        return Array.from(derivatives);
    }

    // استخراج الجذر بناءً على الأوزان العربية
    extractRootByPattern(word) {
        if (word.length < 3) return null;

        // إزالة السوابق الشائعة
        let cleanWord = word;
        const prefixes = ['ال', 'و', 'ف', 'ب', 'ك', 'ل', 'م', 'ت', 'ي', 'ن', 'أ', 'س'];
        for (const prefix of prefixes) {
            if (cleanWord.startsWith(prefix) && cleanWord.length > prefix.length + 2) {
                cleanWord = cleanWord.substring(prefix.length);
                break;
            }
        }

        // إزالة اللواحق الشائعة
        const suffixes = ['ة', 'ان', 'ين', 'ون', 'ات', 'ها', 'هم', 'هن', 'ك', 'كم', 'كن', 'ني', 'نا'];
        for (const suffix of suffixes) {
            if (cleanWord.endsWith(suffix) && cleanWord.length > suffix.length + 2) {
                cleanWord = cleanWord.substring(0, cleanWord.length - suffix.length);
                break;
            }
        }

        // استخراج الجذر الثلاثي (الأكثر شيوعاً)
        if (cleanWord.length >= 3) {
            return cleanWord.substring(0, 3);
        }

        return null;
    }

    // تحليل النص الشامل
    analyzeText(text) {
        const tokens = this.tokenizeWithDialects(text);
        const analysis = {
            originalText: text,
            normalizedText: this.normalizeArabicText(text),
            tokens: tokens,
            keywords: [],
            roots: new Set(),
            derivatives: new Set(),
            dialectWords: [],
            contentWords: []
        };

        // معالجة كل كلمة
        for (const token of tokens) {
            // تجاهل كلمات الربط
            if (this.stopWords.has(token)) continue;

            // إضافة الكلمة كمفتاحية
            analysis.keywords.push(token);
            analysis.contentWords.push(token);

            // استخراج الجذر والمشتقات
            const derivatives = this.extractRootAndDerivatives(token);
            derivatives.forEach(derivative => {
                analysis.derivatives.add(derivative);
            });

            // البحث عن كلمات اللهجة
            for (const [dialect, patterns] of Object.entries(this.dialectPatterns)) {
                if (patterns[token]) {
                    analysis.dialectWords.push({
                        word: token,
                        dialect: dialect,
                        meanings: patterns[token]
                    });
                }
            }
        }

        // تحويل Sets إلى Arrays
        analysis.derivatives = Array.from(analysis.derivatives);
        analysis.roots = Array.from(analysis.roots);

        return analysis;
    }

    // حساب التشابه المتقدم بين نصين
    calculateAdvancedSimilarity(text1, text2) {
        const analysis1 = this.analyzeText(text1);
        const analysis2 = this.analyzeText(text2);

        // حساب التشابه على مستويات مختلفة
        const exactMatch = this.calculateExactSimilarity(analysis1.tokens, analysis2.tokens);
        const rootMatch = this.calculateRootSimilarity(analysis1.derivatives, analysis2.derivatives);
        const dialectMatch = this.calculateDialectSimilarity(analysis1.dialectWords, analysis2.dialectWords);
        const keywordMatch = this.calculateKeywordSimilarity(analysis1.keywords, analysis2.keywords);

        // حساب النتيجة النهائية بأوزان مختلفة
        const finalScore = (
            exactMatch * 0.4 +      // 40% للتطابق المباشر
            rootMatch * 0.3 +       // 30% للتطابق في الجذور
            dialectMatch * 0.2 +    // 20% للتطابق في اللهجات
            keywordMatch * 0.1      // 10% للكلمات المفتاحية
        );

        return {
            total: finalScore,
            breakdown: {
                exact: exactMatch,
                root: rootMatch,
                dialect: dialectMatch,
                keyword: keywordMatch
            },
            details: {
                analysis1: analysis1,
                analysis2: analysis2
            }
        };
    }

    // حساب التشابه المباشر
    calculateExactSimilarity(tokens1, tokens2) {
        if (tokens1.length === 0 || tokens2.length === 0) return 0;

        const set1 = new Set(tokens1);
        const set2 = new Set(tokens2);
        const intersection = new Set([...set1].filter(x => set2.has(x)));

        return (intersection.size * 2) / (set1.size + set2.size);
    }

    // حساب التشابه في الجذور
    calculateRootSimilarity(derivatives1, derivatives2) {
        if (derivatives1.length === 0 || derivatives2.length === 0) return 0;

        const set1 = new Set(derivatives1);
        const set2 = new Set(derivatives2);
        const intersection = new Set([...set1].filter(x => set2.has(x)));

        return (intersection.size * 2) / (set1.size + set2.size);
    }

    // حساب التشابه في اللهجات
    calculateDialectSimilarity(dialectWords1, dialectWords2) {
        if (dialectWords1.length === 0 || dialectWords2.length === 0) return 0;

        let matches = 0;
        let total = dialectWords1.length + dialectWords2.length;

        for (const word1 of dialectWords1) {
            for (const word2 of dialectWords2) {
                // تطابق في نفس اللهجة
                if (word1.dialect === word2.dialect && word1.word === word2.word) {
                    matches += 2;
                }
                // تطابق في المعنى
                else if (word1.meanings.some(meaning => word2.meanings.includes(meaning))) {
                    matches += 1;
                }
            }
        }

        return total > 0 ? matches / total : 0;
    }

    // حساب التشابه في الكلمات المفتاحية
    calculateKeywordSimilarity(keywords1, keywords2) {
        return this.calculateExactSimilarity(keywords1, keywords2);
    }
}

module.exports = { ArabicNLPAdvanced };
