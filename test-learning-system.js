const IntelligentLearningAI = require('./intelligent-learning-ai');

// بيانات الردود التجريبية (نفس البيانات المستخدمة في الاختبارات السابقة)
const testResponses = [
    {
        code: "عضوية",
        messages: {
            text: "معلومات العضوية: نوفر عضويات شهرية وسنوية مع خصومات مميزة. العضوية الشهرية 100 ريال والسنوية 1000 ريال. تشمل العضوية استخدام جميع الأجهزة والمرافق.",
            images: [],
            files: [],
            folders: []
        }
    },
    {
        code: "مواعيد_عضوية",
        messages: {
            text: "مواعيد استحقاق العضوية: يتم تجديد العضوية الشهرية كل 30 يوم والسنوية كل 365 يوم. سنرسل تذكير قبل انتهاء العضوية بـ 7 أيام.",
            images: [],
            files: [],
            folders: []
        }
    },
    {
        code: "تدريب",
        messages: {
            text: "جدول التدريبات: الصباح من 6-10، المساء من 4-10. تدريبات جماعية: يوغا، زومبا، كروس فيت. تدريب شخصي متوفر بموعد مسبق.",
            images: [],
            files: [],
            folders: []
        }
    },
    {
        code: "حجز_تدريب",
        messages: {
            text: "حجز التدريبات: يمكنك حجز جلسة تدريب شخصية مع المدرب المختص. الحجز متاح عبر التطبيق أو الهاتف. مدة الجلسة ساعة واحدة.",
            images: [],
            files: [],
            folders: []
        }
    },
    {
        code: "أسعار",
        messages: {
            text: "قائمة الأسعار: العضوية الشهرية 100 ريال، السنوية 1000 ريال، التدريب الشخصي 150 ريال/ساعة، استشارة التغذية 80 ريال.",
            images: [],
            files: [],
            folders: []
        }
    },
    {
        code: "خدمات",
        messages: {
            text: "خدماتنا تشمل: صالة ألعاب رياضية مجهزة، تدريب شخصي، استشارات تغذية، برامج لياقة متخصصة، ساونا وجاكوزي، مواقف مجانية.",
            images: [],
            files: [],
            folders: []
        }
    },
    {
        code: "حجز",
        messages: {
            text: "نظام الحجز: احجز موعدك عبر التطبيق أو الهاتف. يمكن الحجز حتى 7 أيام مقدماً. تأكيد الحجز خلال ساعة من الطلب.",
            images: [],
            files: [],
            folders: []
        }
    },
    {
        code: "إلغاء",
        messages: {
            text: "سياسة الإلغاء: يمكن إلغاء الحجز مجاناً قبل 24 ساعة. الإلغاء المتأخر يخضع لرسوم 50% من قيمة الخدمة. الإلغاء عبر التطبيق أو الهاتف.",
            images: [],
            files: [],
            folders: []
        }
    }
];

// حالات الاختبار
const testCases = [
    {
        id: 1,
        message: "ما هي مواعيد التدريب؟",
        expectedCode: "تدريب",
        description: "سؤال عن مواعيد التدريب"
    },
    {
        id: 2,
        message: "كم سعر العضوية الشهرية؟",
        expectedCode: "أسعار",
        description: "سؤال عن أسعار العضوية"
    },
    {
        id: 3,
        message: "أريد حجز جلسة تدريب شخصية",
        expectedCode: "حجز_تدريب",
        description: "طلب حجز تدريب"
    },
    {
        id: 4,
        message: "ما هي الخدمات المتوفرة؟",
        expectedCode: "خدمات",
        description: "سؤال عن الخدمات"
    },
    {
        id: 5,
        message: "كيف أحجز موعد؟",
        expectedCode: "حجز",
        description: "سؤال عن طريقة الحجز"
    },
    {
        id: 6,
        message: "أريد إلغاء حجزي",
        expectedCode: "إلغاء",
        description: "طلب إلغاء حجز"
    },
    {
        id: 7,
        message: "متى تنتهي عضويتي؟",
        expectedCode: "مواعيد_عضوية",
        description: "سؤال عن انتهاء العضوية"
    },
    {
        id: 8,
        message: "كم تكلفة التدريب الشخصي؟",
        expectedCode: "أسعار",
        description: "سؤال عن سعر التدريب"
    },
    {
        id: 9,
        message: "أريد طلب بيتزا",
        expectedCode: null,
        description: "طلب غير متعلق بالنادي"
    },
    {
        id: 10,
        message: "ما حالة الطقس اليوم؟",
        expectedCode: null,
        description: "سؤال غير متعلق"
    }
];

async function runLearningTest() {
    console.log('🧠 اختبار نظام التعلم الذكي');
    console.log('=' .repeat(50));
    
    // إنشاء النظام
    const learningAI = new IntelligentLearningAI();
    
    // مرحلة التعلم
    console.log('\n📚 مرحلة التعلم من الردود...');
    const learningStats = await learningAI.learnFromResponses(testResponses);
    
    // طباعة إحصائيات التعلم
    learningAI.printLearningStats();
    
    console.log('\n🧪 بدء الاختبارات...');
    console.log('=' .repeat(50));
    
    let successCount = 0;
    let totalTests = testCases.length;
    
    for (const testCase of testCases) {
        console.log(`\n📨 اختبار ${testCase.id}/${totalTests}: ${testCase.description}`);
        console.log(`   الرسالة: "${testCase.message}"`);
        console.log(`   المتوقع: ${testCase.expectedCode || 'لا يوجد تطابق'}`);
        
        const result = learningAI.getBestResponse(testCase.message);
        
        let success = false;
        if (testCase.expectedCode === null) {
            // نتوقع عدم وجود تطابق
            success = !result || result.confidence < 50;
        } else {
            // نتوقع تطابق مع الكود المحدد
            success = result && result.response && result.response.analysis && 
                     result.response.originalText.includes(testResponses.find(r => r.code === testCase.expectedCode)?.messages.text.substring(0, 20));
        }
        
        if (result) {
            console.log(`   🎯 النتيجة: تطابق بثقة ${result.confidence.toFixed(2)}%`);
            console.log(`   📝 الأسباب: ${result.reasons.join(', ')}`);
            console.log(`   💬 الرد: "${result.response.originalText.substring(0, 80)}..."`);
        } else {
            console.log(`   ❌ لم يتم العثور على تطابق مناسب`);
        }
        
        if (success) {
            console.log(`   ✅ الاختبار نجح`);
            successCount++;
        } else {
            console.log(`   ❌ الاختبار فشل`);
        }
    }
    
    console.log('\n📊 ملخص النتائج:');
    console.log('=' .repeat(50));
    console.log(`   إجمالي الاختبارات: ${totalTests}`);
    console.log(`   الاختبارات الناجحة: ${successCount}`);
    console.log(`   الاختبارات الفاشلة: ${totalTests - successCount}`);
    console.log(`   نسبة النجاح: ${(successCount / totalTests * 100).toFixed(2)}%`);
    
    if (successCount === totalTests) {
        console.log('\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.');
    } else {
        console.log('\n⚠️ بعض الاختبارات فشلت. النظام يحتاج إلى تحسينات.');
    }
}

// تشغيل الاختبار
runLearningTest().catch(console.error);
