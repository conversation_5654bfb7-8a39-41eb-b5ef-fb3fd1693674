// اختبار المسارات المطلقة في نظام الرد التلقائي المتقدم
const fs = require('fs');
const path = require('path');

// إنشاء ملفات اختبار بمسارات مطلقة
const testDir = path.join(__dirname, 'temp-test-files');
const testImagePath = path.join(testDir, 'absolute-test-image.txt');
const testFilePath = path.join(testDir, 'absolute-test-file.pdf');

// إنشاء المجلد والملفات
if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
}

fs.writeFileSync(testImagePath, 'This is a test image with absolute path');
fs.writeFileSync(testFilePath, 'This is a test file with absolute path');

// محاكاة بنية الرسائل مع مسارات مطلقة
const testRuleAbsolute = {
    "id": "test_rule_absolute",
    "code": "TEST",
    "keywords": "اختبار - مطلق",
    "priority": 1,
    "messages": {
        "text": "اختبار المسارات المطلقة",
        "image": testImagePath,
        "file": testFilePath,
        "folder": [
            testImagePath,
            testFilePath
        ]
    },
    "enabled": true
};

// دالة اختبار المسارات المطلقة
function testAbsolutePaths() {
    console.log('🧪 اختبار المسارات المطلقة:');
    console.log('=====================================');
    
    const messages = testRuleAbsolute.messages;
    
    // اختبار الصورة
    if (messages.image) {
        let imagePath;
        if (path.isAbsolute(messages.image)) {
            imagePath = messages.image;
        } else {
            imagePath = path.join(__dirname, 'public', messages.image);
        }
        console.log(`🖼️ الصورة: ${imagePath}`);
        console.log(`   - المسار مطلق: ${path.isAbsolute(messages.image) ? 'نعم' : 'لا'}`);
        console.log(`   - الملف موجود: ${fs.existsSync(imagePath) ? 'نعم' : 'لا'}`);
    }
    
    // اختبار الملف
    if (messages.file) {
        let filePath;
        if (path.isAbsolute(messages.file)) {
            filePath = messages.file;
        } else {
            filePath = path.join(__dirname, 'public', messages.file);
        }
        console.log(`📄 الملف: ${filePath}`);
        console.log(`   - المسار مطلق: ${path.isAbsolute(messages.file) ? 'نعم' : 'لا'}`);
        console.log(`   - الملف موجود: ${fs.existsSync(filePath) ? 'نعم' : 'لا'}`);
    }
    
    // اختبار المجلد
    if (messages.folder && Array.isArray(messages.folder)) {
        console.log(`📁 المجلد: ${messages.folder.length} ملف`);
        messages.folder.forEach((folderFile, index) => {
            let folderFilePath;
            if (path.isAbsolute(folderFile)) {
                folderFilePath = folderFile;
            } else {
                folderFilePath = path.join(__dirname, 'public', folderFile);
            }
            console.log(`   ${index + 1}. ${folderFilePath}`);
            console.log(`      - المسار مطلق: ${path.isAbsolute(folderFile) ? 'نعم' : 'لا'}`);
            console.log(`      - الملف موجود: ${fs.existsSync(folderFilePath) ? 'نعم' : 'لا'}`);
        });
    }
}

// تشغيل الاختبار
console.log('🚀 بدء اختبار المسارات المطلقة');
console.log('==========================================\n');

testAbsolutePaths();

console.log('\n✅ انتهاء الاختبار');

// تنظيف الملفات المؤقتة
setTimeout(() => {
    if (fs.existsSync(testDir)) {
        fs.rmSync(testDir, { recursive: true, force: true });
        console.log('🧹 تم حذف الملفات المؤقتة');
    }
}, 1000);
