// اختبار إرسال الرسائل المتعددة في نظام الرد التلقائي المتقدم
const fs = require('fs');
const path = require('path');

// محاكاة بنية الرسائل من ملف JSON
const testRule = {
    "id": "test_rule",
    "code": "201",
    "keywords": "رحلات - رحله - الصيف",
    "priority": 1,
    "messages": {
        "text": "مرحباً! هذا اختبار للرد التلقائي المتقدم",
        "image": "test-files/test-image.txt",
        "file": "test-files/test-file.pdf",
        "folder": [
            "test-files/test-image.txt",
            "test-files/test-file.pdf"
        ]
    },
    "enabled": true
};

// دالة اختبار لفحص المسارات
function testPathResolution() {
    console.log('🧪 اختبار حل المسارات:');
    console.log('=====================================');
    
    const messages = testRule.messages;
    
    // اختبار النص
    console.log(`📝 النص: ${messages.text ? 'موجود' : 'غير موجود'}`);
    
    // اختبار الصورة
    if (messages.image) {
        let imagePath;
        if (path.isAbsolute(messages.image)) {
            imagePath = messages.image;
        } else {
            imagePath = path.join(__dirname, 'public', messages.image);
        }
        console.log(`🖼️ الصورة: ${imagePath}`);
        console.log(`   - المسار مطلق: ${path.isAbsolute(messages.image) ? 'نعم' : 'لا'}`);
        console.log(`   - الملف موجود: ${fs.existsSync(imagePath) ? 'نعم' : 'لا'}`);
    }
    
    // اختبار الملف
    if (messages.file) {
        let filePath;
        if (path.isAbsolute(messages.file)) {
            filePath = messages.file;
        } else {
            filePath = path.join(__dirname, 'public', messages.file);
        }
        console.log(`📄 الملف: ${filePath}`);
        console.log(`   - المسار مطلق: ${path.isAbsolute(messages.file) ? 'نعم' : 'لا'}`);
        console.log(`   - الملف موجود: ${fs.existsSync(filePath) ? 'نعم' : 'لا'}`);
    }
    
    // اختبار المجلد
    if (messages.folder && Array.isArray(messages.folder)) {
        console.log(`📁 المجلد: ${messages.folder.length} ملف`);
        messages.folder.forEach((folderFile, index) => {
            let folderFilePath;
            if (path.isAbsolute(folderFile)) {
                folderFilePath = folderFile;
            } else {
                folderFilePath = path.join(__dirname, 'public', folderFile);
            }
            console.log(`   ${index + 1}. ${folderFilePath}`);
            console.log(`      - المسار مطلق: ${path.isAbsolute(folderFile) ? 'نعم' : 'لا'}`);
            console.log(`      - الملف موجود: ${fs.existsSync(folderFilePath) ? 'نعم' : 'لا'}`);
        });
    }
}

// دالة لاختبار بنية JSON
function testJSONStructure() {
    console.log('\n🔍 اختبار بنية JSON:');
    console.log('=====================================');
    console.log('بنية الرسائل:', JSON.stringify(testRule.messages, null, 2));
    
    console.log('\n📊 أنواع الرسائل المتاحة:');
    console.log(`   - النص: ${testRule.messages.text ? 'نعم' : 'لا'}`);
    console.log(`   - الصورة: ${testRule.messages.image ? 'نعم (' + testRule.messages.image + ')' : 'لا'}`);
    console.log(`   - الملف: ${testRule.messages.file ? 'نعم (' + testRule.messages.file + ')' : 'لا'}`);
    console.log(`   - المجلد: ${testRule.messages.folder ? 'نعم (' + testRule.messages.folder.length + ' ملفات)' : 'لا'}`);
}

// تشغيل الاختبارات
console.log('🚀 بدء اختبار نظام الرد التلقائي المتقدم');
console.log('==========================================\n');

testJSONStructure();
testPathResolution();

console.log('\n✅ انتهاء الاختبار');
