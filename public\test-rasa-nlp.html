<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام NLP المتقدم (Rasa-like)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .header-section {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }
        
        .test-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
        }
        
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            color: white;
        }
        
        .result-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .intent-badge {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
        }
        
        .confidence-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .confidence-fill {
            background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);
            height: 100%;
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.8em;
        }
        
        .entity-tag {
            background: #6f42c1;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin: 2px;
            display: inline-block;
        }
        
        .sentiment-positive { color: #28a745; }
        .sentiment-negative { color: #dc3545; }
        .sentiment-neutral { color: #6c757d; }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .analysis-details {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .json-display {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header-section">
                <h1><i class="fas fa-brain"></i> اختبار نظام NLP المتقدم</h1>
                <p class="mb-0">نظام تحليل النوايا والكيانات المتقدم (يحاكي Rasa)</p>
            </div>

            <!-- Message Analysis Section -->
            <div class="test-section">
                <h3><i class="fas fa-comment-dots text-primary"></i> تحليل الرسائل</h3>
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label fw-bold">الرسالة للتحليل:</label>
                        <textarea id="messageInput" class="form-control" rows="3" 
                                placeholder="اكتب الرسالة هنا للتحليل..."></textarea>
                        <button onclick="analyzeMessage()" class="btn btn-gradient mt-3">
                            <i class="fas fa-search"></i> تحليل الرسالة
                        </button>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">أمثلة سريعة:</label>
                        <div class="d-grid gap-2">
                            <button onclick="setMessage('مرحبا كيف الحال؟')" class="btn btn-outline-primary btn-sm">تحية</button>
                            <button onclick="setMessage('كم سعر الجوال؟')" class="btn btn-outline-primary btn-sm">استفسار سعر</button>
                            <button onclick="setMessage('أريد شراء لابتوب')" class="btn btn-outline-primary btn-sm">طلب شراء</button>
                            <button onclick="setMessage('عندي مشكلة في المنتج')" class="btn btn-outline-primary btn-sm">شكوى</button>
                            <button onclick="setMessage('شكرا لك')" class="btn btn-outline-primary btn-sm">شكر</button>
                        </div>
                    </div>
                </div>

                <div class="loading-spinner" id="analysisLoading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحليل...</span>
                    </div>
                    <p class="mt-2">جاري تحليل الرسالة...</p>
                </div>

                <div id="analysisResult" class="mt-4" style="display: none;"></div>
            </div>

            <!-- Intent Training Section -->
            <div class="test-section">
                <h3><i class="fas fa-graduation-cap text-success"></i> تدريب النوايا</h3>
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label fw-bold">النية:</label>
                        <select id="intentSelect" class="form-select">
                            <option value="greeting">تحية</option>
                            <option value="price_inquiry">استفسار سعر</option>
                            <option value="purchase_request">طلب شراء</option>
                            <option value="complaint">شكوى</option>
                            <option value="thanks">شكر</option>
                            <option value="delivery_inquiry">استفسار توصيل</option>
                            <option value="product_info">معلومات منتج</option>
                            <option value="cancel_order">إلغاء طلب</option>
                            <option value="payment_inquiry">استفسار دفع</option>
                            <option value="location_inquiry">استفسار موقع</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label class="form-label fw-bold">مثال جديد:</label>
                        <input type="text" id="trainingExample" class="form-control" 
                               placeholder="اكتب مثال جديد للتدريب...">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-bold">إضافة:</label>
                        <button onclick="addTrainingExample()" class="btn btn-success w-100">
                            <i class="fas fa-plus"></i> إضافة مثال
                        </button>
                    </div>
                </div>
                <div id="trainingResult" class="mt-3"></div>
            </div>

            <!-- Response Matching Test -->
            <div class="test-section">
                <h3><i class="fas fa-robot text-warning"></i> اختبار مطابقة الردود</h3>
                <div class="row">
                    <div class="col-md-8">
                        <label class="form-label fw-bold">رسالة الاختبار:</label>
                        <input type="text" id="testMessage" class="form-control" 
                               placeholder="اكتب رسالة لاختبار مطابقة الردود...">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label fw-bold">اختبار:</label>
                        <button onclick="testResponseMatching()" class="btn btn-warning w-100">
                            <i class="fas fa-flask"></i> اختبار المطابقة
                        </button>
                    </div>
                </div>
                <div id="matchingResult" class="mt-3"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function setMessage(message) {
            document.getElementById('messageInput').value = message;
        }

        async function analyzeMessage() {
            const message = document.getElementById('messageInput').value.trim();
            if (!message) {
                alert('يرجى إدخال رسالة للتحليل');
                return;
            }

            const loadingDiv = document.getElementById('analysisLoading');
            const resultDiv = document.getElementById('analysisResult');
            
            loadingDiv.style.display = 'block';
            resultDiv.style.display = 'none';

            try {
                const response = await fetch('/api/nlp/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message: message })
                });

                const result = await response.json();
                displayAnalysisResult(result);
            } catch (error) {
                console.error('Error analyzing message:', error);
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        خطأ في تحليل الرسالة: ${error.message}
                    </div>
                `;
            } finally {
                loadingDiv.style.display = 'none';
                resultDiv.style.display = 'block';
            }
        }

        function displayAnalysisResult(result) {
            const resultDiv = document.getElementById('analysisResult');
            
            if (result.success) {
                const analysis = result.analysis;
                const intent = analysis.intent.intent || analysis.intent;
                const confidence = analysis.confidence || analysis.intent.confidence || 0;
                const entities = analysis.entities || [];
                const sentiment = analysis.sentiment || { sentiment: 'neutral', score: 0 };

                resultDiv.innerHTML = `
                    <div class="result-card">
                        <h5><i class="fas fa-chart-line"></i> نتائج التحليل</h5>
                        
                        <div class="analysis-details">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>النية المكتشفة:</strong>
                                    <span class="intent-badge">${intent}</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>مستوى الثقة:</strong>
                                    <div class="confidence-bar">
                                        <div class="confidence-fill" style="width: ${confidence * 100}%">
                                            ${(confidence * 100).toFixed(2)}%
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="analysis-details">
                            <strong>الكيانات المستخرجة:</strong>
                            <div class="mt-2">
                                ${entities.length > 0 ? 
                                    entities.map(entity => 
                                        `<span class="entity-tag">${entity.entity}: ${entity.value}</span>`
                                    ).join(' ') : 
                                    '<span class="text-muted">لا توجد كيانات</span>'
                                }
                            </div>
                        </div>

                        <div class="analysis-details">
                            <strong>تحليل المشاعر:</strong>
                            <span class="sentiment-${sentiment.sentiment}">
                                <i class="fas fa-${sentiment.sentiment === 'positive' ? 'smile' : 
                                                 sentiment.sentiment === 'negative' ? 'frown' : 'meh'}"></i>
                                ${sentiment.sentiment} (${(sentiment.score * 100).toFixed(2)}%)
                            </span>
                        </div>

                        <div class="analysis-details">
                            <strong>البيانات الكاملة:</strong>
                            <div class="json-display">${JSON.stringify(analysis, null, 2)}</div>
                        </div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        ${result.error || 'خطأ في تحليل الرسالة'}
                    </div>
                `;
            }
        }

        async function addTrainingExample() {
            const intent = document.getElementById('intentSelect').value;
            const example = document.getElementById('trainingExample').value.trim();
            
            if (!example) {
                alert('يرجى إدخال مثال للتدريب');
                return;
            }

            try {
                const response = await fetch('/api/nlp/train', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        intent: intent, 
                        example: example 
                    })
                });

                const result = await response.json();
                const resultDiv = document.getElementById('trainingResult');
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <i class="fas fa-check"></i>
                            تم إضافة المثال بنجاح للنية: ${intent}
                        </div>
                    `;
                    document.getElementById('trainingExample').value = '';
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            ${result.error || 'خطأ في إضافة المثال'}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error adding training example:', error);
            }
        }

        async function testResponseMatching() {
            const message = document.getElementById('testMessage').value.trim();
            if (!message) {
                alert('يرجى إدخال رسالة للاختبار');
                return;
            }

            try {
                const response = await fetch('/api/nlp/test-matching', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message: message })
                });

                const result = await response.json();
                const resultDiv = document.getElementById('matchingResult');
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="result-card">
                            <h6><i class="fas fa-search"></i> نتائج مطابقة الردود</h6>
                            <div class="json-display">${JSON.stringify(result.data, null, 2)}</div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            ${result.error || 'خطأ في اختبار المطابقة'}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error testing response matching:', error);
            }
        }
    </script>
</body>
</html>
