#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8

echo ""
echo "================================================"
echo "🚀 WhatsApp Node - المتصفح المدمج الحصري"
echo "================================================"
echo ""

# دالة للتحقق من وجود أمر
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# التحقق من Python
if ! command_exists python3; then
    echo "❌ Python3 غير مثبت"
    echo "يرجى تثبيت Python 3.7 أو أحدث"
    exit 1
fi

# التحقق من Node.js
if ! command_exists node; then
    echo "❌ Node.js غير مثبت"
    echo "يرجى تثبيت Node.js 14 أو أحدث"
    exit 1
fi

# التحقق من pip
if ! command_exists pip3; then
    echo "❌ pip3 غير متوفر"
    echo "يرجى التأكد من تثبيت Python بشكل صحيح"
    exit 1
fi

echo "✅ جميع المتطلبات متوفرة"
echo ""

# تثبيت مكتبات Python المطلوبة
echo "📦 فحص مكتبات Python..."
if ! python3 -c "import PyQt5" 2>/dev/null; then
    echo "📦 تثبيت PyQt5..."
    pip3 install PyQt5 PyQtWebEngine requests
    if [ $? -ne 0 ]; then
        echo "❌ فشل في تثبيت مكتبات Python"
        exit 1
    fi
else
    echo "✅ مكتبات Python متوفرة"
fi

# تثبيت تبعيات Node.js إذا لم تكن موجودة
if [ ! -d "node_modules" ]; then
    echo "📦 تثبيت تبعيات Node.js..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ فشل في تثبيت تبعيات Node.js"
        exit 1
    fi
else
    echo "✅ تبعيات Node.js متوفرة"
fi

echo ""
echo "🚀 بدء تشغيل المتصفح المدمج..."
echo ""

# تشغيل المتصفح المدمج
python3 start_whatsapp_browser.py

echo ""
echo "👋 تم إغلاق التطبيق"
