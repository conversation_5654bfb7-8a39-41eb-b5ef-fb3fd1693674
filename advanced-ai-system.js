// ===== نظام الذكاء الاصطناعي المتقدم والحديث =====
// Advanced AI System for WhatsApp Auto-Reply with Modern NLP Techniques

const natural = require('natural');
const compromise = require('compromise');
const Fuse = require('fuse.js');
const stopword = require('stopword');

// ===== معالج النصوص المتقدم مع تقنيات الذكاء الاصطناعي الحديثة =====
class ModernAITextProcessor {
    constructor() {
        // قواميس متقدمة للمرادفات العربية والإنجليزية
        this.advancedSynonyms = {
            // التحيات والسلام
            'مرحبا': ['أهلا', 'اهلا', 'هلا', 'السلام عليكم', 'صباح الخير', 'مساء الخير', 'حياك الله', 'أهلا وسهلا', 'مرحباً', 'هاي', 'هلو', 'hello', 'hi', 'سلام', 'أهلين'],
            'شكرا': ['شكراً', 'تسلم', 'يعطيك العافية', 'جزاك الله خير', 'مشكور', 'ممتن', 'أقدر لك', 'بارك الله فيك', 'thanks', 'thank you', 'thx', 'شكرًا'],
            
            // الأسعار والتكاليف
            'السعر': ['الثمن', 'التكلفة', 'كم', 'بكم', 'سعر', 'قيمة', 'مبلغ', 'تسعيرة', 'price', 'cost', 'كلفة', 'فلوس', 'مصاري', 'قد ايش', 'كم يكلف'],
            'رخيص': ['مناسب', 'معقول', 'بسيط', 'مقبول', 'cheap', 'affordable', 'منخفض', 'حلو السعر', 'مو غالي'],
            'غالي': ['مكلف', 'باهظ', 'عالي', 'expensive', 'costly', 'مرتفع', 'غالي شوي', 'فوق الميزانية'],
            
            // المساعدة والدعم
            'مساعدة': ['مساعده', 'دعم', 'مساندة', 'عون', 'help', 'support', 'خدمة', 'إعانة', 'ساعدني', 'ساعدوني'],
            'معلومات': ['معلومه', 'بيانات', 'تفاصيل', 'شرح', 'info', 'information', 'data', 'توضيح', 'وضح لي', 'اشرح لي'],
            
            // الطلبات والأوامر
            'أريد': ['أبغى', 'أبي', 'أطلب', 'أحتاج', 'want', 'need', 'بدي', 'عايز', 'ودي', 'نفسي'],
            'اطلب': ['أطلب', 'أريد', 'احجز', 'order', 'book', 'reserve', 'خذ لي', 'جيب لي'],
            
            // الجودة والوصف
            'جيد': ['ممتاز', 'رائع', 'حلو', 'زين', 'good', 'excellent', 'great', 'nice', 'تمام', 'عالي', 'حلو كتير'],
            'سيء': ['مو زين', 'مش حلو', 'bad', 'terrible', 'awful', 'سيئ', 'مو حلو', 'وحش'],
            
            // الوقت
            'سريع': ['بسرعة', 'عاجل', 'fast', 'quick', 'urgent', 'فوري', 'على طول', 'بأسرع وقت'],
            'بطيء': ['متأخر', 'slow', 'late', 'delayed', 'مو سريع', 'يأخذ وقت'],
            
            // الموافقة والرفض
            'نعم': ['أيوه', 'اي', 'موافق', 'yes', 'ok', 'okay', 'تمام', 'زين', 'اوكي', 'صح', 'أجل'],
            'لا': ['لأ', 'مو موافق', 'no', 'not', 'مش موافق', 'رفض', 'مو زين', 'ما أبي'],
            
            // المشاكل والشكاوى
            'مشكلة': ['مشكله', 'عطل', 'خلل', 'problem', 'issue', 'trouble', 'صعوبة', 'مشاكل', 'عندي مشكلة'],
            'شكوى': ['شكوة', 'اعتراض', 'complaint', 'تذمر', 'مو راضي', 'مش عاجبني'],
            
            // الخدمات والمنتجات
            'خدمة': ['خدمه', 'service', 'خدمات', 'سيرفس'],
            'منتج': ['منتوج', 'product', 'سلعة', 'بضاعة', 'حاجة', 'شي'],
            
            // الاتجاهات والمواقع
            'مكان': ['موقع', 'عنوان', 'location', 'place', 'address', 'وين', 'فين', 'أين'],
            'قريب': ['جنب', 'بجانب', 'near', 'close', 'بالقرب', 'حدا', 'جانب'],
            'بعيد': ['far', 'distant', 'بعيد عن', 'مو قريب', 'بعيد شوي']
        };

        // قاموس تطبيع الأحرف العربية المتقدم
        this.arabicNormalization = {
            'أ': 'ا', 'إ': 'ا', 'آ': 'ا', 'ء': 'ا',
            'ة': 'ه', 'ى': 'ي', 'ؤ': 'و', 'ئ': 'ي'
        };

        // أنماط التعبيرات النمطية للكشف عن الكيانات
        this.entityPatterns = {
            phone: /(\+?\d{1,4}[\s-]?)?\(?\d{1,4}\)?[\s-]?\d{1,4}[\s-]?\d{1,9}/g,
            email: /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,
            url: /(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?/g,
            price: /(\d+[\.,]?\d*)\s*(ريال|درهم|دينار|جنيه|دولار|يورو|ر\.س|د\.إ|ج\.م|$|€|£)/gi,
            numbers: /\d+/g,
            arabicNumbers: /[٠-٩]+/g,
            time: /(\d{1,2}):(\d{2})|(\d{1,2})\s*(صباحاً|مساءً|ص|م)/gi,
            date: /(\d{1,2})\/(\d{1,2})\/(\d{2,4})|(\d{1,2})-(\d{1,2})-(\d{2,4})/g
        };

        // إعداد Fuse.js للبحث الضبابي المتقدم
        this.fuseOptions = {
            includeScore: true,
            threshold: 0.3,
            location: 0,
            distance: 100,
            maxPatternLength: 32,
            minMatchCharLength: 2,
            keys: ['text']
        };

        // إعداد Natural.js
        this.stemmer = natural.PorterStemmerAr || natural.PorterStemmer;
        this.tokenizer = new natural.WordTokenizer();
        this.tfidf = new natural.TfIdf();
    }

    // تطبيع الأرقام العربية إلى إنجليزية
    normalizeNumbers(text) {
        const arabicNumbers = '٠١٢٣٤٥٦٧٨٩';
        const englishNumbers = '0123456789';
        
        let result = text;
        for (let i = 0; i < arabicNumbers.length; i++) {
            const arabicNum = arabicNumbers[i];
            const englishNum = englishNumbers[i];
            result = result.replace(new RegExp(arabicNum, 'g'), englishNum);
        }
        return result;
    }

    // تطبيع النص العربي المتقدم
    normalizeArabicText(text) {
        let normalized = text;
        
        // إزالة التشكيل
        normalized = normalized.replace(/[\u064B-\u0652]/g, '');
        
        // تطبيع الأحرف
        for (const [original, normalized_char] of Object.entries(this.arabicNormalization)) {
            normalized = normalized.replace(new RegExp(original, 'g'), normalized_char);
        }
        
        // توحيد المسافات
        normalized = normalized.replace(/\s+/g, ' ').trim();
        
        return normalized;
    }

    // استخراج الكيانات من النص
    extractEntities(text) {
        const entities = {};
        
        for (const [entityType, pattern] of Object.entries(this.entityPatterns)) {
            const matches = text.match(pattern);
            if (matches) {
                entities[entityType] = matches;
            }
        }
        
        return entities;
    }

    // استخراج الكلمات المفتاحية المتقدم
    extractAdvancedKeywords(text) {
        // تطبيع النص
        const normalizedText = this.normalizeArabicText(text);
        
        // استخدام compromise للتحليل النحوي
        const doc = compromise(normalizedText);
        
        // استخراج الأسماء والأفعال والصفات
        const nouns = doc.nouns().out('array');
        const verbs = doc.verbs().out('array');
        const adjectives = doc.adjectives().out('array');
        
        // دمج الكلمات المفتاحية
        let keywords = [...nouns, ...verbs, ...adjectives];
        
        // إضافة الكلمات العربية المهمة
        const arabicTokens = this.tokenizer.tokenize(normalizedText);
        const arabicKeywords = arabicTokens.filter(token => 
            token.length > 2 && 
            !this.isStopWord(token) &&
            /[\u0600-\u06FF]/.test(token)
        );
        
        keywords = [...keywords, ...arabicKeywords];
        
        // إزالة التكرار وتطبيق المرادفات
        keywords = [...new Set(keywords)];
        keywords = this.expandWithSynonyms(keywords);
        
        return keywords;
    }

    // فحص الكلمات الشائعة
    isStopWord(word) {
        const arabicStopWords = [
            'في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك',
            'التي', 'الذي', 'هو', 'هي', 'هم', 'هن', 'أنت', 'أنتم', 'أنتن', 'أنا', 'نحن',
            'كان', 'كانت', 'كانوا', 'كن', 'يكون', 'تكون', 'يكونوا', 'تكن',
            'لا', 'لم', 'لن', 'ما', 'لكن', 'غير', 'سوى', 'إلا', 'قد', 'كل', 'بعض'
        ];
        
        return arabicStopWords.includes(word.toLowerCase()) || 
               stopword.ar.includes(word.toLowerCase()) ||
               stopword.en.includes(word.toLowerCase());
    }

    // توسيع الكلمات المفتاحية بالمرادفات
    expandWithSynonyms(keywords) {
        const expandedKeywords = [...keywords];
        
        for (const keyword of keywords) {
            const lowerKeyword = keyword.toLowerCase();
            for (const [mainWord, synonyms] of Object.entries(this.advancedSynonyms)) {
                if (synonyms.includes(lowerKeyword) || mainWord === lowerKeyword) {
                    expandedKeywords.push(mainWord, ...synonyms);
                }
            }
        }
        
        return [...new Set(expandedKeywords)];
    }

    // معالجة شاملة للنص
    processText(text) {
        // تطبيع الأرقام
        let processed = this.normalizeNumbers(text);
        
        // تطبيع النص العربي
        processed = this.normalizeArabicText(processed);
        
        // تحويل إلى أحرف صغيرة
        processed = processed.toLowerCase();
        
        // إزالة علامات الترقيم الزائدة
        processed = processed.replace(/[^\w\s\u0600-\u06FF]/g, ' ');
        
        // توحيد المسافات
        processed = processed.replace(/\s+/g, ' ').trim();
        
        return processed;
    }
}

// ===== محلل الرسائل المتقدم مع الذكاء الاصطناعي =====
class ModernMessageAnalyzer {
    constructor() {
        this.textProcessor = new ModernAITextProcessor();
        
        // أنماط تصنيف الرسائل
        this.messagePatterns = {
            greeting: /^(مرحبا|أهلا|هلا|السلام عليكم|صباح الخير|مساء الخير|hello|hi|hey)/i,
            question: /^(كيف|متى|أين|ماذا|لماذا|من|what|when|where|why|who|how)/i,
            request: /^(أريد|أبغى|أطلب|أحتاج|بدي|want|need|i want|i need)/i,
            complaint: /^(مشكلة|شكوى|عطل|خلل|problem|issue|complaint)/i,
            thanks: /^(شكرا|شكراً|تسلم|يعطيك العافية|thanks|thank you)/i,
            price_inquiry: /^(كم|بكم|السعر|الثمن|التكلفة|price|cost|how much)/i
        };
        
        // كلمات المشاعر
        this.sentimentWords = {
            positive: ['ممتاز', 'رائع', 'جيد', 'حلو', 'زين', 'تمام', 'good', 'great', 'excellent', 'nice', 'awesome'],
            negative: ['سيء', 'وحش', 'مو زين', 'مش حلو', 'bad', 'terrible', 'awful', 'horrible', 'worst'],
            neutral: ['عادي', 'مقبول', 'okay', 'ok', 'fine', 'normal']
        };
    }

    // تحليل شامل للرسالة
    analyzeMessage(message) {
        const processed = this.textProcessor.processText(message);
        const keywords = this.textProcessor.extractAdvancedKeywords(message);
        const entities = this.textProcessor.extractEntities(message);
        
        return {
            original: message,
            processed: processed,
            keywords: keywords,
            entities: entities,
            type: this.detectMessageType(message),
            language: this.detectLanguage(message),
            sentiment: this.analyzeSentiment(message),
            intent: this.detectIntent(message),
            confidence: this.calculateConfidence(message, keywords)
        };
    }

    // كشف نوع الرسالة
    detectMessageType(message) {
        for (const [type, pattern] of Object.entries(this.messagePatterns)) {
            if (pattern.test(message)) {
                return type;
            }
        }
        return 'general';
    }

    // كشف اللغة
    detectLanguage(message) {
        const arabicChars = (message.match(/[\u0600-\u06FF]/g) || []).length;
        const englishChars = (message.match(/[a-zA-Z]/g) || []).length;
        const totalChars = arabicChars + englishChars;
        
        if (totalChars === 0) return 'unknown';
        
        const arabicRatio = arabicChars / totalChars;
        
        if (arabicRatio > 0.7) return 'arabic';
        if (arabicRatio < 0.3) return 'english';
        return 'mixed';
    }

    // تحليل المشاعر
    analyzeSentiment(message) {
        const lowerMessage = message.toLowerCase();
        let positiveScore = 0;
        let negativeScore = 0;
        
        for (const word of this.sentimentWords.positive) {
            if (lowerMessage.includes(word)) positiveScore++;
        }
        
        for (const word of this.sentimentWords.negative) {
            if (lowerMessage.includes(word)) negativeScore++;
        }
        
        if (positiveScore > negativeScore) return 'positive';
        if (negativeScore > positiveScore) return 'negative';
        return 'neutral';
    }

    // كشف النية
    detectIntent(message) {
        const lowerMessage = message.toLowerCase();
        
        if (this.messagePatterns.price_inquiry.test(message)) return 'price_inquiry';
        if (this.messagePatterns.request.test(message)) return 'purchase_intent';
        if (this.messagePatterns.complaint.test(message)) return 'support_needed';
        if (this.messagePatterns.greeting.test(message)) return 'social_interaction';
        if (this.messagePatterns.question.test(message)) return 'information_seeking';
        
        return 'general_inquiry';
    }

    // حساب مستوى الثقة
    calculateConfidence(message, keywords) {
        let confidence = 0.5; // قيمة أساسية
        
        // زيادة الثقة بناءً على طول الرسالة
        if (message.length > 10) confidence += 0.1;
        if (message.length > 30) confidence += 0.1;
        
        // زيادة الثقة بناءً على عدد الكلمات المفتاحية
        if (keywords.length > 3) confidence += 0.1;
        if (keywords.length > 6) confidence += 0.1;
        
        // زيادة الثقة إذا كانت الرسالة تحتوي على كيانات
        const entities = this.textProcessor.extractEntities(message);
        if (Object.keys(entities).length > 0) confidence += 0.2;
        
        return Math.min(confidence, 1.0);
    }
}

// ===== نظام المطابقة المتقدم مع الذكاء الاصطناعي الحديث =====
class ModernAIMatcher {
    constructor() {
        this.textProcessor = new ModernAITextProcessor();
        this.messageAnalyzer = new ModernMessageAnalyzer();

        // خوارزميات المطابقة المتقدمة
        this.algorithms = {
            // مطابقة دقيقة
            exact: (text1, text2) => text1 === text2 ? 1.0 : 0.0,

            // مطابقة جاكارد المحسنة
            jaccard: (text1, text2) => {
                const set1 = new Set(text1.split(' '));
                const set2 = new Set(text2.split(' '));
                const intersection = new Set([...set1].filter(x => set2.has(x)));
                const union = new Set([...set1, ...set2]);
                return union.size === 0 ? 0 : intersection.size / union.size;
            },

            // مطابقة كوساين
            cosine: (text1, text2) => {
                const words1 = text1.split(' ');
                const words2 = text2.split(' ');
                const allWords = [...new Set([...words1, ...words2])];

                const vector1 = allWords.map(word => words1.filter(w => w === word).length);
                const vector2 = allWords.map(word => words2.filter(w => w === word).length);

                const dotProduct = vector1.reduce((sum, val, i) => sum + val * vector2[i], 0);
                const magnitude1 = Math.sqrt(vector1.reduce((sum, val) => sum + val * val, 0));
                const magnitude2 = Math.sqrt(vector2.reduce((sum, val) => sum + val * val, 0));

                return magnitude1 && magnitude2 ? dotProduct / (magnitude1 * magnitude2) : 0;
            },

            // مطابقة ليفنشتاين المطورة
            levenshtein: (text1, text2) => {
                const maxLength = Math.max(text1.length, text2.length);
                if (maxLength === 0) return 1.0;

                const distance = natural.LevenshteinDistance(text1, text2);
                return 1 - (distance / maxLength);
            },

            // مطابقة الكلمات المفتاحية المتقدمة
            keywordMatch: (text1, text2) => {
                const keywords1 = this.textProcessor.extractAdvancedKeywords(text1);
                const keywords2 = this.textProcessor.extractAdvancedKeywords(text2);

                if (keywords1.length === 0 && keywords2.length === 0) return 1.0;
                if (keywords1.length === 0 || keywords2.length === 0) return 0.0;

                const commonKeywords = keywords1.filter(k => keywords2.includes(k));
                const totalKeywords = new Set([...keywords1, ...keywords2]).size;

                return commonKeywords.length / totalKeywords;
            },

            // مطابقة المرادفات الذكية
            synonymMatch: (text1, text2) => {
                const words1 = text1.split(' ');
                const words2 = text2.split(' ');
                let matches = 0;
                let totalComparisons = 0;

                for (const word1 of words1) {
                    for (const word2 of words2) {
                        totalComparisons++;
                        if (this.areSynonyms(word1, word2)) {
                            matches++;
                        }
                    }
                }

                return totalComparisons > 0 ? matches / totalComparisons : 0;
            },

            // مطابقة الكيانات
            entityMatch: (text1, text2) => {
                const entities1 = this.textProcessor.extractEntities(text1);
                const entities2 = this.textProcessor.extractEntities(text2);

                const types1 = Object.keys(entities1);
                const types2 = Object.keys(entities2);

                if (types1.length === 0 && types2.length === 0) return 0.5;
                if (types1.length === 0 || types2.length === 0) return 0.0;

                const commonTypes = types1.filter(type => types2.includes(type));
                return commonTypes.length / Math.max(types1.length, types2.length);
            }
        };

        // أوزان الخوارزميات (يمكن تعديلها ديناميكياً)
        this.weights = {
            exact: 0.25,
            jaccard: 0.15,
            cosine: 0.15,
            levenshtein: 0.10,
            keywordMatch: 0.20,
            synonymMatch: 0.10,
            entityMatch: 0.05
        };
    }

    // فحص المرادفات
    areSynonyms(word1, word2) {
        const lowerWord1 = word1.toLowerCase();
        const lowerWord2 = word2.toLowerCase();

        if (lowerWord1 === lowerWord2) return true;

        for (const [mainWord, synonyms] of Object.entries(this.textProcessor.advancedSynonyms)) {
            const allWords = [mainWord, ...synonyms].map(w => w.toLowerCase());
            if (allWords.includes(lowerWord1) && allWords.includes(lowerWord2)) {
                return true;
            }
        }

        return false;
    }

    // حساب التشابه المتقدم
    calculateAdvancedSimilarity(message, ruleText, messageAnalysis = null) {
        // تطبيع النصوص
        const processedMessage = this.textProcessor.processText(message);
        const processedRule = this.textProcessor.processText(ruleText);

        // حساب النتائج لكل خوارزمية
        const scores = {};
        for (const [algorithm, func] of Object.entries(this.algorithms)) {
            try {
                if (algorithm === 'keywordMatch' || algorithm === 'synonymMatch' || algorithm === 'entityMatch') {
                    scores[algorithm] = func.call(this, message, ruleText);
                } else {
                    scores[algorithm] = func(processedMessage, processedRule);
                }
            } catch (error) {
                console.warn(`Error in ${algorithm} algorithm:`, error);
                scores[algorithm] = 0;
            }
        }

        // تعديل الأوزان بناءً على تحليل الرسالة
        let adjustedWeights = { ...this.weights };
        if (messageAnalysis) {
            adjustedWeights = this.adjustWeights(adjustedWeights, messageAnalysis);
        }

        // حساب النتيجة النهائية المرجحة
        let finalScore = 0;
        let totalWeight = 0;

        for (const [algorithm, score] of Object.entries(scores)) {
            const weight = adjustedWeights[algorithm] || 0;
            finalScore += score * weight;
            totalWeight += weight;
        }

        const normalizedScore = totalWeight > 0 ? finalScore / totalWeight : 0;

        return {
            total: normalizedScore,
            breakdown: scores,
            weights: adjustedWeights,
            confidence: this.calculateMatchConfidence(scores, messageAnalysis)
        };
    }

    // تعديل الأوزان بناءً على تحليل الرسالة
    adjustWeights(weights, analysis) {
        const adjusted = { ...weights };

        // تعديل بناءً على نوع الرسالة
        switch (analysis.type) {
            case 'greeting':
                adjusted.exact += 0.1;
                adjusted.synonymMatch += 0.1;
                break;
            case 'question':
                adjusted.keywordMatch += 0.15;
                adjusted.entityMatch += 0.1;
                break;
            case 'request':
                adjusted.keywordMatch += 0.1;
                adjusted.cosine += 0.1;
                break;
            case 'complaint':
                adjusted.synonymMatch += 0.15;
                adjusted.keywordMatch += 0.1;
                break;
        }

        // تعديل بناءً على اللغة
        if (analysis.language === 'arabic') {
            adjusted.synonymMatch += 0.1;
            adjusted.levenshtein += 0.05;
        } else if (analysis.language === 'english') {
            adjusted.cosine += 0.1;
            adjusted.jaccard += 0.05;
        }

        // تطبيع الأوزان
        const totalWeight = Object.values(adjusted).reduce((sum, weight) => sum + weight, 0);
        for (const key in adjusted) {
            adjusted[key] = adjusted[key] / totalWeight;
        }

        return adjusted;
    }

    // حساب مستوى الثقة في المطابقة
    calculateMatchConfidence(scores, analysis) {
        const scoresArray = Object.values(scores);
        const avgScore = scoresArray.reduce((sum, score) => sum + score, 0) / scoresArray.length;
        const variance = scoresArray.reduce((sum, score) => sum + Math.pow(score - avgScore, 2), 0) / scoresArray.length;
        const consistency = 1 - Math.sqrt(variance);

        let confidence = (avgScore + consistency) / 2;

        // تعديل الثقة بناءً على تحليل الرسالة
        if (analysis) {
            confidence *= analysis.confidence;
        }

        return Math.min(Math.max(confidence, 0), 1);
    }

    // البحث عن أفضل مطابقة
    async findBestMatch(message, rules, threshold = 0.5) {
        if (!rules || rules.length === 0) {
            return null;
        }

        // تحليل الرسالة
        const messageAnalysis = this.messageAnalyzer.analyzeMessage(message);

        let bestMatch = null;
        let bestScore = 0;
        const allResults = [];

        console.log(`🤖 Modern AI Analysis: Processing "${message}" against ${rules.length} rules`);
        console.log(`📊 Message Analysis:`, {
            type: messageAnalysis.type,
            language: messageAnalysis.language,
            sentiment: messageAnalysis.sentiment,
            intent: messageAnalysis.intent,
            confidence: messageAnalysis.confidence.toFixed(3)
        });

        for (const rule of rules) {
            if (!rule.enabled || !rule.messages || !rule.messages.text) {
                continue;
            }

            const similarity = this.calculateAdvancedSimilarity(message, rule.messages.text, messageAnalysis);

            allResults.push({
                rule: rule,
                score: similarity.total,
                breakdown: similarity.breakdown,
                confidence: similarity.confidence
            });

            console.log(`🔍 Rule "${rule.code}": Score=${(similarity.total * 100).toFixed(1)}%, Confidence=${(similarity.confidence * 100).toFixed(1)}%`);

            if (similarity.total > bestScore && similarity.total >= threshold) {
                bestScore = similarity.total;
                bestMatch = {
                    rule: rule,
                    score: similarity.total,
                    breakdown: similarity.breakdown,
                    confidence: similarity.confidence,
                    messageAnalysis: messageAnalysis
                };
            }
        }

        // ترتيب النتائج حسب النتيجة
        allResults.sort((a, b) => b.score - a.score);

        console.log(`🎯 Best match: ${bestMatch ? `"${bestMatch.rule.code}" (${(bestMatch.score * 100).toFixed(1)}%)` : 'None above threshold'}`);

        return bestMatch;
    }
}

module.exports = {
    ModernAITextProcessor,
    ModernMessageAnalyzer,
    ModernAIMatcher
};
