{"name": "@tensorflow/tfjs-node", "version": "4.22.0", "main": "dist/index.js", "types": "dist/index.d.ts", "gypfile": true, "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs.git", "directory": "tfjs-node"}, "license": "Apache-2.0", "engines": {"node": ">=8.11.0"}, "scripts": {"build": "tsc && npx mkdirp dist/proto && cp src/proto/api_pb.js dist/proto/api_pb.js", "build-ci": "tsc && npx mkdirp dist/proto && cp src/proto/api_pb.js dist/proto/api_pb.js", "build-link-package": "cd ../link-package && yarn build-deps-for tfjs-node", "build-union": "cd ../tfjs && yarn && yarn build", "build-union-ci": "cd ../tfjs && yarn && yarn build-ci", "build-deps": "yarn build-link-package && yarn build-union", "build-deps-ci": "yarn build-link-package && yarn build-union-ci", "build-npm": "./scripts/build-npm.sh", "build-and-upload-addon": "./scripts/build-and-upload-addon.sh", "build-addon-from-source": "node-pre-gyp install --build-from-source", "clean-deps": "rm -rf deps && rm -rf lib", "coverage": "nyc yarn ts-node -P tsconfig.test.json src/run_tests.ts", "enable-gpu": "node scripts/install.js gpu download && yarn && yarn build-addon-from-source", "ensure-cpu-gpu-packages-align": "node scripts/ensure-cpu-gpu-packages-align.js", "format": "clang-format -i -style=Google binding/*.cc binding/*.h", "install": "node scripts/install.js", "install-from-source": "yarn clean-deps && yarn && yarn build-addon-from-source", "link-local": "yalc link", "lint": "tslint -p . -t verbose", "prep": "cd node_modules/@tensorflow/tfjs-core && yarn && yarn build", "publish-local": "yarn prep && yalc push", "publish-npm": "yarn build-and-upload-addon publish && npm publish", "test": "yarn && yarn build-deps && yarn build && ts-node --transpile-only --skip-ignore -P tsconfig.test.json src/run_tests.ts", "test-dev": "tsc && ts-node --transpile-only --skip-ignore -P tsconfig.test.json src/run_tests.ts", "test-ci": "ts-node --transpile-only --skip-ignore -P tsconfig.test.json src/run_tests.ts", "upload-windows-addon": "./scripts/build-and-upload-windows-addon.bat"}, "devDependencies": {"@tensorflow/tfjs-core": "4.22.0", "@types/jasmine": "~4.0.3", "@types/node": "^10.5.1", "@types/progress": "^2.0.1", "@types/rimraf": "~2.0.2", "@types/yargs": "^13.0.3", "clang-format": "~1.8.0", "jasmine": "~4.2.1", "node-fetch": "~2.6.1", "nyc": "^15.1.0", "tmp": "^0.0.33", "ts-node": "~8.8.2", "tslint": "~6.1.3", "tslint-no-circular-imports": "^0.7.0", "typescript": "5.0.4", "yalc": "~1.0.0-pre.50", "yargs": "^16.2.0"}, "dependencies": {"@mapbox/node-pre-gyp": "1.0.9", "@tensorflow/tfjs": "4.22.0", "adm-zip": "^0.5.2", "google-protobuf": "^3.9.2", "https-proxy-agent": "^2.2.1", "progress": "^2.0.0", "rimraf": "^2.6.2", "tar": "^6.2.1"}, "binary": {"module_name": "tfjs_binding", "module_path": "./lib/napi-v{napi_build_version}", "host": "https://storage.googleapis.com/tf-builds/pre-built-binary", "remote_path": "./napi-v{napi_build_version}/{version}/", "napi_versions": [3, 4, 5, 6, 7, 8]}}