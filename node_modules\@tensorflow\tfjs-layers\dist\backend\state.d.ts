/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-layers/dist/backend/state" />
export declare function getNextUniqueTensorId(): number;
/**
 * Provides a unique UID given a string prefix.
 *
 * @param prefix
 */
export declare function getUid(prefix?: string): string;
