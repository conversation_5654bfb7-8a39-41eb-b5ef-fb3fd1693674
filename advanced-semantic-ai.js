// نظام الذكاء الاصطناعي المتقدم للتطابق الدلالي - أحدث التقنيات 2024
// يستخدم Vector Embeddings و Cosine Similarity للحصول على أفضل النتائج

class AdvancedSemanticAI {
    constructor() {
        // نظام Vector Embeddings مبسط للكلمات العربية
        this.wordVectors = new Map();
        
        // المفاهيم الأساسية مع أوزان محددة
        this.semanticConcepts = {
            // العضوية والاشتراكات - وزن عالي
            membership: {
                core: ['عضوية', 'اشتراك', 'استراك', 'عضو', 'انضمام', 'تسجيل'],
                related: ['تجديد', 'انتهاء', 'استحقاق', 'انتساب'],
                weight: 3.0
            },
            
            // التدريب والرياضة - وزن عالي
            training: {
                core: ['تدريب', 'تدريبات', 'تمرين', 'تمارين', 'رياضة', 'لياقة'],
                related: ['جيم', 'نادي', 'صالة', 'قوة', 'كارديو', 'مدرب'],
                weight: 3.0
            },
            
            // المواعيد والجداول - وزن عالي
            schedule: {
                core: ['موعد', 'مواعيد', 'جدول', 'وقت', 'أوقات', 'توقيت'],
                related: ['برنامج', 'زمن', 'ساعة', 'يوم', 'صباح', 'مساء'],
                weight: 3.0
            },
            
            // الأسعار والتكاليف - وزن عالي
            pricing: {
                core: ['سعر', 'أسعار', 'تكلفة', 'مبلغ', 'رسوم', 'قيمة'],
                related: ['ريال', 'دفع', 'مال', 'فلوس', 'ثمن', 'قائمة'],
                weight: 3.0
            },
            
            // الحجز والطلبات - وزن عالي
            booking: {
                core: ['حجز', 'احجز', 'حجوزات', 'طلب', 'تأكيد'],
                related: ['ترتيب', 'تنظيم', 'موعد', 'تطبيق', 'هاتف'],
                weight: 3.0
            },
            
            // الإلغاء والتغيير - وزن عالي
            cancellation: {
                core: ['إلغاء', 'الغاء', 'ألغي', 'تغيير', 'تعديل'],
                related: ['تأجيل', 'إيقاف', 'سياسة', 'مجانا', 'متأخر'],
                weight: 3.0
            },
            
            // الخدمات - وزن عالي
            services: {
                core: ['خدمة', 'خدمات', 'عرض', 'عروض', 'متوفر', 'متاح'],
                related: ['باقة', 'برنامج', 'استشارة', 'تشمل', 'نوفر'],
                weight: 3.0
            },

            // الاستفسارات - وزن متوسط
            inquiry: {
                core: ['سؤال', 'استفسار', 'معلومات', 'ما', 'ماذا', 'كيف'],
                related: ['تفاصيل', 'شرح', 'توضيح', 'هي', 'هو'],
                weight: 2.0
            }
        };
        
        // كلمات الاستفهام والطلب
        this.questionIndicators = ['كيف', 'ماذا', 'متى', 'أين', 'هل', 'ما', 'من', 'لماذا'];
        this.requestIndicators = ['أريد', 'أطلب', 'أحتاج', 'أرغب', 'أود', 'ممكن'];

        // كلمات استبعاد للسياق الخاطئ
        this.contextExclusions = {
            food: ['بيتزا', 'طعام', 'أكل', 'وجبة', 'مطعم', 'طبخ'],
            weather: ['طقس', 'جو', 'مطر', 'شمس', 'حرارة', 'برد'],
            general: ['عام', 'عامة', 'شامل', 'كل', 'جميع']
        };
        
        // تهيئة نظام Vector Embeddings
        this.initializeVectorSystem();
    }
    
    // تهيئة نظام Vector Embeddings
    initializeVectorSystem() {
        // إنشاء vectors للكلمات الأساسية
        Object.keys(this.semanticConcepts).forEach(concept => {
            const conceptData = this.semanticConcepts[concept];
            
            // إنشاء vector للكلمات الأساسية
            conceptData.core.forEach(word => {
                this.wordVectors.set(word, this.createWordVector(word, concept, 1.0));
            });
            
            // إنشاء vector للكلمات المرتبطة
            conceptData.related.forEach(word => {
                this.wordVectors.set(word, this.createWordVector(word, concept, 0.7));
            });
        });
    }
    
    // إنشاء vector للكلمة
    createWordVector(word, concept, strength) {
        const vector = new Array(8).fill(0); // 8 أبعاد للمفاهيم الـ8
        const conceptIndex = Object.keys(this.semanticConcepts).indexOf(concept);
        if (conceptIndex !== -1) {
            vector[conceptIndex] = strength;
        }
        return vector;
    }
    
    // تنظيف النص العربي
    cleanArabicText(text) {
        if (!text) return '';
        
        return text
            .replace(/[ًٌٍَُِّْ]/g, '') // إزالة التشكيل
            .replace(/[؟!،.]/g, '') // إزالة علامات الترقيم
            .replace(/\s+/g, ' ') // توحيد المسافات
            .trim()
            .toLowerCase();
    }
    
    // تحليل النص وإنشاء vector دلالي
    createSemanticVector(text) {
        const cleanText = this.cleanArabicText(text);
        const words = cleanText.split(' ').filter(word => word.length > 1);
        
        // إنشاء vector مجمع للنص
        const semanticVector = new Array(8).fill(0);
        let totalWeight = 0;
        
        words.forEach(word => {
            if (this.wordVectors.has(word)) {
                const wordVector = this.wordVectors.get(word);
                for (let i = 0; i < semanticVector.length; i++) {
                    semanticVector[i] += wordVector[i];
                    totalWeight += wordVector[i];
                }
            }
        });
        
        // تطبيع Vector
        if (totalWeight > 0) {
            for (let i = 0; i < semanticVector.length; i++) {
                semanticVector[i] /= totalWeight;
            }
        }
        
        return semanticVector;
    }
    
    // حساب Cosine Similarity بين vectorين
    calculateCosineSimilarity(vector1, vector2) {
        if (vector1.length !== vector2.length) return 0;
        
        let dotProduct = 0;
        let magnitude1 = 0;
        let magnitude2 = 0;
        
        for (let i = 0; i < vector1.length; i++) {
            dotProduct += vector1[i] * vector2[i];
            magnitude1 += vector1[i] * vector1[i];
            magnitude2 += vector2[i] * vector2[i];
        }
        
        magnitude1 = Math.sqrt(magnitude1);
        magnitude2 = Math.sqrt(magnitude2);
        
        if (magnitude1 === 0 || magnitude2 === 0) return 0;
        
        return dotProduct / (magnitude1 * magnitude2);
    }
    
    // فحص السياق المستبعد
    hasExcludedContext(text) {
        const cleanText = this.cleanArabicText(text);
        const words = cleanText.split(' ');

        for (const category in this.contextExclusions) {
            const excludedWords = this.contextExclusions[category];
            for (const word of words) {
                if (excludedWords.includes(word)) {
                    return { excluded: true, category, word };
                }
            }
        }

        return { excluded: false };
    }

    // كشف المفهوم الرئيسي في النص
    detectPrimaryConcept(text) {
        const cleanText = this.cleanArabicText(text);
        const words = cleanText.split(' ');

        // فحص السياق المستبعد أولاً
        const exclusionCheck = this.hasExcludedContext(text);
        if (exclusionCheck.excluded) {
            return { concept: 'excluded', score: 0, reason: exclusionCheck };
        }

        const conceptScores = {};
        Object.keys(this.semanticConcepts).forEach(concept => {
            conceptScores[concept] = 0;
        });

        words.forEach(word => {
            Object.keys(this.semanticConcepts).forEach(concept => {
                const conceptData = this.semanticConcepts[concept];

                // البحث في الكلمات الأساسية
                if (conceptData.core.includes(word)) {
                    conceptScores[concept] += conceptData.weight * 1.0;
                }

                // البحث في الكلمات المرتبطة
                if (conceptData.related.includes(word)) {
                    conceptScores[concept] += conceptData.weight * 0.7;
                }
            });
        });

        // العثور على أعلى نتيجة
        let maxScore = 0;
        let primaryConcept = 'inquiry';

        Object.keys(conceptScores).forEach(concept => {
            if (conceptScores[concept] > maxScore) {
                maxScore = conceptScores[concept];
                primaryConcept = concept;
            }
        });

        return { concept: primaryConcept, score: maxScore };
    }
    
    // الدالة الرئيسية لحساب التطابق الدلالي
    calculateSemanticSimilarity(messageText, responseText) {
        try {
            // كشف المفاهيم الرئيسية أولاً
            const messageConcept = this.detectPrimaryConcept(messageText);
            const responseConcept = this.detectPrimaryConcept(responseText);

            // إذا كانت الرسالة مستبعدة، إرجاع 0
            if (messageConcept.concept === 'excluded') {
                console.log(`🚫 رسالة مستبعدة: "${messageText}"`);
                console.log(`   السبب: ${messageConcept.reason.category} - كلمة "${messageConcept.reason.word}"`);
                return 0;
            }

            // إنشاء vectors للنصين
            const messageVector = this.createSemanticVector(messageText);
            const responseVector = this.createSemanticVector(responseText);

            // حساب Cosine Similarity
            const cosineSim = this.calculateCosineSimilarity(messageVector, responseVector);

            // حساب تطابق المفاهيم
            const conceptMatch = messageConcept.concept === responseConcept.concept ? 1.0 : 0.0;

            // حساب النتيجة النهائية مع تعديل الأوزان
            let finalScore = (cosineSim * 0.6) + (conceptMatch * 0.4);

            // تعزيز النتيجة للمطابقات الممتازة
            if (conceptMatch === 1.0 && cosineSim > 0.8) {
                finalScore = Math.min(finalScore * 1.1, 1.0);
            }

            // تسجيل تفصيلي للتحليل
            console.log(`🔍 تحليل دلالي متقدم:`);
            console.log(`   📨 الرسالة: "${messageText}"`);
            console.log(`   💬 الرد: "${responseText.substring(0, 50)}..."`);
            console.log(`   🧮 Cosine Similarity: ${(cosineSim * 100).toFixed(2)}%`);
            console.log(`   🎯 مفهوم الرسالة: ${messageConcept.concept} (${messageConcept.score.toFixed(2)})`);
            console.log(`   🎯 مفهوم الرد: ${responseConcept.concept} (${responseConcept.score.toFixed(2)})`);
            console.log(`   ✅ تطابق المفاهيم: ${conceptMatch === 1.0 ? 'نعم' : 'لا'}`);
            console.log(`   📊 النتيجة النهائية: ${(finalScore * 100).toFixed(2)}%`);

            return finalScore;

        } catch (error) {
            console.error('❌ خطأ في حساب التطابق الدلالي:', error);
            return 0;
        }
    }
}

module.exports = AdvancedSemanticAI;
