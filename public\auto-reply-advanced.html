<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الرد التلقائي المتقدم - WhatsApp Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: '<PERSON>goe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .main-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #25D366, #128C7E);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .card-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .card-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .toolbar {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .btn-whatsapp {
            background: linear-gradient(135deg, #25D366, #128C7E);
            border: none;
            color: white;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-whatsapp:hover {
            background: linear-gradient(135deg, #128C7E, #25D366);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(37, 211, 102, 0.4);
        }

        .table-container {
            padding: 30px;
            overflow-x: auto;
        }

        .advanced-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .advanced-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 12px;
            text-align: center;
            font-weight: 600;
            border: none;
        }

        .advanced-table td {
            padding: 15px 12px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
            text-align: center;
        }

        .advanced-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .code-cell {
            font-weight: bold;
            color: #495057;
            background: #e9ecef;
            border-radius: 8px;
            padding: 8px 12px;
            display: inline-block;
            min-width: 60px;
        }

        .message-preview {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .file-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            background: #e3f2fd;
            border-radius: 12px;
            font-size: 0.85rem;
            color: #1976d2;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.875rem;
            border-radius: 6px;
        }

        .status-toggle {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 30px;
            background: white;
            border-bottom: 1px solid #dee2e6;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #25D366;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .toolbar > div {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-card">
            <!-- Header -->
            <div class="card-header">
                <h1><i class="fas fa-robot me-3"></i><span id="pageTitle">الرد التلقائي المتقدم</span></h1>
                <p id="pageSubtitle">نظام ذكي للرد التلقائي مع تحليل الرسائل والمطابقة الدقيقة</p>
            </div>

            <!-- Status Toggle -->
            <div class="status-toggle">
                <div>
                    <h4 class="mb-1">حالة النظام المتقدم</h4>
                    <p class="text-muted mb-0">تفعيل أو إلغاء تفعيل نظام الرد التلقائي المتقدم</p>
                </div>
                <label class="switch">
                    <input type="checkbox" id="advancedAutoReplyToggle">
                    <span class="slider"></span>
                </label>
            </div>

            <!-- Toolbar -->
            <div class="toolbar">
                <div>
                    <button class="btn btn-whatsapp" id="addRuleBtn">
                        <i class="fas fa-plus me-2"></i>إضافة قاعدة جديدة
                    </button>
                    <button class="btn btn-outline-primary" id="importExcelBtn">
                        <i class="fas fa-file-excel me-2"></i>استيراد من Excel
                    </button>
                    <button class="btn btn-outline-success" id="exportJsonBtn">
                        <i class="fas fa-download me-2"></i>تصدير JSON
                    </button>
                </div>
                <div>
                    <button class="btn btn-outline-secondary" id="backBtn">
                        <i class="fas fa-arrow-right me-2"></i>العودة
                    </button>
                    <a href="advanced-auto-reply-guide.html" target="_blank" class="btn btn-outline-warning">
                        <i class="fas fa-question-circle me-2"></i>دليل الاستخدام
                    </a>
                    <button class="btn btn-outline-info" id="testSystemBtn">
                        <i class="fas fa-flask me-2"></i>اختبار النظام
                    </button>
                </div>
            </div>

            <!-- Table Container -->
            <div class="table-container">
                <div id="rulesTableContainer">
                    <!-- Table will be generated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="auto-reply-advanced.js"></script>
</body>
</html>
