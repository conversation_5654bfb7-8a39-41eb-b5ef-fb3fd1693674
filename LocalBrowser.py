import sys
import os
import subprocess
import signal
import time
import threading
import requests
from PyQt5.QtWidgets import QApplication, QMainWindow, QMessageBox, QVBoxLayout, QWidget, QLabel
from PyQt5.QtWebEngineWidgets import QWebEngineView, QWebEnginePage, QWebEngineProfile
from PyQt5.QtCore import Qt, QUrl, pyqtSignal, QTimer, QObject
from PyQt5.QtGui import QIcon, QPixmap, QKeySequence

# تم إزالة كلاسات الحماية المفرطة لتبسيط المتصفح

class WhatsAppBrowser(QMainWindow):
    """المتصفح المدمج الحصري لتطبيق WhatsApp"""

    # إشارات للتواصل مع السيرفر
    server_started = pyqtSignal()
    server_stopped = pyqtSignal()

    def __init__(self):
        super().__init__()

        # متغيرات النظام
        self.server_process = None
        self.server_port = 3045
        self.server_url = f"http://localhost:{self.server_port}"
        self.project_path = os.path.dirname(os.path.abspath(__file__))

        # إعداد واجهة المتصفح
        self.setup_ui()

        # بدء السيرفر تلقائياً
        self.start_server()

        # إعداد مراقب السيرفر
        self.setup_server_monitor()

        # تحميل الصفحة الرئيسية
        self.load_main_page()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("WhatsApp Node - المتصفح المدمج")
        self.setWindowIcon(QIcon(os.path.join(self.project_path, "public", "favicon.ico")))
        self.showMaximized()

        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # تخطيط عمودي
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # شريط الحالة
        self.status_label = QLabel("🔄 جاري بدء السيرفر...")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #25D366;
                color: white;
                padding: 8px;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        layout.addWidget(self.status_label)

        # إعداد المتصفح
        self.setup_browser()
        layout.addWidget(self.browser)

    def load_main_page(self):
        """تحميل الصفحة الرئيسية مع رسالة اتصال محسنة"""
        loading_html = """
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>جاري الاتصال...</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }

                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                }

                .loading-container {
                    text-align: center;
                    background: rgba(255, 255, 255, 0.1);
                    padding: 30px;
                    border-radius: 15px;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                    max-width: 350px;
                    width: 90%;
                }

                .logo {
                    font-size: 40px;
                    margin-bottom: 15px;
                    animation: pulse 2s infinite;
                }

                .title {
                    font-size: 20px;
                    font-weight: 600;
                    margin-bottom: 10px;
                    color: #ffffff;
                }

                .message {
                    font-size: 14px;
                    margin-bottom: 20px;
                    color: rgba(255, 255, 255, 0.8);
                }

                .spinner {
                    width: 30px;
                    height: 30px;
                    border: 3px solid rgba(255, 255, 255, 0.3);
                    border-top: 3px solid #ffffff;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 15px;
                }

                .status {
                    font-size: 12px;
                    color: rgba(255, 255, 255, 0.7);
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }

                @keyframes pulse {
                    0%, 100% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                }
            </style>
        </head>
        <body>
            <div class="loading-container">
                <div class="logo">💬</div>
                <div class="title">WhatsApp Node</div>
                <div class="message">جاري الاتصال بالسيرفر</div>
                <div class="spinner"></div>
                <div class="status">يرجى الانتظار...</div>
            </div>

            <script>
                function checkServer() {
                    fetch('http://localhost:3045/')
                        .then(response => {
                            if (response.ok) {
                                window.location.href = 'http://localhost:3045/';
                            }
                        })
                        .catch(() => {
                            setTimeout(checkServer, 2000);
                        });
                }

                setTimeout(checkServer, 1000);
            </script>
        </body>
        </html>
        """

        self.browser.setHtml(loading_html)

    def setup_browser(self):
        """إعداد محرك المتصفح البسيط"""
        # إنشاء ملف تعريف مخصص للمتصفح المدمج
        self.profile = QWebEngineProfile("WhatsAppNodeProfile")

        # إعدادات التخزين المؤقت
        self.profile.setHttpCacheType(QWebEngineProfile.MemoryHttpCache)
        self.profile.setHttpCacheMaximumSize(50 * 1024 * 1024)  # 50MB

        # إعدادات الكوكيز
        self.profile.setPersistentCookiesPolicy(QWebEngineProfile.AllowPersistentCookies)

        # User-Agent مخصص للتعرف على المتصفح المدمج
        custom_user_agent = "Mozilla/5.0 (WhatsApp-Integrated-Browser) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        self.profile.setHttpUserAgent(custom_user_agent)

        # إنشاء صفحة عادية (بدون حماية مفرطة)
        self.page = QWebEnginePage(self.profile)

        # إنشاء عرض المتصفح
        self.browser = QWebEngineView()
        self.browser.setPage(self.page)

        # إعدادات بسيطة للمتصفح
        # (تم إزالة الحماية المفرطة)

        # إعداد إشارات التحميل
        self.browser.loadStarted.connect(self.on_load_started)
        self.browser.loadProgress.connect(self.on_load_progress)
        self.browser.loadFinished.connect(self.on_load_finished)

    def on_load_started(self):
        """عند بدء تحميل الصفحة"""
        self.status_label.setText("🔄 جاري تحميل الصفحة...")
        self.status_label.setStyleSheet("QLabel { background-color: #f39c12; color: white; padding: 5px; font-weight: bold; }")

    def on_load_progress(self, progress):
        """أثناء تحميل الصفحة"""
        self.status_label.setText(f"🔄 جاري التحميل... {progress}%")

    def on_load_finished(self, success):
        """عند انتهاء تحميل الصفحة"""
        if success:
            self.status_label.setText("✅ تم تحميل الصفحة بنجاح")
            self.status_label.setStyleSheet("QLabel { background-color: #25D366; color: white; padding: 5px; font-weight: bold; }")
        else:
            self.status_label.setText("❌ فشل في تحميل الصفحة")
            self.status_label.setStyleSheet("QLabel { background-color: #e74c3c; color: white; padding: 5px; font-weight: bold; }")

    # تم إزالة دوال الحماية المفرطة

    def setup_server_monitor(self):
        """إعداد مراقب السيرفر"""
        self.server_timer = QTimer()
        self.server_timer.timeout.connect(self.check_server_status)
        self.server_timer.start(2000)  # فحص كل ثانيتين

    def start_server(self):
        """بدء سيرفر Node.js"""
        try:
            # التحقق من وجود ملف server.js
            server_file = os.path.join(self.project_path, "server.js")
            if not os.path.exists(server_file):
                self.show_error("ملف server.js غير موجود في مجلد المشروع")
                return

            # بدء السيرفر
            self.server_process = subprocess.Popen(
                ["node", "server.js"],
                cwd=self.project_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            self.status_label.setText("🚀 جاري بدء السيرفر...")
            self.status_label.setStyleSheet("QLabel { background-color: #f39c12; color: white; padding: 5px; font-weight: bold; }")

            # انتظار بدء السيرفر
            QTimer.singleShot(3000, self.load_application)

        except Exception as e:
            self.show_error(f"خطأ في بدء السيرفر: {str(e)}")

    def load_application(self):
        """تحميل التطبيق في المتصفح"""
        try:
            # التحقق من أن السيرفر يعمل
            response = requests.get(self.server_url, timeout=5)
            if response.status_code == 200:
                # تحميل الصفحة الرئيسية
                self.browser.setUrl(QUrl(self.server_url))
                self.status_label.setText("✅ السيرفر يعمل بنجاح")
                self.status_label.setStyleSheet("QLabel { background-color: #25D366; color: white; padding: 5px; font-weight: bold; }")
            else:
                self.show_error("السيرفر لا يستجيب بشكل صحيح")
        except requests.exceptions.RequestException:
            # إعادة المحاولة بعد ثانيتين
            QTimer.singleShot(2000, self.load_application)

    def check_server_status(self):
        """فحص حالة السيرفر"""
        try:
            response = requests.get(self.server_url, timeout=2)
            if response.status_code == 200:
                if "❌" in self.status_label.text():
                    self.status_label.setText("✅ السيرفر يعمل بنجاح")
                    self.status_label.setStyleSheet("QLabel { background-color: #25D366; color: white; padding: 5px; font-weight: bold; }")
        except requests.exceptions.RequestException:
            self.status_label.setText("❌ السيرفر متوقف")
            self.status_label.setStyleSheet("QLabel { background-color: #e74c3c; color: white; padding: 5px; font-weight: bold; }")
            # إعادة تشغيل السيرفر
            self.restart_server()

    def restart_server(self):
        """إعادة تشغيل السيرفر"""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
            except:
                if os.name == 'nt':
                    subprocess.run(["taskkill", "/F", "/PID", str(self.server_process.pid)],
                                 capture_output=True)
                else:
                    os.kill(self.server_process.pid, signal.SIGKILL)

        # بدء السيرفر مرة أخرى
        time.sleep(1)
        self.start_server()

    def show_error(self, message):
        """عرض رسالة خطأ"""
        QMessageBox.critical(self, "خطأ", message)
        self.status_label.setText(f"❌ خطأ: {message}")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #DC3545;
                color: white;
                padding: 8px;
                font-weight: bold;
                font-size: 14px;
            }
        """)

    def keyPressEvent(self, event):
        """تعطيل اختصارات لوحة المفاتيح الخطيرة"""
        # F12
        if event.key() == Qt.Key_F12:
            event.ignore()
            return

        # Ctrl+Shift+I (أدوات المطور)
        if (event.modifiers() == (Qt.ControlModifier | Qt.ShiftModifier) and
            event.key() == Qt.Key_I):
            event.ignore()
            return

        # Ctrl+Shift+C (فحص العنصر)
        if (event.modifiers() == (Qt.ControlModifier | Qt.ShiftModifier) and
            event.key() == Qt.Key_C):
            event.ignore()
            return

        # Ctrl+Shift+J (وحدة التحكم)
        if (event.modifiers() == (Qt.ControlModifier | Qt.ShiftModifier) and
            event.key() == Qt.Key_J):
            event.ignore()
            return

        # Ctrl+U (عرض المصدر)
        if (event.modifiers() == Qt.ControlModifier and event.key() == Qt.Key_U):
            event.ignore()
            return

        super().keyPressEvent(event)

    def closeEvent(self, event):
        """إغلاق التطبيق وإيقاف السيرفر"""
        # إيقاف مراقب السيرفر
        if hasattr(self, 'server_timer'):
            self.server_timer.stop()

        # إيقاف السيرفر
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
            except:
                if os.name == 'nt':
                    subprocess.run(["taskkill", "/F", "/PID", str(self.server_process.pid)],
                                 capture_output=True)
                else:
                    os.kill(self.server_process.pid, signal.SIGKILL)

        event.accept()

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)

    # إعداد التطبيق
    app.setApplicationName("WhatsApp Node Browser")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("WhatsApp Node")

    # إنشاء المتصفح
    browser = WhatsAppBrowser()
    browser.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
