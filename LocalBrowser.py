import sys
import os
import subprocess
import signal
import time
import threading
import requests
from PyQt5.QtWidgets import QApp<PERSON>, QMainWindow, QMessageBox, QVBoxLayout, QWidget, QLabel
from PyQt5.QtWebEngineWidgets import QWebEngineView, QWebEnginePage, QWebEngineProfile
from PyQt5.QtWebEngineCore import QWebEngineUrlRequestInterceptor
from PyQt5.QtCore import Qt, QUrl, pyqtSignal, QTimer, QObject
from PyQt5.QtGui import QIcon, QPixmap, QKeySequence
from PyQt5.QtWebChannel import QWebChannel

class SecurityInterceptor(QWebEngineUrlRequestInterceptor):
    """مُعترض الطلبات لحماية التطبيق من الوصول الخارجي"""

    def __init__(self, allowed_hosts=None):
        super().__init__()
        self.allowed_hosts = allowed_hosts or ['localhost', '127.0.0.1']

    def interceptRequest(self, info):
        url = info.requestUrl().toString()
        host = info.requestUrl().host()

        # السماح فقط بالمضيفين المحددين
        if host not in self.allowed_hosts:
            # منع الطلبات الخارجية غير المصرح بها
            if not url.startswith(('data:', 'blob:', 'chrome-extension:')):
                info.block(True)

class SecurePage(QWebEnginePage):
    """صفحة ويب آمنة مع حماية من أدوات المطور"""

    def __init__(self, profile, parent=None):
        super().__init__(profile, parent)

    def createWindow(self, window_type):
        # منع فتح نوافذ جديدة
        return None

    def javaScriptConsoleMessage(self, level, message, line_number, source_id):
        # تعطيل رسائل وحدة التحكم في JavaScript
        pass

class WhatsAppBrowser(QMainWindow):
    """المتصفح المدمج الحصري لتطبيق WhatsApp"""

    # إشارات للتواصل مع السيرفر
    server_started = pyqtSignal()
    server_stopped = pyqtSignal()

    def __init__(self):
        super().__init__()

        # متغيرات النظام
        self.server_process = None
        self.server_port = 3000
        self.server_url = f"http://localhost:{self.server_port}"
        self.project_path = os.path.dirname(os.path.abspath(__file__))

        # إعداد واجهة المتصفح
        self.setup_ui()

        # إعداد الحماية الأمنية
        self.setup_security()

        # بدء السيرفر تلقائياً
        self.start_server()

        # إعداد مراقب السيرفر
        self.setup_server_monitor()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("WhatsApp Node - المتصفح المدمج")
        self.setWindowIcon(QIcon(os.path.join(self.project_path, "public", "favicon.ico")))
        self.showMaximized()

        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # تخطيط عمودي
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # شريط الحالة
        self.status_label = QLabel("🔄 جاري بدء السيرفر...")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #25D366;
                color: white;
                padding: 8px;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        layout.addWidget(self.status_label)

        # إعداد المتصفح
        self.setup_browser()
        layout.addWidget(self.browser)

    def setup_browser(self):
        """إعداد محرك المتصفح مع الحماية الأمنية"""
        # إنشاء ملف تعريف مخصص
        self.profile = QWebEngineProfile.defaultProfile()

        # تعطيل التخزين المؤقت والكوكيز الخارجية
        self.profile.setHttpCacheType(QWebEngineProfile.NoCache)
        self.profile.setPersistentCookiesPolicy(QWebEngineProfile.NoPersistentCookies)

        # إعداد مُعترض الطلبات للحماية
        self.interceptor = SecurityInterceptor(['localhost', '127.0.0.1'])
        self.profile.setUrlRequestInterceptor(self.interceptor)

        # إنشاء صفحة آمنة
        self.secure_page = SecurePage(self.profile)

        # إنشاء عرض المتصفح
        self.browser = QWebEngineView()
        self.browser.setPage(self.secure_page)

        # تعطيل قائمة السياق (كليك يمين)
        self.browser.setContextMenuPolicy(Qt.NoContextMenu)

    def setup_security(self):
        """إعداد الحماية الأمنية الشاملة"""
        # تعطيل اختصارات لوحة المفاتيح الخطيرة
        self.setFocusPolicy(Qt.StrongFocus)

        # حقن JavaScript لتعطيل أدوات المطور
        security_js = """
        // تعطيل F12 وأدوات المطور
        document.addEventListener('keydown', function(e) {
            // F12
            if (e.keyCode === 123) {
                e.preventDefault();
                return false;
            }
            // Ctrl+Shift+I
            if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
                e.preventDefault();
                return false;
            }
            // Ctrl+Shift+C
            if (e.ctrlKey && e.shiftKey && e.keyCode === 67) {
                e.preventDefault();
                return false;
            }
            // Ctrl+Shift+J
            if (e.ctrlKey && e.shiftKey && e.keyCode === 74) {
                e.preventDefault();
                return false;
            }
            // Ctrl+U
            if (e.ctrlKey && e.keyCode === 85) {
                e.preventDefault();
                return false;
            }
        });

        // تعطيل كليك يمين
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            return false;
        });

        // تعطيل التحديد والنسخ
        document.addEventListener('selectstart', function(e) {
            e.preventDefault();
            return false;
        });

        // إخفاء وحدة التحكم
        if (typeof console !== 'undefined') {
            console.log = function() {};
            console.warn = function() {};
            console.error = function() {};
            console.info = function() {};
            console.debug = function() {};
        }
        """

        # حقن الكود الأمني عند تحميل الصفحة
        self.browser.page().loadFinished.connect(
            lambda: self.browser.page().runJavaScript(security_js)
        )

    def setup_server_monitor(self):
        """إعداد مراقب السيرفر"""
        self.server_timer = QTimer()
        self.server_timer.timeout.connect(self.check_server_status)
        self.server_timer.start(2000)  # فحص كل ثانيتين

    def start_server(self):
        """بدء سيرفر Node.js"""
        try:
            # التحقق من وجود ملف server.js
            server_file = os.path.join(self.project_path, "server.js")
            if not os.path.exists(server_file):
                self.show_error("ملف server.js غير موجود في مجلد المشروع")
                return

            # بدء السيرفر
            self.server_process = subprocess.Popen(
                ["node", "server.js"],
                cwd=self.project_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            self.status_label.setText("🔄 جاري بدء السيرفر...")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #FFA500;
                    color: white;
                    padding: 8px;
                    font-weight: bold;
                    font-size: 14px;
                }
            """)

            # انتظار بدء السيرفر
            QTimer.singleShot(3000, self.load_application)

        except Exception as e:
            self.show_error(f"خطأ في بدء السيرفر: {str(e)}")

    def load_application(self):
        """تحميل التطبيق في المتصفح"""
        try:
            # التحقق من أن السيرفر يعمل
            response = requests.get(self.server_url, timeout=5)
            if response.status_code == 200:
                # تحميل الصفحة الرئيسية
                self.browser.setUrl(QUrl(self.server_url))
                self.status_label.setText("✅ السيرفر يعمل - التطبيق جاهز")
                self.status_label.setStyleSheet("""
                    QLabel {
                        background-color: #25D366;
                        color: white;
                        padding: 8px;
                        font-weight: bold;
                        font-size: 14px;
                    }
                """)
            else:
                self.show_error("السيرفر لا يستجيب بشكل صحيح")
        except requests.exceptions.RequestException:
            # إعادة المحاولة بعد ثانيتين
            QTimer.singleShot(2000, self.load_application)

    def check_server_status(self):
        """فحص حالة السيرفر"""
        try:
            response = requests.get(self.server_url, timeout=2)
            if response.status_code == 200:
                if "❌" in self.status_label.text():
                    self.status_label.setText("✅ السيرفر يعمل - التطبيق جاهز")
                    self.status_label.setStyleSheet("""
                        QLabel {
                            background-color: #25D366;
                            color: white;
                            padding: 8px;
                            font-weight: bold;
                            font-size: 14px;
                        }
                    """)
        except requests.exceptions.RequestException:
            self.status_label.setText("❌ السيرفر متوقف - جاري إعادة التشغيل...")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #DC3545;
                    color: white;
                    padding: 8px;
                    font-weight: bold;
                    font-size: 14px;
                }
            """)
            # إعادة تشغيل السيرفر
            self.restart_server()

    def restart_server(self):
        """إعادة تشغيل السيرفر"""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
            except:
                if os.name == 'nt':
                    subprocess.run(["taskkill", "/F", "/PID", str(self.server_process.pid)],
                                 capture_output=True)
                else:
                    os.kill(self.server_process.pid, signal.SIGKILL)

        # بدء السيرفر مرة أخرى
        time.sleep(1)
        self.start_server()

    def show_error(self, message):
        """عرض رسالة خطأ"""
        QMessageBox.critical(self, "خطأ", message)
        self.status_label.setText(f"❌ خطأ: {message}")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #DC3545;
                color: white;
                padding: 8px;
                font-weight: bold;
                font-size: 14px;
            }
        """)

    def keyPressEvent(self, event):
        """تعطيل اختصارات لوحة المفاتيح الخطيرة"""
        # F12
        if event.key() == Qt.Key_F12:
            event.ignore()
            return

        # Ctrl+Shift+I (أدوات المطور)
        if (event.modifiers() == (Qt.ControlModifier | Qt.ShiftModifier) and
            event.key() == Qt.Key_I):
            event.ignore()
            return

        # Ctrl+Shift+C (فحص العنصر)
        if (event.modifiers() == (Qt.ControlModifier | Qt.ShiftModifier) and
            event.key() == Qt.Key_C):
            event.ignore()
            return

        # Ctrl+Shift+J (وحدة التحكم)
        if (event.modifiers() == (Qt.ControlModifier | Qt.ShiftModifier) and
            event.key() == Qt.Key_J):
            event.ignore()
            return

        # Ctrl+U (عرض المصدر)
        if (event.modifiers() == Qt.ControlModifier and event.key() == Qt.Key_U):
            event.ignore()
            return

        super().keyPressEvent(event)

    def closeEvent(self, event):
        """إغلاق التطبيق وإيقاف السيرفر"""
        # إيقاف مراقب السيرفر
        if hasattr(self, 'server_timer'):
            self.server_timer.stop()

        # إيقاف السيرفر
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
            except:
                if os.name == 'nt':
                    subprocess.run(["taskkill", "/F", "/PID", str(self.server_process.pid)],
                                 capture_output=True)
                else:
                    os.kill(self.server_process.pid, signal.SIGKILL)

        event.accept()

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)

    # إعداد التطبيق
    app.setApplicationName("WhatsApp Node Browser")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("WhatsApp Node")

    # إنشاء المتصفح
    browser = WhatsAppBrowser()
    browser.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
